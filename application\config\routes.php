<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'welcome';
$route['404_override'] = 'Page_not_found';
$route['504_override'] = 'error_504';
$route['translate_uri_dashes'] = FALSE;

// Modules Hub
$route['modules'] = 'ModulesController/index';
$route['modules/toggle_pin'] = 'ModulesController/toggle_pin';
$route['modules/track_usage'] = 'ModulesController/track_usage';
$route['modules/get_pinned'] = 'ModulesController/get_pinned';
$route['modules/get_frequent'] = 'ModulesController/get_frequent';
$route['modules/search_staff'] = 'ModulesController/search_staff';
$route['modules/search_students'] = 'ModulesController/search_students';
//Attendance Routes


$route['attendance'] = 'attendance/Attendance_controller';
$route['markAttendance'] = 'attendance/Attendance_controller/submitAttendanceRequest';
$route['attendance/(:num)/action'] = 'attendance/Action_controller/studentActions/$1';
$route['attendance/(:num)/createAction'] = 'attendance/Action_controller/addStudentActions/$1';
$route['attendance/(:num)/createActions'] = 'attendance/Action_controller/addStudentActionss/$1';
$route['attendance/filterIndex/(:num)'] = 'attendance/Action_controller/filterIndex/$1';
$route['attendance/saveAction'] = 'attendance/Action_controller/saveStudentActions';
$route['attendance/editAttendanceBy/AdmissionNumber'] = 'attendance/Attendance_controller/manuelInsertStudentAttendance';
$route['submitDeleteAttendanceRequest'] = 'attendance/Attendance_controller/submitDeleteAttendanceRequest';
$route['attendance/deleteAttendanceByClassSection'] = 'attendance/Attendance_controller/deleteAttendanceByClassSection';
$route['galleries/create_gallery'] = 'galleries/create_gallery';

$route['student_profile/(:any)'] = 'student/student_form/updateInfoPage/$1';
$route['update_profile/(:any)'] = 'student/student_form/updateForm/$1';

// admission forms

$route['admissions'] = 'admission_user/verifyMobileForm';
$route['admissions/home'] = 'Admission_user/saveAction';
$route['admissions/forms'] = 'admission_controller/form_templates';
$route['admissions/instructions'] = 'admission_controller/instruction';
// $route['admissions/my_application_forms'] = 'admission_controller/my_application';
$route['admissions/new'] = 'Admission_controller/start_admission_from';
$route['admissions/preview'] = 'admission_controller/preview_data';
$route['admissions/print'] = 'admission_controller/print_application';
$route['admissions/pay'] = 'admission_controller/pay';
$route['admissions/pay_redirect'] = 'admission_controller/admission_trans_done';
$route['admissions/print_receipt/(:num)'] = 'admission_controller/print_receipt/$1';
$route['admissions/short_application'] = 'admission_controller/preview_minimum_fields';
$route['payment'] = 'admission_controller/make_partial_payment';
$route['admissions/start_application'] = 'admission_controller/fill_partial_application';


// Enquiry
$route['enquiries'] = 'enquiry/users';
$route['enquiries/(:any)'] = 'enquiry/users/index/$1';
$route['enquiries/(:any)/(:any)'] = 'enquiry/users/index/$1/$2';
$route['enquiries-success/(:any)'] = 'enquiry/users/enquiry_form_message/$1';
$route['suy_enquiry_form'] = "suy/suy_controller/suy_manage_page";
$route['suy-success'] = 'suy/Suy_controller/suy_success_page';
$route['iisb_contact_form'] = 'enquiry/users/contact_us_iisb';
$route['iis_landing_page'] = 'enquiry/users/iisb_landing_form';
$route['iisb_contact_form_success'] = 'enquiry/users/enquiry_form_message_contact';
$route['iisb_landing_form_success'] = 'enquiry/users/enquiry_form_message_landing';

$route['itari_enquiry_form/(:any)'] = "itari/Itari_user/itari_enquiry_page/$1";
$route['itari_common_enquiry_form'] = "itari/Itari_user/itari_common_enquiry_page";
$route['itari-success/(:any)'] = 'itari/Itari_user/itari_success/$1';
$route['itari-common-success/(:any)'] = 'itari/Itari_user/itari_common_success/$1';


// Library books display
$route['library'] = 'library/Library_users';
$route['library/(:num)'] = 'library/library_users/load_books_page/$1';


$route['logs'] = "logViewerController/index";
$route['dblogs'] = "Db_Log_Controller/index";

//Online Payment routes
$route['op_recon_callback'] = 'payment_controller/recon_callback';
$route['op_s2s_callback'] = 'payment_controller/payment_done_s2s_fees';


//Push Nottification

$route['notificationRegistration'] = 'PushNotification_controller/addFcmToken';
$route['testNotification'] = 'PushNotification_controller/prepareNotification';



//Calender Routes


$route['calender'] = 'calender_events/Year_calender_controller';
$route['calender/create'] = 'calender_events/Year_calender_controller/create';


// Forgot Username
$route['forgot-password'] = 'auth/forgot_username_password_new';
$route['reset-password'] = 'auth/forgot_reset_password_new';
$route['multiple-accounts'] = 'auth/get_multi_account_details';
$route['success'] = 'auth/reset_password_success';

// Career page
$route['staff-recruitment'] = 'Staff_recruitment_users_controller';
$route['recruitment_success'] = 'Staff_recruitment_users_controller/thank_you_page';

//Itari Admissions
$route['itari_admissions'] = 'itari/Itari_Admission_Controller/verifyMobile';
$route['itari_admissions/home'] = 'itari/Itari_Admission_Controller/saveAction';
$route['itari_admissions/new_application'] = 'itari/Itari_Admission_Controller/testform';
$route['itari_admissions/my_application'] = 'itari/Itari_Admission_Controller/my_application';
$route['itari_admissions/saved_application'] = 'itari/Itari_Admission_Controller/my_application_data';
$route['itari_admissions/payment'] = 'itari/Itari_Admission_Controller/get_payment_link';
$route['itari_admissions/payment_failure'] = 'itari/Itari_Admission_Controller/payment_failure';
$route['itari_admissions/payment_success'] = 'itari/Itari_Admission_Controller/payment_success';

//QR code
$route['student_url_qr_code/(:any)'] = 'url_qr_codes/Url_qr_codes_controller/index/$1';

//staff recruitment

$route['careers_page'] = 'Staff_recruitment_controller_osu/introPage';


//ID Cards
$route['create'] = 'idcards/Idcards_controller/create_template';
$route['view'] = 'idcards/Idcards_controller/view_orders';
$route['idcards/view/(:num)'] = 'idcards/Idcards_controller/view_orders/$1';
$route['idcards'] = 'idcards/Idcards_controller/template_list';
$route['edit/(:num)'] = 'idcards/Idcards_controller/edit_template/$1';
$route['preview/(:num)'] = 'idcards/Idcards_controller/preview_template/$1';
$route['verify_data/(:num)'] = 'idcards/Idcards_controller/preview_generation/$1';
$route['download'] = 'idcards/Idcards_controller/downloadApprovedIDCards';
$route['idcards/process'] = 'idcards/ImageProcessor/processImages';


// Parent Dashboard V3
$route['parentdashboard'] = 'parent/Parent_Dashboard_Controller/index';
$route['info_message'] = 'parent/Parent_Dashboard_Controller/info_for_desktop_mobile';
$route['profile'] = 'parent/Parent_Dashboard_Controller/profile';
$route['parent/profile_detail'] = 'parent/Parent_Dashboard_Controller/profile_details';
$route['texts'] = 'parent/Parent_Dashboard_Controller/text';
$route['calendar'] = 'parent/Parent_Dashboard_Controller/calendar';
$route['circulars'] = 'parent/Parent_Dashboard_Controller/circular_inbox';


$route['parent/change_password'] = 'parent/Dashboard_controller/change_password';
$route['parent/help_support'] = 'parent/Dashboard_controller/help_support';
$route['parent/delete_account'] = 'parent/Dashboard_controller/delete_account';

// Parent Dashboard Feature Routes
$route['parent/upload'] = 'parent/Upload_controller/index';
$route['parent/transportation'] = 'parent/Transportation_controller/index';
$route['parent/circulars'] = 'parent/Circular_controller/index';
$route['parent/timetable'] = 'parent/Timetable_controller/index';
$route['parent/calendar'] = 'parent/Calendar_controller/index';
$route['parent/fees'] = 'parent/Fees_controller/index';
$route['parent/non_compliance'] = 'parent/Compliance_controller/index';
$route['parent/tasks'] = 'parent/Tasks_controller/index';
$route['parent/report_card'] = 'parent/Report_controller/index';
$route['parent/marks'] = 'parent/Marks_controller/index';
$route['parent/attendance'] = 'parent/Attendance_controller/index';
$route['parent/gallery'] = 'parent/Gallery_controller/index';
$route['parent/explore'] = 'parent/Explore_controller/index';
$route['parent/notifications'] = 'parent/Notification_controller/index';


