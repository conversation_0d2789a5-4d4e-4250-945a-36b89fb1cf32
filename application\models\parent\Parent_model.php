<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Parent_model extends CI_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function get_student_datails($student_id){
        $yearId =  $this->acad_year->getAcadYearId();
		$result =  $this->db_readonly->select("concat(sa.first_name,' ', ifnull(sa.last_name,'')) as student_name, concat(c.class_name,' ', ifnull(cs.section_name,'')) as class_section, cs.id as class_section_id, c.class_name, cs.section_name, sy.board, sy.picture_url,  IF(sy.profile_confirmed = 'No', 0, 1) as profile_confirmed, IF(sa.sibling_id IS NULL, 0, 1) as has_sibling, sy.id as stdYearId, ifnull(profile_status,'') as profile_status, profile_confirmed as profileConfirmed, date_format(profile_confirmed_date,'%d-%m-%Y') as profile_confirmed_date, date_format(profile_status_changed_date,'%d-%m-%Y') as profile_status_changed_date")
        ->from('student_admission sa')
        ->join('student_year sy','sa.id = sy.student_admission_id')
        ->join('class c','c.id = sy.class_id')
        ->join('class_section cs','cs.id = sy.class_section_id','left')
        ->where('sa.id',$student_id)
		->where('sy.acad_year_id',$yearId)
		->get()->row();
		if ($result) {
			$result->student_photo = base_url('assets/img/icons/profile.png');
			if (!empty($result->picture_url)) {
				$result->student_photo = $this->filemanager->getFilePath($result->picture_url);
			}
		}
		return $result;

    }

    /**
     * Get parent ID by student ID
     */
    public function getParentIdByStudentId($student_id) {
        $result = $this->db_readonly->select('p.id')
            ->from('parent p')
            ->where('p.student_id', $student_id)
            ->get()->row();

        return $result ? $result->id : 0;
    }

    /**
     * Get recent homework for dashboard
     */
    public function getRecentHomework($class_section_id, $limit = 5) {
        $this->db_readonly->select('h.id, h.body, h.created_date, h.expect_submissions');
        $this->db_readonly->from('homework h');
        $this->db_readonly->where('h.section_id', $class_section_id);
        $this->db_readonly->where('h.status', 1);
        $this->db_readonly->order_by('h.created_date', 'DESC');
        $this->db_readonly->limit($limit);

        return $this->db_readonly->get()->result();
    }

    /**
     * Get recent student tasks for dashboard
     */
    public function getRecentStudentTasks($student_id, $limit = 5) {
        $this->db_readonly->select('lts.*, lt.created_date, lts.read_status');
        $this->db_readonly->from('lp_tasks_students lts');
        $this->db_readonly->join('lp_tasks lt', 'lt.id = lts.lp_tasks_id');
        $this->db_readonly->where('lts.student_id', $student_id);
        $this->db_readonly->where('lt.status', 'published');
        $this->db_readonly->order_by('lt.created_date', 'DESC');
        $this->db_readonly->limit($limit);

        return $this->db_readonly->get()->result();
    }

    /**
     * Get weekly homework count for statistics
     */
    public function getWeeklyHomeworkCount($class_section_id) {
        $week_ago = date('Y-m-d', strtotime('-7 days'));
        $today = date('Y-m-d');

        $this->db_readonly->select('count(h.id) as hwCount');
        $this->db_readonly->from('homework h');
        $this->db_readonly->where('h.section_id', $class_section_id);
        $this->db_readonly->where('h.status', 1);
        $this->db_readonly->where('h.created_date >=', $week_ago);
        $this->db_readonly->where('h.created_date <=', $today);

        $result = $this->db_readonly->get()->row();
        return $result ? $result->hwCount : 0;
    }

    /**
     * Get student attendance percentage
     */
    public function getStudentAttendancePercentage($student_id) {
        // This is a simplified version - you might need to adjust based on your attendance system
        $this->db_readonly->select('
            COUNT(*) as total_days,
            SUM(CASE WHEN attendance_status = "Present" THEN 1 ELSE 0 END) as present_days
        ');
        $this->db_readonly->from('attendance_std_day_v2 asd');
        $this->db_readonly->where('asd.student_id', $student_id);
        $this->db_readonly->where('asd.att_taken_date >=', date('Y-m-d', strtotime('-30 days')));

        $result = $this->db_readonly->get()->row();

        if ($result && $result->total_days > 0) {
            $percentage = ($result->present_days / $result->total_days) * 100;
            return (object) ['percentage' => $percentage];
        }

        return null;
    }

    public function getStudentIdOfLoggedInParent(){
		$parentAvatarId = $this->authorization->getAvatarId();
		if (empty($parentAvatarId)) {
			log_message('error', 'parent_model->getStudentIdOfLoggedInParent: Invalid parent avatar ID');
			return 0;
		}

		//Get Student Id of the logged in parent
		$studentRecord = $this->db_readonly->select('sr.std_id as studentId')
			->from('student_relation sr')
			->join('parent p', 'sr.relation_id=p.id')
			->join('avatar a', 'a.stakeholder_id=p.id')
			->where('a.avatar_type', '2')
			->where('a.id', $parentAvatarId)
			->get()->row();

			if(!empty($studentRecord->studentId)){
				return $studentRecord->studentId;
			}else{
				log_message('error', 'parent_model->getStudentIdOfLoggedInParent: Student ID not found');
				return 0;
			}
	}

	public function getSiblingdata($currentStudentId){
        $this->yearId = $this->acad_year->getAcadYearId();
        $siblingCount = "select a.user_id,sa.id student_id from avatar a
        join parent p on a.stakeholder_id = p.id
        join student_admission sa on p.student_id = sa.id
        where avatar_type = 2 and sa.id = ?";
        $sibCountResult = $this->db->query($siblingCount, array($currentStudentId))->result();

        $userIds = [];
        foreach ($sibCountResult as $key => $value) {
            array_push($userIds, $value->user_id);
        }

        $siblingsid = [];
        if (!empty($userIds)) {
            $siblings = "select sa.id as student_id, p.student_id from avatar a
            join parent p on p.id = a.stakeholder_id
            join student_admission sa on p.student_id = sa.id
            where avatar_type = 2 and a.user_id IN (" . implode(',', array_fill(0, count($userIds), '?')) . ")
            group by p.student_id";
            $siblingsid = $this->db->query($siblings, $userIds)->result();
        }

        $student_ids = [];
        foreach ($siblingsid as $key => $value) {
            if ($value->student_id != $currentStudentId) {
                array_push($student_ids, $value->student_id);
            }
        }
        $pAvatarResult = [];
        if (count($student_ids) > 0) {
            $pAvatarResult = $this->db_readonly->select("sa.id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(c.class_name,' ', ifnull(cs.section_name,'')) as class_section, sy.picture_url")
                ->from('student_admission sa')
                ->join('student_year sy', ' sa.id=sy.student_admission_id')
                ->join('class c', 'sy.class_id=c.id')
                ->join('class_section cs', 'sy.class_section_id=cs.id','left')
                ->where_in('sa.id', $student_ids)
                ->where('sy.acad_year_id', $this->yearId)
                ->get()->result();
        }
        if ($pAvatarResult) {
            foreach ($pAvatarResult as $key => $result) {
                $result->student_photo = base_url('assets/img/icons/profile.png');
                if (!empty($result->picture_url)) {
                    $result->student_photo = $this->filemanager->getFilePath($result->picture_url);
                }
            }
        }
        return $pAvatarResult;
	}

    public function getStudentDataById($studentId, $tablColumn){

        // Get the latest academic year ID for the student in a single query
        $studentAcadYearId = $this->db_readonly
            ->select_max('acad_year_id')
            ->where('student_admission_id', $studentId)
            ->get('student_year')
            ->row('acad_year_id');

        if (!$studentAcadYearId) {
            return null;
        }

        // Build the select query for student-specific data only
        $selectColumns = "sa.id as stdId, sy.id as stdYearId, " .
                        "(CASE WHEN sy.promotion_status = 4 OR sy.promotion_status = 5 THEN sy.promotion_status ELSE sa.admission_status END) AS admission_status, " .
                        "cs.id as sectionId";

        if (!empty($tablColumn)) {
            $selectColumns .= ", " . $tablColumn;
        }

        $studentData = $this->db_readonly->select($selectColumns)
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id AND sy.acad_year_id='.$studentAcadYearId)
            ->join('class_master_combinations cmc','cmc.id=sy.combination_id','left')
            ->join('class c', 'sy.class_id=c.id', 'left')
            ->join('class_section cs', 'cs.id=sy.class_section_id', 'left')
            ->join('semester sem', 'sy.semester=sem.id', 'left')
            ->join('avatar a','sa.id=a.stakeholder_id AND a.avatar_type=1')
            ->join('users u','a.user_id=u.id')
            // Join father data
            ->join('student_relation sr_father', 'sa.id=sr_father.std_id AND sr_father.relation_type="Father"', 'left')
            ->join('parent pf', 'sr_father.relation_id=pf.id', 'left')
            // Join mother data
            ->join('student_relation sr_mother', 'sa.id=sr_mother.std_id AND sr_mother.relation_type="Mother"', 'left')
            ->join('parent pm', 'sr_mother.relation_id=pm.id', 'left')
            // Join guardian data
            ->join('student_relation sr_guardian', 'sa.id=sr_guardian.std_id AND sr_guardian.relation_type="Guardian"', 'left')
            ->join('parent pg', 'sr_guardian.relation_id=pg.id', 'left')
            ->where('sa.id', $studentId)
            ->get()->row();
        // Process student-specific data
        if ($studentData) {
            if(!empty($studentData->category)){
                $studentData->category = $this->settings->getSetting('category')[$studentData->category];
            }

            // Handle transport stops if needed
            if(isset($studentData->stop) && !empty($studentData->stop)){
                $studentData->transportStops = $this->db_readonly->select('*')->where('id',$studentData->stop)->get('feev2_stops')->result();
            } else {
                $studentData->transportStops = '';
            }
        }

        return $studentData;
    }

    public function getStudent_Address_Details($pId, $address){
		return $this->db_readonly->select("CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS student_address")
			->from('address_info')
			->where('address_type', $address)
			->where('stakeholder_id', $pId)
			->where('avatar_type','1')
			->get()->result();
	}
    public function getFather_Address_Details($pId, $address)
	{
		return $this->db_readonly->select(
			"CONCAT_WS(', ', 
				NULLIF(Address_line1, ''), 
				NULLIF(Address_line2, ''), 
				NULLIF(area, ''), 
				NULLIF(district, ''), 
				NULLIF(state, ''), 
				NULLIF(country, ''), 
				NULLIF(pin_code, '')
			) AS {$address}_address"
		)
		->from('address_info')
		->where_in('address_type', $address)
		->where('stakeholder_id', $pId)
		->where('avatar_type', '2')
		->get()
		->result();
	}



    public function getFatherDetails($studentId, $tablColumn)
	{
		return $this->__getRelationDetails($studentId, 'Father', $tablColumn);
	}

	public function getMotherDetails($studentId, $tablColumn)
	{
		return $this->__getRelationDetails($studentId, 'Mother', $tablColumn);
	}

    private function __getRelationDetails($studentId, $relation, $tablColumn)
	{
		$fatherData = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*, u.id as userId, p.email as fatherEmail")
			->from('parent p')
			->join('student_relation sr', 'p.id=sr.relation_id', 'left')
		  	->join('avatar a','p.id=a.stakeholder_id')
	        ->where('a.avatar_type','2')
	        ->join('users u','a.user_id=u.id')
			->where('sr.relation_type', $relation)
			->where('sr.std_id', $studentId)
			->get()->row();

		return $fatherData;
	}

    public function getGuardianDetails($studentId) {
		$guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*")
			->from('parent p')
			->join('student_relation sr', 'p.id=sr.relation_id', 'left')
			->where('sr.relation_type', 'Guardian')
			->where('sr.std_id', $studentId)
			->get()->row();

		return $guardian;
	}

	/**
	 * Get Father data by ID with specific columns
	 */
	public function getFatherDataById($studentId, $tablColumn = '') {
		if (empty($tablColumn)) {
			$tablColumn = "CONCAT(IFNULL(pf.first_name,''), ' ', IFNULL(pf.last_name,'')) as FATHER_NAME, " .
						  "pf.picture_url as FATHER_PHOTO, " .
						  "pf.mobile_no as FATHER_CONTACT_NO, " .
						  "pf.email as FATHER_EMAIL, " .
						  "pf.occupation as FATHER_OCCUPATION, " .
						  "pf.qualification as FATHER_QUALIFICATION, " .
						  "pf.annual_income as FATHER_ANNUAL_INCOME, " .
						  "pf.company as FATHER_OFFICE_ADDRESS";
		}

		$fatherData = $this->db_readonly->select($tablColumn)
			->from('parent pf')
			->join('student_relation sr', 'pf.id=sr.relation_id')
			->where('sr.relation_type', 'Father')
			->where('sr.std_id', $studentId)
			->get()->row();

		return $fatherData;
	}

	/**
	 * Get Mother data by ID with specific columns
	 */
	public function getMotherDataById($studentId, $tablColumn = '') {
		if (empty($tablColumn)) {
			$tablColumn = "CONCAT(IFNULL(pm.first_name,''), ' ', IFNULL(pm.last_name,'')) as MOTHER_NAME, " .
						  "pm.picture_url as MOTHER_PHOTO, " .
						  "pm.mobile_no as MOTHER_CONTACT_NO, " .
						  "pm.email as MOTHER_EMAIL, " .
						  "pm.occupation as MOTHER_OCCUPATION, " .
						  "pm.qualification as MOTHER_QUALIFICATION, " .
						  "pm.annual_income as MOTHER_ANNUAL_INCOME, " .
						  "pm.company as MOTHER_OFFICE_ADDRESS";
		}

		$motherData = $this->db_readonly->select($tablColumn)
			->from('parent pm')
			->join('student_relation sr', 'pm.id=sr.relation_id')
			->where('sr.relation_type', 'Mother')
			->where('sr.std_id', $studentId)
			->get()->row();

		return $motherData;
	}

	/**
	 * Get Guardian data by ID with specific columns
	 */
	public function getGuardianDataById($studentId, $tablColumn = '') {
		if (empty($tablColumn)) {
			$tablColumn = "CONCAT(IFNULL(pg.first_name,''), ' ', IFNULL(pg.last_name,'')) as GUARDIAN_NAME, " .
						  "pg.picture_url as GUARDIAN_PHOTO, " .
						  "pg.mobile_no as GUARDIAN_CONTACT_NO, " .
						  "pg.email as GUARDIAN_EMAIL, " .
						  "pg.occupation as GUARDIAN_OCCUPATION, " .
						  "pg.qualification as GUARDIAN_QUALIFICATION, " .
						  "pg.annual_income as GUARDIAN_ANNUAL_INCOME, " .
						  "pg.company as GUARDIAN_OFFICE_ADDRESS";
		}

		$guardianData = $this->db_readonly->select($tablColumn)
			->from('parent pg')
			->join('student_relation sr', 'pg.id=sr.relation_id')
			->where('sr.relation_type', 'Guardian')
			->where('sr.std_id', $studentId)
			->get()->row();

		return $guardianData;
	}

	/**
	 * Get Family data by ID with specific columns
	 */
	public function getFamilyDataById($studentId, $tablColumn = '') {
		if (empty($tablColumn)) {
			$tablColumn = "sa.family_picture_url as FAMILY_PHOTO, " .
						  "sa.student_remarks as FAMILY_REMARKS";
		}

		$familyData = $this->db_readonly->select($tablColumn)
			->from('student_admission sa')
			->where('sa.id', $studentId)
			->get()->row();

		return $familyData;
	}

	/**
	 * Get Electives data by ID with specific columns
	 */
	public function getElectivesDataById($studentId, $tablColumn = '') {
		if (empty($tablColumn)) {
			$tablColumn = "GROUP_CONCAT(DISTINCT sub.subject_name SEPARATOR ', ') as ELECTIVES";
		}

		$electivesData = $this->db_readonly->select($tablColumn)
			->from('student_admission sa')
			->join('elective_student_master esm', 'sa.id=esm.student_admission_id', 'left')
			->join('elective_master_group_subjects gs', 'esm.elective_master_group_subject_id=gs.id', 'left')
			->join('subject_master sub', 'sub.id=gs.subject_master_id', 'left')
			->where('sa.id', $studentId)
			->group_by('sa.id')
			->get()->row();

		return $electivesData;
	}

    public function get_class_wise_teacher_subj($section_id, $studentYearId){
	 	$this->db_readonly->distinct('sss.subject_id');
	    $list = $this->db_readonly->select("s.long_name as sub_name, sss.class_section_id,  concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name, sm.id as staff_id")
	    ->from('staff_subject_section sss')
	    ->where('sss.acad_year_id',$studentYearId)
	    ->where('sss.class_section_id', $section_id)
	    ->join('stafflist_subject_section stss', 'stss.sss_id=sss.id')
	    ->join('subjects s','sss.subject_id=s.id')
	    ->join('staff_master sm','stss.staff_id=sm.id')
	    ->order_by('sm.first_name')
	    ->get()->result();
	    // echo "<pre>"; print_r($list); die();
	    $temp = array();
	    foreach ($list as $key => $val) {
    		$temp[trim($val->teacher_name)][] = $val->sub_name;
	    }

	   return $temp;
	}

    public function get_classTeacher_section($section_id){
		return $this->db_readonly->select("concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name")
		->from('class_section cs')
		->where('cs.id',$section_id)
		->join('staff_master sm','cs.class_teacher_id=sm.id')
		->get()->row();
	}

    public function getElectives($stdId){
		$this->db->select('aeg.entity_name, ae.friendly_name');
		$this->db->from('assessment_students_elective as');
		$this->db->join('assessment_entities_group aeg', 'as.ass_entity_gid=aeg.id');
		$this->db->join('assessment_elective_group ae', 'as.ass_elective_gid=ae.id');
		$this->db->where('as.student_id', $stdId);
		return $this->db->get()->result();
	}

	// Get Day Attendnace Wise attendance summary
	public function getAttendanceDaywiseSummaryCount($student_id){
		$yearId =  $this->acad_year->getAcadYearId();
		$result = $this->db_readonly->select("count(case when asds.morning_session_status = 1 and  afternoon_session_status= 1 then 1 else 0 end) as present, count(case when asds.morning_session_status != 1 or afternoon_session_status != 1 then 1 else 0 end) as half_day, count(case when asds.morning_session_status != 1 and  afternoon_session_status != 1 then 1 else 0 end) as absent, count(case when asds.is_late = 1 then 1 else 0 end) as is_late ")
		->from('attendance_std_day_v2_session asd')
		->join('attendance_std_day_v2_students asds','asd.id=asds.attendance_day_v2_session_id')
		->where('asds.student_admission_id',$student_id)
		->where('asd.acad_year_id',$yearId)
		->get()->row();
		return $result;
	}
	// Get Subject Wise attendance summary
	public function get_subject_wise_attendance($student_id) {
		$yearId =  $this->acad_year->getAcadYearId();
        $sql =" SELECT distinct(sub.subject_name),sum(case when s.status =1 then 1 else 0 end ) as present_days,sum(case when s.status =2 then 1 else 0 end ) as absent,sum(case when s.status =3 then 1 else 0 end ) as late
                FROM attendance_v2_master m 
                JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
                JOIN subject_master sub ON m.type_id=sub.id
                where sub.acad_year_id = '$yearId'
                AND s.student_admission_id='$student_id' 
                Group By sub.subject_name
                ORDER BY m.taken_on Asc, m.period_no Asc";
        $result1=$this->db->query($sql)->result();
        return $result1;
    }

	public function get_textsStudentIdwise($parentId, $limit = null, $offset = null)
	{
		$this->db_readonly->select("tm.id, sent_on, message, title, stakeholder_id as student_id, ts.mode, ts.is_read, ts.id as parent_text_id, source");
		$this->db_readonly->from('texting_master tm');
		$this->db_readonly->join('text_sent_to ts', 'ts.texting_master_id=tm.id');
		$this->db_readonly->where("ts.stakeholder_id=$parentId and ts.avatar_type=2");
		$this->db_readonly->order_by('tm.sent_on', 'desc');

		// Add pagination if limit is provided
		if ($limit !== null) {
			$this->db_readonly->limit($limit, $offset);
		}

		return $this->db_readonly->get()->result();
	}
	
	public function getStudentBoard($student_id) {
		return $this->db_readonly->query("SELECT sy.board from student_year sy where acad_year_id=$this->yearId and sy.student_admission_id=$student_id")->row()->board;
	}

	public function get_student_id_by_user_id($stakeholder_id){
		$result =  $this->db_readonly->select('student_id')
		->from('parent')
		->where('id',$stakeholder_id)
		->get()->row();
		if(!empty($result)){
			return $result->student_id;
		}else{
			return 0;
		}
	}

	public function checkStudentPartiallyDeactivated($student_id) {
		return $this->db_readonly->where('id', $student_id)->select('temp_deactivation')->get('student_admission')->row()->temp_deactivation;
	}

	public function update_profile_confirmedbyid($stdYearId){
		// $reqFields = $this->settings->getSetting('parent_profile_mandatory_fields');
		$reqFields= $this->get_requried_fields();
		$acadyearId = $this->acad_year->getAcadYearId();
		// echo "<pre>"; print_r($reqFields); die();
		if (!empty($reqFields)) {
			$result = $this->check_mandatory_field_before($stdYearId, $reqFields);
		}
	
		if (empty($reqFields) || empty($result)) {
			$this->db->where('id',$stdYearId);
			$this->db->where('acad_year_id',$acadyearId);
			return $this->db->update('student_year',array('profile_confirmed'=>'Yes','profile_confirmed_date'=>$this->Kolkata_datetime(),'profile_confirmed_by'=>$this->authorization->getAvatarStakeHolderId(),'profile_status'=>'Lock'));
		}else{
			return $result;
		}
	}

	public function store_edit_history($old_data,$new_data){
		$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		$edit_history = array(
			'student_id' => $studentId,
			'old_data' => $old_data,
			'new_data' => $new_data,
			'edited_by' =>  $this->authorization->getAvatarStakeHolderId(),
			'edited_on' => $this->Kolkata_datetime(),
			'source' => 'Parent'
		);
		$this->db->insert('student_edit_history',$edit_history);
	}

	public function get_requried_fields(){
		$this->db->select('value');
    	$this->db->where('name','profile_mandatory_columns');
    	$mandatoryFields=$this->db->get('config')->result();
		$dbEnabedMandatory = [];
		if (!empty($mandatoryFields)) {
			foreach ($mandatoryFields as $key => $enabled) {
				if (isset($enabled->value)) {
				$dbEnabedMandatory = json_decode($enabled->value);
				}
			}
		}
		return $dbEnabedMandatory;
	}

		public function check_mandatory_field_before($stdYearId, $reqFields){
		$query = $this->db->select('sa.id as stdId, sa.first_name as student_name, sa.dob as student_dob, sy.picture_url as student_photo, sa.gender as student_gender, sa.blood_group as student_blood_group,  sa.nationality as student_nationality, sa.caste as student_caste, sa.religion as student_religion, sa.aadhar_no as student_aadhar, sy.stop as student_stop, sy.pickup_mode as student_pickup_mode, p1.picture_url as father_photo, p2.picture_url as mother_photo, p1.first_name as father_name, p2.first_name as mother_name, p1.qualification as father_qualification, p2.qualification as mother_qualificiation, p1.occupation as father_occupation, p2.occupation as mother_occupation, p1.mobile_no as father_contact_no, p2.mobile_no as mother_contact_no, p1.email as father_email, p2.email as mother_email, p1.annual_income as father_annual_income, p2.annual_income as mother_annual_income, p1.company as father_company, p2.company as mother_company, p1.aadhar_no as father_aadhar, p2.aadhar_no as mother_aadhar, sy.student_house as student_house,sa.email as student_email,sa.mother_tongue as student_mother_tongue,sa.point_of_contact')
		->from('student_admission sa')
		->join('student_year sy','sa.id=sy.student_admission_id')
        ->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'")
        ->join('parent p1', 'p1.id=sr1.relation_id')
        ->join('student_relation sr2', "sr2.std_id=sa.id and sr2.relation_type='mother'")
        ->join('parent p2', 'p2.id=sr2.relation_id')
		->where('sy.id',$stdYearId)
		->get()->row();

		$this->db->select('sa.id as sId, g.picture_url as guardian_photo, g.first_name as guardian_name, g.mobile_no as guardian_contact_no, g.email as  guardian_email')
		->from('student_admission sa')
		->where('sa.admission_status','2')
		->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
		->where("sy.promotion_status!='JOINED'")
		->join('student_relation grm', 'sa.id=grm.std_id')
		->join('parent g', "g.id=grm.relation_id and grm.relation_type='Guardian'") 
		->where_in('sy.id',$stdYearId);
		$guardianList =  $this->db->get()->row();
		
		$query->guardian_photo = (!empty($guardianList)) ?  $guardianList->guardian_photo : '';
		$query->guardian_name = (!empty($guardianList)) ?  $guardianList->guardian_name : '';
		$query->guardian_contact_no = (!empty($guardianList)) ?  $guardianList->guardian_contact_no : '';
		$query->guardian_email = (!empty($guardianList)) ?  $guardianList->guardian_email : '';

		foreach ($reqFields as $key => $val) {
			if ($val == 'FATHER_ADDRESS') {
				$fatherAddress = $this->__getAddresses_field($query->stdId, 'Father');
			}
			if ($val == 'MOTHER_ADDRESS') {
				$motherAddress = $this->__getAddresses_field($query->stdId, 'Mother');
			}
			if ($val == 'STUDENT_ADDRESS') {
				$studentAddress = $this->__getAddresses_field($query->stdId, 'Student');
			}
		}
		
		$query->father_address = (!empty($fatherAddress)) ?  $fatherAddress->Father_address : '';
		$query->mother_address = (!empty($motherAddress)) ?  $motherAddress->Mother_address : '';
		$query->student_address = (!empty($studentAddress)) ?  $studentAddress->Student_address : '';
		
		$colData = (array)$query;
		$fields = [];
		foreach ($reqFields as $key => $value) {
			$required = strtolower($value);
			if (array_key_exists($required, $colData)) {
				if (empty($colData[$required])) {
					array_push($fields, $value);
				}
			}
		}
		return $fields;
	}
	

}
?>