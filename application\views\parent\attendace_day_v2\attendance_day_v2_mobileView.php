<style>
        body {
            font-family: 'Inter', <PERSON><PERSON>, sans-serif;
            background: #fafbfc;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 430px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            padding: 20px 16px 32px 16px;
            border-radius: 0;
        }
        .attendance-header {
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0 0 24px 0;
            text-align: center;
            padding: 16px 0;
            letter-spacing: 0.01em;
            color: #333;
        }
        .summary-card-wrap {
            display: flex;
            gap: 0;
            margin: 0 0 24px 0;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            overflow: hidden;
        }
        .summary-card {
            flex: 1;
            background: #fff;
            border-right: 1px solid #f0f0f0;
            border-radius: 0;
            padding: 16px 8px 12px 8px;
            text-align: center;
        }
        .summary-card:last-child {
            border-right: none;
        }
        .summary-card .label {
            font-size: 0.75rem;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
            line-height: 1.2;
        }
        .summary-card .value {
            font-size: 1.3rem;
            font-weight: 700;
            line-height: 1.1;
        }
        .summary-card .value.attendance { color: #5b6dfa; }
        .summary-card .value.present { color: #2ca66f; }
        .summary-card .value.absent { color: #e14c4c; }
        .summary-card .value.holiday { color: #b68400; }
        .calendar-card {
            background: #fff;
            border-radius: 12px;
            margin: 0 0 16px 0;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            padding: 0 0 16px 0;
        }
        .calendar-legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 8px 12px;
            margin: 16px 0 0 0;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 18px 18px 0 18px;
        }
        .calendar-header .month {
            font-size: 1.13rem;
            font-weight: 600;
            color: #222;
            letter-spacing: 0.01em;
        }
        .calendar-header button {
            background: #f6f8fa;
            border: none;
            border-radius: 6px;
            font-size: 1.2rem;
            width: 36px;
            height: 36px;
            color: #5b6dfa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .calendar-table th, .calendar-table td {
            width: 14.28%;
            height: 54px;
            text-align: center;
            vertical-align: top;
            border: none;
            background: #fff;
            font-size: 1.01rem;
            position: relative;
        }
        .calendar-table th {
            color: #888;
            font-weight: 500;
            font-size: 1.01rem;
            background: #fff;
            padding-bottom: 6px;
        }
        .calendar-table .day-number {
            font-size: 1.01rem;
            font-weight: 600;
            margin-bottom: 2px;
            display: block;
            color: #222;
        }
        .calendar-table .today {
            background: #f5f7ff !important;
            border-radius: 50%;
            color: #4c6ef5 !important;
            border: 1.5px solid #4c6ef5;
            padding: 2px 7px;
            display: inline-block;
        }
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 0.93rem;
            font-weight: 500;
            margin-top: 4px;
            margin-bottom: 2px;
        }
        .present { background: #eafaf1; color: #2ca66f; }
        .absent { background: #fdeaea; color: #e14c4c; }
        .holiday { background: #fff7e0; color: #e1a100; }
        .weekoff { background: #f0f0f0; color: #aaa; }
        .leave { background: #fffbe6; color: #b68400; border: 1px solid #ffe58f; }
        .calendar-table td {
            padding: 0;
        }
        .fc-event {
            border: none !important;
            padding: 0 !important;
            border-radius: 6px !important;
            font-size: 12px !important;
            font-weight: 600 !important;
            margin: 1px 0 !important;
            display: flex !important;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            min-width: 24px;
            min-height: 24px;
            max-width: 24px;
            max-height: 24px;
            box-sizing: border-box;
        }
        .fc-event .att-icon {
            font-size: 11px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff !important;
        }
        .fc-event .att-icon i {
            color: #fff !important;
        }
        .fc-event.present {
            background:#308242 !important;
            color: #fff !important;
            border: 1.5px solid #2ca66f !important;
        }
        .fc-event.absent {
            background:#e14c4c !important;
            color: #fff !important;
            border: 1.5px solid #e14c4c !important;
        }
        .fc-event.holiday {
            background:#e1a100 !important;
            color: #fff !important;
            border: 1.5px solid #e1a100 !important;
        }
        .fc-event.weekoff {
            background:#756969 !important;
            color: #fff !important;
            border: 1.5px solid #aaa !important;
        }
        .fc-event.halfday {
            background: #fffbe6 !important;
            color: #fff !important;
            border: 1.5px solid #b68400 !important;
        }
    </style>

<?php 
$present_days= isset($getOverallDetails['present_days']) ? $getOverallDetails['present_days'] : '--';
$absent_days= isset($getOverallDetails['absent_days']) ? $getOverallDetails['absent_days'] : '--';
$holidays= isset($getOverallDetails['holidays']) ? $getOverallDetails['holidays'] : '--';
$total_days= isset($getOverallDetails['total_days']) ? $getOverallDetails['total_days'] : '--';
if (is_numeric($present_days) && is_numeric($total_days) && is_numeric($holidays) && ($total_days - $holidays) > 0) {
    $percentage = round(($present_days / ($total_days - $holidays)) * 100, 2);
} else {
    $percentage = '--';
}
?>
<div class="container">
    <div class="attendance-header">Attendance</div>

    <!-- Month-wise Report Button -->
    <div class="text-center" style="margin-bottom: 15px;">
        <a href="<?php echo site_url('parent_controller/monthwise_attendance_report'); ?>" class="btn btn-info btn-sm">
            <i class="fa fa-calendar"></i> Month-wise Report
        </a>
    </div>

    <div class="summary-card-wrap">
        <div class="summary-card">
            <div class="label">Overall Attendance</div>
            <div class="value attendance"><?php echo is_numeric($percentage) ? $percentage.'%' : '--'; ?></div>
        </div>
        <div class="summary-card">
            <div class="label">Present Days</div>
            <div class="value present"><?php echo is_numeric($present_days) ? $present_days : '--'; ?></div>
        </div>
        <div class="summary-card">
            <div class="label">Absent Days</div>
            <div class="value absent"><?php echo is_numeric($absent_days) ? $absent_days : '--'; ?></div>
        </div>
        <div class="summary-card">
            <div class="label">Late Count</div>
            <div class="value holiday"><?php echo isset($getOverallDetails['late_count']) && is_numeric($getOverallDetails['late_count']) ? $getOverallDetails['late_count'] : '--'; ?></div>
        </div>
    </div>
    <div class="calendar-legend">
        <div style="display: flex; align-items: center; gap: 6px; min-width: 70px;">
            <span style="color:#2ca66f; font-size: 14px;"><i class="fa fa-check"></i></span>
            <span style="font-size: 0.85rem; color: #2ca66f; font-weight: 500;">Present</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 70px;">
            <span style="color:#e14c4c; font-size: 14px;"><i class="fa fa-times"></i></span>
            <span style="font-size: 0.85rem; color: #e14c4c; font-weight: 500;">Absent</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 70px;">
            <span style="color:#e1a100; font-size: 14px;"><i class="fa fa-calendar"></i></span>
            <span style="font-size: 0.85rem; color: #e1a100; font-weight: 500;">Holiday</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 70px;">
            <span style="color:#756969; font-size: 14px;"><i class="fa fa-bed"></i></span>
            <span style="font-size: 0.85rem; color: #756969; font-weight: 500;">Weekoff</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 70px;">
            <span style="color:#b68400; font-size: 14px;"><i class="fa fa-clock-o"></i></span>
            <span style="font-size: 0.85rem; color: #b68400; font-weight: 500;">Halfday</span>
        </div>
    </div>
    <div class="calendar-card">
        <div id="att_calendar"></div>
    </div>
</div>
<!-- FullCalendar CSS and JS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>
<script>
function fetchAttendanceEvents(startDate, endDate) {
    var student_id = <?php echo isset($studentId) ? json_encode($studentId) : 'null'; ?>;
    return fetch('<?php echo base_url("parent_controller/get_attendance_events"); ?>', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({
            start: startDate,
            end: endDate,
            student_id: student_id
        })
    }).then(response => response.json());
}
document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('att_calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: ''
        },
        events: function(fetchInfo, successCallback, failureCallback) {
            fetchAttendanceEvents(fetchInfo.startStr, fetchInfo.endStr)
                .then(data => successCallback(data))
                .catch(() => failureCallback([]));
        },
        eventDidMount: function(info) {
            // Show only icon, no text, using classNames array for event type
            let icon = '';
            let classList = info.event.classNames || [];
            if (classList.includes('present')) {
                icon = '<span class="att-icon"><i class="fa fa-check"></i></span>';
            } else if (classList.includes('absent')) {
                icon = '<span class="att-icon"><i class="fa fa-times"></i></span>';
            } else if (classList.includes('holiday')) {
                icon = '<span class="att-icon"><i class="fa fa-calendar"></i></span>';
            } else if (classList.includes('weekoff')) {
                icon = '<span class="att-icon"><i class="fa fa-bed"></i></span>';
            } else if (classList.includes('halfday')) {
                icon = '<span class="att-icon"><i class="fa fa-clock-o"></i></span>';
            }
            if (icon) {
                info.el.innerHTML = icon;
            } else {
                info.el.innerHTML = '';
            }
            // Remove any text nodes that may remain
            Array.from(info.el.childNodes).forEach(function(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    node.textContent = '';
                }
            });
            info.el.title = info.event.title;
        }
    });
    calendar.render();
});
</script>

