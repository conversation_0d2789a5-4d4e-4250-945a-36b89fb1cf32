<?php
$current_date = date('Y-m-d');
$current_month_start = date('Y-m-01');
?>

<div class="page-content-wrap">
    <div class="row">
        <!-- Calendar Navigation -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Calendar</h5>
                </div>
                <div class="card-body">
                    <!-- Month Navigation -->
                    <div class="calendar-nav-desktop mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="prevMonth">
                                <i class="fa fa-chevron-left"></i>
                            </button>
                            <h6 id="currentMonth" class="mb-0 font-weight-bold"><?php echo $current_month_display; ?></h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="nextMonth">
                                <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Mini Calendar -->
                    <div id="miniCalendar" class="mini-calendar">
                        <!-- Mini calendar will be loaded here -->
                    </div>

                    <!-- Legend -->
                    <div class="calendar-legend mt-4">
                        <h6 class="font-weight-bold mb-3">Legend</h6>
                        <div class="legend-item mb-2">
                            <span class="legend-color holiday"></span>
                            <span class="legend-text">Holiday</span>
                        </div>
                        <div class="legend-item mb-2">
                            <span class="legend-color information"></span>
                            <span class="legend-text">Information</span>
                        </div>
                        <div class="legend-item mb-2">
                            <span class="legend-color money"></span>
                            <span class="legend-text">Money</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Calendar and Events -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Events Calendar</h5>
                    <div class="calendar-view-toggle">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary active" id="monthView">Month</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="weekView">Week</button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="mainCalendar" class="main-calendar">
                        <!-- Main calendar will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Events List -->
            <div class="card mt-3" id="eventsCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">Events on <span id="selectedDate"></span></h6>
                </div>
                <div class="card-body" id="eventsList">
                    <!-- Events will be loaded here -->
                </div>
            </div>

            <!-- No Events Message -->
            <div class="card mt-3" id="noEventsCard" style="display: none;">
                <div class="card-body text-center py-5">
                    <div class="no-events-illustration mb-4">
                        <i class="fa fa-calendar-o fa-4x text-muted"></i>
                    </div>
                    <h5 class="text-muted mb-3">No Events</h5>
                    <p class="text-muted">You have no upcoming events.<br>Let's change the date!</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>

<style>
.calendar-nav-desktop {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
}

.mini-calendar {
    font-size: 12px;
}

.mini-calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 5px;
}

.mini-calendar-header-day {
    text-align: center;
    font-weight: 600;
    color: #6c757d;
    padding: 5px 2px;
}

.mini-calendar-body {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.mini-calendar-day {
    text-align: center;
    padding: 5px 2px;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s;
}

.mini-calendar-day:hover {
    background-color: #f8f9fa;
}

.mini-calendar-day.today {
    background-color: #007bff;
    color: white;
    font-weight: 600;
}

.mini-calendar-day.selected {
    background-color: #28a745;
    color: white;
}

.mini-calendar-day.other-month {
    color: #ccc;
}

.mini-calendar-day.has-events {
    background-color: #fff3e0;
    font-weight: 600;
}

.main-calendar {
    min-height: 600px;
}

.main-calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.main-calendar-header-day {
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    color: #495057;
    border-right: 1px solid #dee2e6;
}

.main-calendar-header-day:last-child {
    border-right: none;
}

.main-calendar-body {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.main-calendar-day {
    min-height: 120px;
    padding: 10px;
    border-right: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
}

.main-calendar-day:last-child {
    border-right: none;
}

.main-calendar-day:hover {
    background-color: #f8f9fa;
}

.main-calendar-day.today {
    background-color: #e3f2fd;
}

.main-calendar-day.selected {
    background-color: #bbdefb;
}

.main-calendar-day.other-month {
    background-color: #fafafa;
    color: #ccc;
}

.main-calendar-day-number {
    font-weight: 600;
    margin-bottom: 5px;
}

.main-calendar-day.today .main-calendar-day-number {
    color: #1976d2;
}

.calendar-event-item {
    background: #2196f3;
    color: white;
    padding: 2px 6px;
    margin-bottom: 2px;
    border-radius: 3px;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.calendar-event-item.holiday {
    background: #f44336;
}

.calendar-event-item.information {
    background: #2196f3;
}

.calendar-event-item.money {
    background: #4caf50;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
}

.legend-color.holiday {
    background: #f44336;
}

.legend-color.information {
    background: #2196f3;
}

.legend-color.money {
    background: #4caf50;
}

.legend-text {
    font-size: 13px;
}

.event-item {
    padding: 15px;
    border-left: 4px solid #2196f3;
    background: #f8f9fa;
    margin-bottom: 10px;
    border-radius: 4px;
}

.event-item.holiday {
    border-left-color: #f44336;
}

.event-item.information {
    border-left-color: #2196f3;
}

.event-item.money {
    border-left-color: #4caf50;
}

.event-title {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 16px;
}

.event-date {
    color: #666;
    font-size: 14px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.no-events-illustration {
    opacity: 0.3;
}
</style>

<script>
$(document).ready(function() {
    let currentDate = '<?php echo $current_month; ?>';
    let studentBoard = '<?php echo isset($student_board) ? $student_board : ""; ?>';
    let selectedDate = null;
    let currentEvents = [];
    
    // Initialize calendar
    loadCalendar(currentDate);
    
    // Navigation handlers
    $('#prevMonth').click(function() {
        navigateMonth('prev');
    });
    
    $('#nextMonth').click(function() {
        navigateMonth('next');
    });
    
    // View toggle handlers
    $('#monthView, #weekView').click(function() {
        $('.btn-group .btn').removeClass('active');
        $(this).addClass('active');
        // Week view can be implemented later
    });
    
    function navigateMonth(direction) {
        showLoading();
        
        $.ajax({
            url: '<?php echo site_url('parent/parent_dashboard_controller/get_calendar_events'); ?>',
            type: 'POST',
            data: {
                date: currentDate,
                state: direction,
                student_board: studentBoard,
                student_id: '<?php echo $studentId; ?>'
            },
            success: function(response) {
                hideLoading();
                try {
                    const data = JSON.parse(response);
                    if (data.status === 'success') {
                        currentDate = data.date;
                        currentEvents = data.events;
                        $('#currentMonth').text(data.displayDate);
                        renderCalendars(data.events);
                        hideEventDetails();
                    } else {
                        showError('Failed to load calendar events');
                    }
                } catch (e) {
                    showError('Error parsing calendar data');
                }
            },
            error: function() {
                hideLoading();
                showError('Failed to load calendar events');
            }
        });
    }
    
    function loadCalendar(date) {
        showLoading();
        
        $.ajax({
            url: '<?php echo site_url('parent/parent_dashboard_controller/get_calendar_events'); ?>',
            type: 'POST',
            data: {
                date: date,
                state: 'current',
                student_board: studentBoard,
                student_id: '<?php echo $studentId; ?>'
            },
            success: function(response) {
                hideLoading();
                try {
                    const data = JSON.parse(response);
                    if (data.status === 'success') {
                        currentEvents = data.events;
                        $('#currentMonth').text(data.displayDate);
                        renderCalendars(data.events);
                    } else {
                        showError('Failed to load calendar events');
                    }
                } catch (e) {
                    showError('Error parsing calendar data');
                }
            },
            error: function() {
                hideLoading();
                showError('Failed to load calendar events');
            }
        });
    }
    
    function renderCalendars(events) {
        renderMiniCalendar(events);
        renderMainCalendar(events);
    }
    
    function renderMiniCalendar(events) {
        const calendarHtml = generateMiniCalendarHTML(currentDate, events);
        $('#miniCalendar').html(calendarHtml);
        
        // Add click handlers to mini calendar days
        $('.mini-calendar-day').click(function() {
            const dayNumber = $(this).data('day');
            const hasEvents = $(this).hasClass('has-events');
            
            if (dayNumber && !$(this).hasClass('other-month')) {
                $('.mini-calendar-day').removeClass('selected');
                $('.main-calendar-day').removeClass('selected');
                $(this).addClass('selected');
                
                // Also select in main calendar
                $(`.main-calendar-day[data-day="${dayNumber}"]`).addClass('selected');
                
                selectedDate = currentDate + '-' + String(dayNumber).padStart(2, '0');
                
                if (hasEvents) {
                    showEventDetails(selectedDate, events);
                } else {
                    showNoEvents();
                }
            }
        });
    }
    
    function renderMainCalendar(events) {
        const calendarHtml = generateMainCalendarHTML(currentDate, events);
        $('#mainCalendar').html(calendarHtml);
        
        // Add click handlers to main calendar days
        $('.main-calendar-day').click(function() {
            const dayNumber = $(this).data('day');
            const hasEvents = $(this).find('.calendar-event-item').length > 0;
            
            if (dayNumber && !$(this).hasClass('other-month')) {
                $('.main-calendar-day').removeClass('selected');
                $('.mini-calendar-day').removeClass('selected');
                $(this).addClass('selected');
                
                // Also select in mini calendar
                $(`.mini-calendar-day[data-day="${dayNumber}"]`).addClass('selected');
                
                selectedDate = currentDate + '-' + String(dayNumber).padStart(2, '0');
                
                if (hasEvents) {
                    showEventDetails(selectedDate, events);
                } else {
                    showNoEvents();
                }
            }
        });
    }
    
    function generateMiniCalendarHTML(dateStr, events) {
        const [year, month] = dateStr.split('-');
        const firstDay = new Date(year, month - 1, 1);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const today = new Date();
        const todayStr = today.getFullYear() + '-' +
                        String(today.getMonth() + 1).padStart(2, '0') + '-' +
                        String(today.getDate()).padStart(2, '0');

        // Create events map
        const eventsMap = {};
        events.forEach(event => {
            const eventDate = event.event_on.split(' ')[0];
            if (!eventsMap[eventDate]) {
                eventsMap[eventDate] = [];
            }
            eventsMap[eventDate].push(event);
        });

        let html = '<div class="mini-calendar-header">';
        const dayNames = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
        dayNames.forEach(day => {
            html += `<div class="mini-calendar-header-day">${day}</div>`;
        });
        html += '</div>';

        html += '<div class="mini-calendar-body">';
        const currentDate = new Date(startDate);
        for (let i = 0; i < 42; i++) {
            const dayStr = currentDate.getFullYear() + '-' +
                          String(currentDate.getMonth() + 1).padStart(2, '0') + '-' +
                          String(currentDate.getDate()).padStart(2, '0');

            const isCurrentMonth = currentDate.getMonth() === (month - 1);
            const isToday = dayStr === todayStr;
            const hasEvents = eventsMap[String(currentDate.getDate()).padStart(2, '0')];

            let classes = 'mini-calendar-day';
            if (!isCurrentMonth) classes += ' other-month';
            if (isToday) classes += ' today';
            if (hasEvents) classes += ' has-events';

            html += `<div class="${classes}" data-day="${currentDate.getDate()}" data-date="${dayStr}">`;
            html += `${currentDate.getDate()}`;
            html += '</div>';

            currentDate.setDate(currentDate.getDate() + 1);
        }
        html += '</div>';
        return html;
    }

    function generateMainCalendarHTML(dateStr, events) {
        const [year, month] = dateStr.split('-');
        const firstDay = new Date(year, month - 1, 1);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const today = new Date();
        const todayStr = today.getFullYear() + '-' +
                        String(today.getMonth() + 1).padStart(2, '0') + '-' +
                        String(today.getDate()).padStart(2, '0');

        // Create events map
        const eventsMap = {};
        events.forEach(event => {
            const eventDate = event.event_on.split(' ')[0];
            if (!eventsMap[eventDate]) {
                eventsMap[eventDate] = [];
            }
            eventsMap[eventDate].push(event);
        });

        let html = '<div class="main-calendar-header">';
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        dayNames.forEach(day => {
            html += `<div class="main-calendar-header-day">${day}</div>`;
        });
        html += '</div>';

        html += '<div class="main-calendar-body">';
        const currentDate = new Date(startDate);
        for (let i = 0; i < 42; i++) {
            const dayStr = currentDate.getFullYear() + '-' +
                          String(currentDate.getMonth() + 1).padStart(2, '0') + '-' +
                          String(currentDate.getDate()).padStart(2, '0');

            const isCurrentMonth = currentDate.getMonth() === (month - 1);
            const isToday = dayStr === todayStr;
            const dayEvents = eventsMap[String(currentDate.getDate()).padStart(2, '0')] || [];

            let classes = 'main-calendar-day';
            if (!isCurrentMonth) classes += ' other-month';
            if (isToday) classes += ' today';

            html += `<div class="${classes}" data-day="${currentDate.getDate()}" data-date="${dayStr}">`;
            html += `<div class="main-calendar-day-number">${currentDate.getDate()}</div>`;

            // Add events
            dayEvents.slice(0, 3).forEach(event => {
                const eventClass = event.event_type.toLowerCase().replace(' ', '-');
                const eventName = event.names.join(', ');
                html += `<div class="calendar-event-item ${eventClass}" title="${eventName}">${eventName}</div>`;
            });

            if (dayEvents.length > 3) {
                html += `<div class="calendar-event-item more">+${dayEvents.length - 3} more</div>`;
            }

            html += '</div>';
            currentDate.setDate(currentDate.getDate() + 1);
        }
        html += '</div>';
        return html;
    }

    function showEventDetails(date, events) {
        const dayNumber = date.split('-')[2];
        const eventsForDay = events.filter(event => {
            const eventDate = event.event_on.split(' ')[0];
            return eventDate === dayNumber;
        });

        if (eventsForDay.length > 0) {
            let html = '';
            eventsForDay.forEach(event => {
                const eventClass = event.event_type.toLowerCase().replace(' ', '-');
                html += `<div class="event-item ${eventClass}">`;
                html += `<div class="event-title">${event.names.join(', ')}</div>`;
                html += `<div class="event-date">${event.event_on}</div>`;
                html += '</div>';
            });

            $('#eventsList').html(html);
            $('#selectedDate').text(formatDate(date));
            $('#eventsCard').show();
            $('#noEventsCard').hide();
        } else {
            showNoEvents();
        }
    }

    function showNoEvents() {
        $('#eventsCard').hide();
        $('#noEventsCard').show();
    }

    function hideEventDetails() {
        $('#eventsCard').hide();
        $('#noEventsCard').hide();
    }

    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    function showLoading() {
        $('#loadingOverlay').show();
    }

    function hideLoading() {
        $('#loadingOverlay').hide();
    }

    function showError(message) {
        console.error(message);
        // You can implement a toast notification here
    }
});
</script>
