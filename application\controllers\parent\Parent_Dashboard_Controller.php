<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Parent_Dashboard_Controller extends CI_Controller {
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
			redirect('auth/login', 'refresh');
		}
		if (!$this->authorization->isModuleEnabled('PARENTS_LOGIN')) {
			redirect('parentdashboard', 'refresh');
		}

		// Load required models and libraries
		$this->load->model('parent/parent_model');
		$this->load->model('dashboard_model');
		$this->load->model('feesv2/fees_student_model');
		$this->load->model('attendance_day_v2/attendance_day_v2_model');
		$this->load->model('parentv2/circular_model');
		$this->load->model('calenderevents_model');
		$this->load->library('filemanager');
		$this->config->load('form_elements');
		$this->yearId = $this->acad_year->getAcadYearId();
	}

	// ========================================
	// CORE METHODS
	// ========================================

	/**
	 * Main entry point with device detection
	 */
	public function index($student_id = '') {
		$student_id = $this->input->get('student_id');
		if ($student_id) {
			$data['studentId'] = $student_id;
		} else {
			$data['studentId'] = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		// Enhanced device detection
		$deviceType = $this->detect_device_type();

		switch($deviceType) {
			case 'mobile':
				$this->mobile_dashboard();
				break;
			case 'tablet':
				$this->tablet_dashboard();
				break;
			case 'desktop':
			default:
				$this->desktop_dashboard();
				break;
		}
	}

	/**
	 * Mobile-specific dashboard
	 */
	public function mobile_dashboard() {
		$data = $this->load_common_data();
		$data['parentModules'] = $this->config->item('parent_side_modules');
		$data['main_content'] = 'dashboard/parent/pages/dashboard/mobile_dashboard';
		$this->load->view('dashboard/parent/layouts/mobile/main', $data);
	}

	/**
	 * Tablet-specific dashboard
	 */
	public function tablet_dashboard() {
		$data = $this->load_common_data();
		$data['main_content'] = 'dashboard/parent/pages/dashboard/tablet_dashboard';
		$this->load->view('dashboard/parent/layouts/tablet/main', $data);
	}

	/**
	 * Desktop-specific dashboard
	 */
	public function desktop_dashboard() {
		$data = $this->load_common_data();
		$data['parentModules'] = $this->config->item('parent_side_modules');
		$data['main_content'] = 'dashboard/parent/pages/dashboard/desktop_dashboard';
		$this->load->view('dashboard/parent/layouts/desktop/main', $data);
	}

	// ========================================
	// PROFILE METHODS
	// ========================================

	/**
	 * Profile overview
	 */
	public function profile() {
		$loadData = $this->load_common_data();
		
		// Decode base64-encoded JSON data if provided via GET parameter 'data'
		$encodedData = $this->input->get('data');
		if ($encodedData) {
			$json = urldecode(base64_decode($encodedData));
			$decoded = json_decode($json, true);
			if (is_array($decoded)) {
				$loadData = array_merge($loadData, $decoded);
			}
		}
		$data['student_data'] = $loadData;
		$deviceType = $this->detect_device_type();
		switch($deviceType) {
			case 'mobile':
				$data['main_content'] = 'dashboard/parent/pages/profile/mobile_profile';
				$this->load->view('dashboard/parent/layouts/mobile/main', $data);
				break;
			case 'tablet':
				$data['main_content'] = 'dashboard/parent/pages/profile/tablet_profile';
				$this->load->view('dashboard/parent/layouts/tablet/main', $data);
				break;
			case 'desktop':
			default:
				$data['main_content'] = 'dashboard/parent/pages/profile/desktop_profile';
				$this->load->view('dashboard/parent/layouts/desktop/main', $data);
				break;
		}
	}

	
	public function getflashNewsdata(){
		$class_section_id = $this->input->post('class_section_id');
		$class_name = $this->input->post('class_name');
		$section_name = $this->input->post('section_name');
		$board = $this->input->post('board');
		$this->load->model('flash_model');
		//Get Flash news data
        $is_enforce = 0;
		$flash_news = $this->flash_model->get_flash_news_for_section_v2($class_section_id, $class_name, $section_name, $board);
		$enforce_reading = array();
		if(!empty($flash_news)){
			foreach ($flash_news as $flash_obj) {
				if ($flash_obj->enforce_reading == '1') {
					$is_enforce = 1;
					$enforce_reading = $flash_obj;
				}
			}
		}
		echo json_encode(array('flash_news'=>$flash_news, 'is_enforce'=>$is_enforce, 'enforce_reading'=>$enforce_reading));
	}

	
	/**
	 * Detailed profile view
	 */
	public function profile_details() {
		$loadData = $this->load_common_data();
	
		// Handle encoded data from URL parameters
		$encodedData = $this->input->get('data');
		if ($encodedData) {
			$json = urldecode(base64_decode($encodedData));
			$decoded = json_decode($json, true);
			if (is_array($decoded)) {
				$loadData = array_merge($loadData, $decoded);
			}
		}
		$data['student_id'] = $loadData['studentId'];
		$data['studentData'] = $loadData;
		// Get comprehensive student data
		$this->config->load('form_elements');
		$data['parent_columns'] = $parentColumns = $this->config->item('parent_side_columns');

		// Get display columns from settings, with fallback to default
		if (isset($this->config->schoolSettings['parent_profile_display_columns']['value'])) {
			$displayColumnsJson = $this->config->schoolSettings['parent_profile_display_columns']['value'];
		} else {
			$displayColumnsJson = '[]';
		}
		$data['display_columns'] = json_decode($displayColumnsJson, true);

		$displayParentColumns = array();
		foreach ($data['parent_columns'] as $key => $value) {
			if (in_array($value['column_name'], $data['display_columns'])) {
				if (!isset($displayParentColumns[$value['tabs']])) {
					$displayParentColumns[$value['tabs']] = array();
				}
				$displayParentColumns[$value['tabs']][] = $value;
			}
		}
		$data['displayList'] = $displayParentColumns;

		$deviceType = $this->detect_device_type();
		switch($deviceType) {
			case 'mobile':
				$data['main_content'] = 'dashboard/parent/pages/profile/mobile_profile_details';
				$this->load->view('dashboard/parent/layouts/mobile/main', $data);
				break;
			case 'tablet':
				$data['main_content'] = 'dashboard/parent/pages/profile/tablet_profile_details';
				$this->load->view('dashboard/parent/layouts/tablet/main', $data);
				break;
			case 'desktop':
			default:
				$data['main_content'] = 'dashboard/parent/pages/profile/desktop_profile_details';
				$this->load->view('dashboard/parent/layouts/desktop/main', $data);
				break;
		}
	}

	/**
	 * Profile editing (placeholder for future implementation)
	 */
	public function profile_edit() {
		// This method can be implemented when profile editing is required
		show_404();
	}

	// ========================================
	// AJAX DATA METHODS
	// ========================================

	/**
	 * Get student basic data
	 */
	public function get_student_data(){
		$student_id = $this->input->post('student_id');
		if (!$student_id) {
			$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($student_id)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		$result = $this->parent_model->get_student_datails($student_id);
		echo json_encode($result);
	}

	/**
	 * Get dashboard statistics
	 */
	public function get_dashboard_statistics() {
		$student_id = $this->input->post('student_id');
		if (!$student_id) {
			$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($student_id)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		if($this->db->table_exists('attendance_std_day_v2_session')){
			$result = $this->parent_model->getAttendanceDaywiseSummaryCount($student_id);
		}
		if($this->db->table_exists('attendance_v2_master')){
			$result = $this->parent_model->get_subject_wise_attendance($student_id);
		}

		$statistics = array(
			'present' => (!empty($result) && isset($result->present)) ? $result->present : 0,
			'absent' => (!empty($result) && isset($result->absent)) ? $result->absent : 0,
			'half_day' => (!empty($result) && isset($result->half_day)) ? $result->half_day : 0,
			'late' => (!empty($result) && isset($result->is_late)) ? $result->is_late : 0
		);
		echo json_encode($statistics);
	}

	/**
	 * Get recent activities feed
	 */
	public function get_recent_activities() {
		$student_id = $this->input->post('student_id');
		if (!$student_id) {
			$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($student_id)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		$student_data = $this->parent_model->get_student_datails($student_id);
		$activities = array();

		// Get recent homework
		$homework = $this->parent_model->getRecentHomework($student_data->class_section_id, 3);
		foreach ($homework as $hw) {
			$activities[] = array(
				'type' => 'homework',
				'title' => 'Complete math assignment',
				'description' => 'Chapter 2, questions from 1 to 12',
				'date' => date('d M', strtotime($hw->created_date)),
				'due_date' => date('d M', strtotime($hw->created_date . ' +3 days')),
				'status_class' => 'pending'
			);
		}

		// Get recent student tasks
		$tasks = $this->parent_model->getRecentStudentTasks($student_id, 2);
		foreach ($tasks as $task) {
			$activities[] = array(
				'type' => 'task',
				'title' => 'Student task assigned',
				'description' => 'Complete the assigned learning task',
				'date' => date('d M', strtotime($task->created_date)),
				'due_date' => null,
				'status_class' => $task->read_status == 'read' ? 'completed' : 'pending'
			);
		}

		// Sort activities by date
		usort($activities, function($a, $b) {
			return strtotime($b['date']) - strtotime($a['date']);
		});

		echo json_encode(array_slice($activities, 0, 5));
	}

	/**
	 * Get flash news/announcements
	 */
	public function get_flash_news(){
		$class_section_id = $this->input->post('class_section_id');
		$class_name = $this->input->post('class_name');
		$section_name = $this->input->post('section_name');
		$board = $this->input->post('board');

		$this->load->model('flash_model');
		$is_enforce = 0;
		$flash_news = $this->flash_model->get_flash_news_for_section_v2($class_section_id, $class_name, $section_name, $board);
		$enforce_reading = array();

		if(!empty($flash_news)){
			foreach ($flash_news as $flash_obj) {
				if ($flash_obj->enforce_reading == '1') {
					$is_enforce = 1;
					$enforce_reading = $flash_obj;
				}
			}
		}

		echo json_encode(array('flash_news'=>$flash_news, 'is_enforce'=>$is_enforce, 'enforce_reading'=>$enforce_reading));
	}

	/**
	 * Get recent circulars
	 */
	public function get_recent_circulars() {
		$student_id = $this->input->post('student_id');
		if (!$student_id) {
			$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($student_id)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		$parent_id = $this->parent_model->getParentIdByStudentId($student_id);
		$circulars = $this->circular_model->getRecentCirculars($parent_id, 5);

		// Format circulars for display
		$formatted_circulars = array();
		foreach ($circulars as $circular) {
			$formatted_circulars[] = array(
				'id' => $circular->id,
				'title' => $circular->title,
				'description' => substr(strip_tags($circular->short_body), 0, 100) . '...',
				'day' => date('d', strtotime($circular->sent_date)),
				'month' => date('M', strtotime($circular->sent_date)),
				'category' => ucfirst($circular->category)
			);
		}

		echo json_encode($formatted_circulars);
	}

	/**
	 * Get recent events
	 */
	public function get_recent_events() {
		$student_id = $this->input->post('student_id');
		if (!$student_id) {
			$student_id = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($student_id)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		$student_data = $this->parent_model->get_student_datails($student_id);
		$events = $this->calenderevents_model->getUpcomingEvents($student_data->board, 5);

		// Format events for display
		$formatted_events = array();
		foreach ($events as $event) {
			$event_date = new DateTime($event->from_date);
			$formatted_events[] = array(
				'id' => $event->id,
				'title' => $event->event_title,
				'description' => substr(strip_tags($event->event_description), 0, 100) . '...',
				'day' => $event_date->format('d'),
				'month' => $event_date->format('M'),
				'category' => $this->_getEventCategoryClass($event->event_type),
				'category_class' => strtolower(str_replace(' ', '-', $event->event_type))
			);
		}

		echo json_encode($formatted_events);
	}

	// ========================================
	// PROFILE DATA METHODS (organized by category)
	// ========================================

	/**
	 * Get student profile data by header_list categories
	 */
	public function get_student_profile_data(){
		$studentId = $this->input->post('student_id');
		$listDataInput = $this->input->post('listDataInput');

		if (!$studentId) {
			$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($studentId)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		try {
			$result = array();

			// Group data by header_list
			if (is_array($listDataInput)) {
				$groupedData = $this->groupDataByHeaderList($listDataInput);
				// Process each group separately
				foreach ($groupedData as $headerList => $items) {
					$columns = [];
					foreach ($items as $item) {
						if (!empty($item['table_column']) && !empty($item['field'])) {
							$columns[] = $item['table_column'] . ' as ' . $item['field'];
						}
					}
					$tablColumn = implode(', ', $columns);

					if (!empty($tablColumn)) {
						$groupResult = $this->getDataByHeaderList($studentId, $headerList, $tablColumn);
						if ($groupResult) {
							// Convert object to array and merge with main result
							$groupArray = (array) $groupResult;
							$result = array_merge($result, $groupArray);
						}
					}
				}

				// Process photo fields to generate proper URLs
				if (!empty($result)) {
					$result = $this->processPhotoFieldsInResult((object)$result, $listDataInput);
					$result = (array) $result;
				}
			}
			// If no specific columns requested, get basic profile data
			if (empty($result)) {
				$result = $this->getBasicProfileData($studentId);
			}

			echo json_encode($result);

		} catch (Exception $e) {
			log_message('error', 'Error in get_student_profile_data: ' . $e->getMessage());
			echo json_encode(array('error' => 'Internal server error: ' . $e->getMessage()));
		}
	}

	/**
	 * Get connected siblings data
	 */
	public function get_sibling_data(){
		$currentStudentId = $this->input->post('currentStudentId');

		if (!$this->validate_student_access($currentStudentId)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		$result = $this->parent_model->getSiblingdata($currentStudentId);
		echo json_encode($result);
	}

	// ========================================
	// UTILITY METHODS
	// ========================================

	/**
	 * Enhanced device detection
	 */
	private function detect_device_type() {
		// Check for tablet first (more specific)
		if ($this->mobile_detect->isTablet()) {
			return 'tablet';
		}
		// Then check for mobile
		if ($this->mobile_detect->isMobile()) {
			return 'mobile';
		}
		// Default to desktop
		return 'desktop';
	}

	/**
	 * Load common data for all views
	 */
	private function load_common_data() {
		$student_id = $this->input->get('student_id');
		if ($student_id) {
			$data['studentId'] = $student_id;
		} else {
			$data['studentId'] = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		// Add any other common data here
		$data['yearId'] = $this->yearId;

		return $data;
	}

	/**
	 * Security validation for student access
	 */
	private function validate_student_access($student_id) {
		if (!$student_id) {
			return false;
		}

		// Add your validation logic here
		// For example, check if the logged-in parent has access to this student
		$parent_students = $this->parent_model->getStudentIdOfLoggedInParent();

		// This is a simplified check - you may need more complex validation
		return true; // Placeholder - implement actual validation
	}

	/**
	 * Group listDataInput by header_list
	 */
	private function groupDataByHeaderList($listDataInput) {
		$grouped = array();

		foreach ($listDataInput as $item) {
			if (!empty($item['header_list'])) {
				$headerList = $item['header_list'];
				if (!isset($grouped[$headerList])) {
					$grouped[$headerList] = array();
				}
				$grouped[$headerList][] = $item;
			}
		}

		return $grouped;
	}

	/**
	 * Get data based on header_list type
	 */
	private function getDataByHeaderList($studentId, $headerList, $tablColumn) {
		switch ($headerList) {
			case 'student_info':
				return $this->parent_model->getStudentDataById($studentId, $tablColumn);

			case 'father_info':
				return $this->parent_model->getFatherDataById($studentId, $tablColumn);

			case 'mother_info':
				return $this->parent_model->getMotherDataById($studentId, $tablColumn);

			case 'guardian_info':
				return $this->parent_model->getGuardianDataById($studentId, $tablColumn);

			case 'family_info':
				return $this->parent_model->getFamilyDataById($studentId, $tablColumn);

			case 'Electives':
				return $this->parent_model->getElectivesDataById($studentId, $tablColumn);

			default:
				log_message('error', 'Unknown header_list type: ' . $headerList);
				return null;
		}
	}

	/**
	 * Get basic profile data when no specific columns are requested
	 */
	private function getBasicProfileData($studentId) {
		$studentData = $this->parent_model->getStudentDataById($studentId,
			"CONCAT(IFNULL(sa.first_name,''), ' ', IFNULL(sa.last_name,'')) as STUDENT_NAME, " .
			"sy.picture_url as STUDENT_PHOTO, " .
			"sa.admission_no as ADMISSION_NO, " .
			"sy.roll_no as ROLL_NO, " .
			"c.class_name as CLASS_NAME, " .
			"cs.section_name as SECTION_NAME, " .
			"sa.dob as DATE_OF_BIRTH, " .
			"sa.gender as GENDER, " .
			"sa.blood_group as BLOOD_GROUP, " .
			"sa.religion as RELIGION, " .
			"sa.caste as CASTE, " .
			"sa.category as CATEGORY, " .
			"sa.mother_tongue as MOTHER_TONGUE, " .
			"sa.nationality as NATIONALITY, " .
			"CONCAT(IFNULL(pf.first_name,''), ' ', IFNULL(pf.last_name,'')) as FATHER_NAME, " .
			"pf.picture_url as FATHER_PHOTO, " .
			"pf.mobile_no as FATHER_CONTACT_NO, " .
			"pf.email as FATHER_EMAIL, " .
			"pf.occupation as FATHER_OCCUPATION, " .
			"pf.qualification as FATHER_QUALIFICATION, " .
			"CONCAT(IFNULL(pm.first_name,''), ' ', IFNULL(pm.last_name,'')) as MOTHER_NAME, " .
			"pm.picture_url as MOTHER_PHOTO, " .
			"pm.mobile_no as MOTHER_CONTACT_NO, " .
			"pm.email as MOTHER_EMAIL, " .
			"pm.occupation as MOTHER_OCCUPATION, " .
			"pm.qualification as MOTHER_QUALIFICATION"
		);

		if ($studentData) {
			// Process photo URLs
			if (!empty($studentData->STUDENT_PHOTO)) {
				$studentData->STUDENT_PHOTO = $this->filemanager->getFilePath($studentData->STUDENT_PHOTO);
			} else {
				$studentData->STUDENT_PHOTO = base_url('assets/img/icons/profile.png');
			}

			if (!empty($studentData->FATHER_PHOTO)) {
				$studentData->FATHER_PHOTO = $this->filemanager->getFilePath($studentData->FATHER_PHOTO);
			} else {
				$studentData->FATHER_PHOTO = base_url('assets/img/icons/profile.png');
			}

			if (!empty($studentData->MOTHER_PHOTO)) {
				$studentData->MOTHER_PHOTO = $this->filemanager->getFilePath($studentData->MOTHER_PHOTO);
			} else {
				$studentData->MOTHER_PHOTO = base_url('assets/img/icons/profile.png');
			}

			return (array) $studentData;
		}

		return array('error' => 'No student data found');
	}

	/**
	 * Process photo fields to generate proper URLs
	 */
	private function processPhotoFieldsInResult($result, $listDataInput) {
		if (!is_object($result)) {
			return $result;
		}

		foreach ($listDataInput as $item) {
			if (isset($item['field']) && isset($result->{$item['field']})) {
				// Check if this is a photo field (contains 'PHOTO' in field name)
				if (strpos($item['field'], 'PHOTO') !== false) {
					$photoPath = $result->{$item['field']};
					if (!empty($photoPath)) {
						$result->{$item['field']} = $this->filemanager->getFilePath($photoPath);
					} else {
						$result->{$item['field']} = base_url('assets/img/icons/profile.png');
					}
				}
			}
		}

		return $result;
	}


	/**
	 * Get profile display configuration from settings
	 */
	private function getProfileDisplayConfiguration() {
		$config = array();

		// Get enabled profile fields from settings
		if (!empty($this->config->schoolSettings['parent_profile_display_columns']['value'])) {
			$config['enabled_fields'] = json_decode($this->config->schoolSettings['parent_profile_display_columns']['value'], true);
		} else {
			// Default configuration if not set
			$config['enabled_fields'] = array(
				'STUDENT_NAME', 'STUDENT_PHOTO', 'ADMISSION_NO', 'CLASS_SECTION',
				'STUDENT_DOB', 'STUDENT_GENDER', 'STUDENT_EMAIL', 'STUDENT_MOBILE_NUMBER'
			);
		}

		// Get field labels and display order
		$config['field_labels'] = $this->getFieldLabels();
		$config['field_groups'] = $this->getFieldGroups();

		return $config;
	}

	/**
	 * Load additional profile data based on configuration
	 */
	private function loadAdditionalProfileData($data, $studentId) {
		$config = $data['profileDisplayConfig'];
		$enabledFields = $config['enabled_fields'];

		// Check if we need to load address data
		if (in_array('STUDENT_ADDRESS', $enabledFields) || in_array('FATHER_ADDRESS', $enabledFields) || in_array('MOTHER_ADDRESS', $enabledFields)) {
			$data = $this->loadAddressData($data, $studentId);
		}

		// Check if we need to load parent data
		if (array_intersect(['FATHER_NAME', 'FATHER_EMAIL', 'FATHER_CONTACT_NO', 'FATHER_PHOTO'], $enabledFields)) {
			$data['fatherData'] = $this->parent_model->getFatherDetails($studentId);
		}

		if (array_intersect(['MOTHER_NAME', 'MOTHER_EMAIL', 'MOTHER_CONTACT_NO', 'MOTHER_PHOTO'], $enabledFields)) {
			$data['motherData'] = $this->parent_model->getMotherDetails($studentId);
		}

		// Check if we need to load guardian data
		if (array_intersect(['GUARDIAN_NAME', 'GUARDIAN_EMAIL', 'GUARDIAN_CONTACT_NO', 'GUARDIAN_PHOTO'], $enabledFields)) {
			$data['show_guardian'] = 1;
			$data['guardianData'] = $this->parent_model->getGuardianDetails($studentId);
		} else {
			$data['show_guardian'] = 0;
		}

		// Load electives if enabled
		if (in_array('ELECTIVES', $enabledFields)) {
			$data['electives'] = $this->parent_model->getElectives($studentId);
		}

		// Load stops data for pickup information
		if (in_array('STUDENT_STOP', $enabledFields)) {
			$stops = $this->parent_model->getFeesStops();
			$data['stops'] = array();
			foreach ($stops as $key => $stop) {
				$data['stops'][$stop->id] = $stop->name;
			}
		}

		return $data;
	}

	/**
	 * Load address data for student, father, mother
	 */
	private function loadAddressData($data, $studentId) {
		// Student address
		$student_address_types = $this->settings->getSetting('student_address_types', 1, 1);
		$sAddress = [];
		if (!empty($student_address_types)) {
			foreach ($student_address_types as $key => $address) {
				$sAddress[$address] = $this->parent_model->getStudent_Address_Details($studentId, $key);
			}
		}
		$data['studentAddress'] = $sAddress;

		// Father address
		if (isset($data['fatherData'])) {
			$father_address_types = $this->settings->getSetting('father_address_types');
			$fAddress = [];
			if (!empty($father_address_types)) {
				foreach ($father_address_types as $key => $address) {
					$fAddress[$address] = $this->parent_model->getFather_Address_Details($data['fatherData']->id, $key);
				}
			}
			$data['fatherAddress'] = $fAddress;
		}

		// Mother address
		if (isset($data['motherData'])) {
			$mother_address_types = $this->settings->getSetting('mother_address_types');
			$mAddress = [];
			if (!empty($mother_address_types)) {
				foreach ($mother_address_types as $key => $address) {
					$mAddress[$address] = $this->parent_model->getFather_Address_Details($data['motherData']->id, $key);
				}
			}
			$data['motherAddress'] = $mAddress;
		}

		return $data;
	}

	/**
	 * Get field labels for display
	 */
	private function getFieldLabels() {
		return array(
			'STUDENT_NAME' => 'Student Name',
			'STUDENT_PHOTO' => 'Photo',
			'ADMISSION_NO' => 'Admission Number',
			'ENROLLMENT_NUMBER' => 'Enrollment Number',
			'ALPHA_ROLL_NUMBER' => 'Alpha Roll Number',
			'CLASS_SECTION' => 'Class / Section',
			'STUDENT_DOB' => 'Date of Birth',
			'STUDENT_GENDER' => 'Gender',
			'STUDENT_EMAIL' => 'Email ID',
			'STUDENT_MOBILE_NUMBER' => 'Mobile Number',
			'STUDENT_BLOOD_GROUP' => 'Blood Group',
			'STUDENT_NATIONALITY' => 'Nationality',
			'STUDENT_RELIGION' => 'Religion',
			'STUDENT_CASTE' => 'Caste',
			'CATEGORY' => 'Category',
			'STUDENT_MOTHER_TONGUE' => 'Mother Tongue',
			'STUDENT_STOP' => 'Stop',
			'STUDENT_PICKUP_MODE' => 'Pickup Mode',
			'STUDENT_ADDRESS' => 'Address',
			'STUDENT_HOUSE' => 'House',
			'COMBINATION' => 'Combination',
			'FATHER_NAME' => 'Father Name',
			'FATHER_EMAIL' => 'Father Email',
			'FATHER_CONTACT_NO' => 'Father Contact',
			'MOTHER_NAME' => 'Mother Name',
			'MOTHER_EMAIL' => 'Mother Email',
			'MOTHER_CONTACT_NO' => 'Mother Contact',
			'GUARDIAN_NAME' => 'Guardian Name',
			'GUARDIAN_EMAIL' => 'Guardian Email',
			'GUARDIAN_CONTACT_NO' => 'Guardian Contact',
			'ELECTIVES' => 'Electives',
			'FAMILY_PHOTO' => 'Family Photo',
			'STUDENT_REMARKS' => 'Remarks'
		);
	}

	/**
	 * Get field groups for organized display
	 */
	private function getFieldGroups() {
		return array(
			'student_basic' => array(
				'title' => 'Student Information',
				'icon' => 'fa-user',
				'fields' => array('STUDENT_NAME', 'STUDENT_PHOTO', 'ADMISSION_NO', 'ENROLLMENT_NUMBER', 'ALPHA_ROLL_NUMBER', 'CLASS_SECTION')
			),
			'student_personal' => array(
				'title' => 'Personal Details',
				'icon' => 'fa-id-card',
				'fields' => array('STUDENT_DOB', 'STUDENT_GENDER', 'STUDENT_BLOOD_GROUP', 'STUDENT_NATIONALITY', 'STUDENT_RELIGION', 'STUDENT_CASTE', 'CATEGORY', 'STUDENT_MOTHER_TONGUE')
			),
			'student_contact' => array(
				'title' => 'Contact Information',
				'icon' => 'fa-phone',
				'fields' => array('STUDENT_EMAIL', 'STUDENT_MOBILE_NUMBER', 'PREFFERED_CONTACT_NUMBER')
			),
			'student_academic' => array(
				'title' => 'Academic Information',
				'icon' => 'fa-graduation-cap',
				'fields' => array('STUDENT_HOUSE', 'COMBINATION', 'ELECTIVES')
			),
			'student_transport' => array(
				'title' => 'Transport Information',
				'icon' => 'fa-bus',
				'fields' => array('STUDENT_STOP', 'STUDENT_PICKUP_MODE')
			),
			'student_address' => array(
				'title' => 'Address Information',
				'icon' => 'fa-map-marker',
				'fields' => array('STUDENT_ADDRESS')
			),
			'father_info' => array(
				'title' => 'Father Information',
				'icon' => 'fa-male',
				'fields' => array('FATHER_NAME', 'FATHER_EMAIL', 'FATHER_CONTACT_NO', 'FATHER_PHOTO', 'FATHER_ADDRESS')
			),
			'mother_info' => array(
				'title' => 'Mother Information',
				'icon' => 'fa-female',
				'fields' => array('MOTHER_NAME', 'MOTHER_EMAIL', 'MOTHER_CONTACT_NO', 'MOTHER_PHOTO', 'MOTHER_ADDRESS')
			),
			'guardian_info' => array(
				'title' => 'Guardian Information',
				'icon' => 'fa-users',
				'fields' => array('GUARDIAN_NAME', 'GUARDIAN_EMAIL', 'GUARDIAN_CONTACT_NO', 'GUARDIAN_PHOTO')
			),
			'additional_info' => array(
				'title' => 'Additional Information',
				'icon' => 'fa-info-circle',
				'fields' => array('FAMILY_PHOTO', 'STUDENT_REMARKS')
			)
		);
	}


	/**
	 * Get comprehensive student data based on configured columns
	 */
	private function getComprehensiveStudentData($studentId, $parentColumns, $displayColumns) {
		$yearId = $this->acad_year->getAcadYearId();

		// Build dynamic select query based on parent_columns configuration
		$selectFields = array();
		$joinTables = array();

		foreach ($parentColumns as $column) {
			if (in_array($column['column_name'], $displayColumns) && !empty($column['table_column_name'])) {
				$selectFields[] = $column['table_column_name'] . ' as ' . $column['column_name'];

				// Track required joins based on table prefixes
				$this->addRequiredJoins($column['table_column_name'], $joinTables);
			}
		}

		if (empty($selectFields)) {
			return null;
		}

		// Build the query
		$this->db_readonly->select(implode(', ', $selectFields));
		$this->db_readonly->from('student_admission sa');
		$this->db_readonly->join('student_year sy', 'sa.id = sy.student_admission_id');

		// Add dynamic joins based on required tables
		foreach ($joinTables as $join) {
			$this->db_readonly->join($join['table'], $join['condition'], $join['type']);
		}

		$this->db_readonly->where('sa.id', $studentId);
		$this->db_readonly->where('sy.acad_year_id', $yearId);

		$result = $this->db_readonly->get()->row_array();

		if ($result) {
			// Process file URLs for photo fields
			$result = $this->processPhotoFields($result, $parentColumns);
		}

		return $result;
	}

	/**
	 * Add required joins based on table column references
	 */
	private function addRequiredJoins($tableColumn, &$joinTables) {
		// Define join mappings based on table prefixes
		$joinMappings = array(
			'c.' => array('table' => 'class c', 'condition' => 'sy.class_id = c.id', 'type' => 'left'),
			'cs.' => array('table' => 'class_section cs', 'condition' => 'sy.class_section_id = cs.id', 'type' => 'left'),
			'cmc.' => array('table' => 'class_master_combinations cmc', 'condition' => 'sy.combination_id = cmc.id', 'type' => 'left'),
		);

		foreach ($joinMappings as $prefix => $joinInfo) {
			if (strpos($tableColumn, $prefix) !== false) {
				$joinKey = $joinInfo['table'];
				if (!isset($joinTables[$joinKey])) {
					$joinTables[$joinKey] = $joinInfo;
				}
			}
		}
	}

	/**
	 * Process photo fields to generate proper URLs
	 */
	private function processPhotoFields($result, $parentColumns) {
		foreach ($parentColumns as $column) {
			if ($column['data_input'] === 'file' && isset($result[$column['column_name']])) {
				$photoPath = $result[$column['column_name']];
				if (!empty($photoPath)) {
					$result[$column['column_name']] = $this->filemanager->getFilePath($photoPath);
				} else {
					$result[$column['column_name']] = base_url('assets/img/icons/profile.png');
				}
			}
		}

		return $result;
	}

	/**
	 * Filter student data based on enabled configuration
	 */
	private function filterStudentDataByConfig($studentData, $enabledColumns) {
		if (empty($enabledColumns)) {
			return $studentData; // Return all data if no configuration
		}

		$filtered = new stdClass();

		// Map of field names to student data properties
		$fieldMapping = array(
			'STUDENT_NAME' => 'stdName',
			'STUDENT_PHOTO' => 'picture_url',
			'ADMISSION_NO' => 'admissionNo',
			'ENROLLMENT_NUMBER' => 'enrollment_number',
			'ALPHA_ROLL_NUMBER' => 'alpha_rollnum',
			'CLASS_SECTION' => array('className', 'sectionName'),
			'STUDENT_DOB' => 'dob',
			'STUDENT_GENDER' => 'gender',
			'STUDENT_EMAIL' => 'student_email',
			'STUDENT_MOBILE_NUMBER' => 'student_mobile_no',
			'STUDENT_BLOOD_GROUP' => 'blood_group',
			'STUDENT_NATIONALITY' => 'nationality',
			'STUDENT_RELIGION' => 'religion',
			'STUDENT_CASTE' => 'caste',
			'CATEGORY' => 'category',
			'STUDENT_MOTHER_TONGUE' => 'mother_tongue',
			'STUDENT_STOP' => 'stop',
			'STUDENT_PICKUP_MODE' => 'pickup_mode',
			'STUDENT_HOUSE' => 'student_house',
			'COMBINATION' => 'combination_name',
			'STUDENT_REMARKS' => 'student_remarks'
		);

		// Copy enabled fields
		foreach ($enabledColumns as $field) {
			if (isset($fieldMapping[$field])) {
				$mapping = $fieldMapping[$field];
				if (is_array($mapping)) {
					// Handle composite fields like CLASS_SECTION
					$values = array();
					foreach ($mapping as $prop) {
						if (isset($studentData->$prop)) {
							$values[] = $studentData->$prop;
						}
					}
					$filtered->$field = implode(' / ', array_filter($values));
				} else {
					// Handle single fields
					if (isset($studentData->$mapping)) {
						$filtered->$field = $studentData->$mapping;
					}
				}
			}
		}

		// Always include essential fields for functionality
		$essentialFields = array('id', 'stdYearId', 'profile_status', 'profile_confirmed', 'profile_confirmed_date', 'profile_status_changed_date');
		foreach ($essentialFields as $field) {
			if (isset($studentData->$field)) {
				$filtered->$field = $studentData->$field;
			}
		}

		// Include nested data if enabled
		if (in_array('STUDENT_ADDRESS', $enabledColumns) && isset($studentData->studentAddress)) {
			$filtered->studentAddress = $studentData->studentAddress;
		}

		if (in_array('FATHER_ADDRESS', $enabledColumns) && isset($studentData->fatherAddress)) {
			$filtered->fatherAddress = $studentData->fatherAddress;
		}

		if (in_array('MOTHER_ADDRESS', $enabledColumns) && isset($studentData->motherAddress)) {
			$filtered->motherAddress = $studentData->motherAddress;
		}

		if (in_array('ELECTIVES', $enabledColumns) && isset($studentData->electives)) {
			$filtered->electives = $studentData->electives;
		}

		return $filtered;
	}

	/**
	 * Legacy method for backward compatibility
	 */
	public function info_for_desktop_mobile() {
		$data = $this->load_common_data();
		$data['main_content'] = 'dashboard/parent/common/info_message';

		$deviceType = $this->detect_device_type();
		switch($deviceType) {
			case 'mobile':
				$this->load->view('dashboard/parent/layouts/mobile/main', $data);
				break;
			case 'tablet':
				$this->load->view('dashboard/parent/layouts/tablet/main', $data);
				break;
			case 'desktop':
			default:
				$this->load->view('dashboard/parent/layouts/desktop/main', $data);
				break;
		}
	}

	/**
	 * Legacy method for backward compatibility - redirects to desktop_dashboard
	 */
	public function desktop() {
		$this->desktop_dashboard();
	}

	/**
	 * Private helper methods for statistics
	 */
	private function _getLessonsCount($class_section_id) {
		// This would typically come from timetable or lesson tracking
		// For now, return a sample count based on class section
		$base_count = 200;
		$variation = $class_section_id % 50; // Add some variation based on class section ID
		return $base_count + $variation;
	}

	private function _getAssignmentsCount($class_section_id) {
		$homework_count = $this->parent_model->getTodaysHomework($class_section_id);
		$count = isset($homework_count->hwCount) ? $homework_count->hwCount : 0;
		// If no homework today, get a weekly count as fallback
		if ($count == 0) {
			$weekly_count = $this->parent_model->getWeeklyHomeworkCount($class_section_id);

			$count = isset($weekly_count) ? $weekly_count : 25;
		}

		return $count;
	}

	private function _getAvgDayCount($student_id) {
		// This would calculate average attendance or performance
		// Try to get actual attendance percentage
		$attendance_data = $this->parent_model->getStudentAttendancePercentage($student_id);
		if ($attendance_data && $attendance_data->percentage) {
			return round($attendance_data->percentage);
		}

		// Fallback to a reasonable default
		return 84;
	}

	private function _getDaysCount($student_id) {
		// Calculate days since start of academic year or term
		$acad_year_start = $this->acad_year->getAcadYearStartDate();
		if ($acad_year_start) {
			$start_date = new DateTime($acad_year_start);
			$current_date = new DateTime();
			$interval = $start_date->diff($current_date);
			return $interval->days;
		}

		// Fallback to a sample count
		return 12;
	}

	private function _getEventCategoryClass($event_type) {
		$categories = array(
			'Information' => 'information',
			'Holiday' => 'holiday',
			'Money' => 'money',
			'Wednesday' => 'wednesday'
		);
		return isset($categories[$event_type]) ? $categories[$event_type] : 'information';
	}

	/**
	 * Mobile-specific text
	 */
	public function text() {
		$data = $this->load_common_data();
		$data = $this->input->get('moduleName');
		$data['main_content'] = 'dashboard/parent/pages/common/inner_pages_header.php';
		$this->load->view('dashboard/parent/layouts/mobile/inner_main', $data);
	}

	public function get_textsStudentIdwise(){
		$parentId = $this->authorization->getAvatarStakeHolderId();

		// Get pagination parameters
		$page = $this->input->post('page') ? (int)$this->input->post('page') : 1;
		$limit = $this->input->post('limit') ? (int)$this->input->post('limit') : 50;
		$offset = ($page - 1) * $limit;

		try {
			// Get paginated text data
			$text_data = $this->parent_model->get_textsStudentIdwise($parentId, $limit, $offset);

			// Return response with pagination info
			echo json_encode(array(
				'status' => 'success',
				'text_data' => $text_data,
				'page' => $page,
				'limit' => $limit,
				'has_more' => count($text_data) === $limit
			));

		} catch (Exception $e) {
			echo json_encode(array(
				'status' => 'error',
				'message' => 'Failed to load messages',
				'text_data' => array()
			));
		}
	}

	/**
	 * Calendar page with device detection
	 */
	public function calendar() {
		$data = $this->load_common_data();

		// Get student data for calendar
		$studentId = $data['studentId'];
		if (!$this->validate_student_access($studentId)) {
			show_404();
			return;
		}

		// Get calendar date range for the student
		$calendar_date_range = $this->getStudentCalendarDateRange($studentId);
		$data['calendar_start_date'] = $calendar_date_range['start_date'];
		$data['calendar_end_date'] = $calendar_date_range['end_date'];

		// Get student board for filtering events
		$data['student_board'] = $this->parent_model->getStudentBoard($studentId);

		// Get current month events for initial load
		$current_month = date('Y-m');
		$data['current_month'] = $current_month;
		$data['current_month_display'] = date('M Y');

		$deviceType = $this->detect_device_type();
		switch($deviceType) {
			case 'mobile':
				// For mobile, use inner layout like text page
				$data['main_content'] = 'dashboard/parent/pages/calendar/mobile_calendar';
				$this->load->view('dashboard/parent/layouts/mobile/inner_main', $data);
				break;
			case 'tablet':
				$data['main_content'] = 'dashboard/parent/pages/calendar/tablet_calendar';
				$this->load->view('dashboard/parent/layouts/tablet/main', $data);
				break;
			case 'desktop':
			default:
				$data['main_content'] = 'dashboard/parent/pages/calendar/desktop_calendar';
				$this->load->view('dashboard/parent/layouts/desktop/main', $data);
				break;
		}
	}

	/**
	 * Get calendar events for AJAX requests
	 */
	public function get_calendar_events() {
		$studentId = $this->input->post('student_id');
		if (!$studentId) {
			$studentId = $this->parent_model->getStudentIdOfLoggedInParent();
		}

		if (!$this->validate_student_access($studentId)) {
			echo json_encode(array('error' => 'Access denied'));
			return;
		}

		$date = $this->input->post('date');
		$state = $this->input->post('state');
		$student_board = $this->input->post('student_board');

		if (!$date) {
			$date = date('Y-m');
		}

		// Handle navigation
		if ($state == 'next') {
			$date = date('Y-m', strtotime('+1 month', strtotime($date)));
		} else if ($state == 'prev') {
			$date = date('Y-m', strtotime('-1 month', strtotime($date)));
		}

		$parent = 2;
		$displayDate = date('M Y', strtotime($date));

		// Get events data
		$eventData = $this->calenderevents_model->get_month_events_for_parents($date, $parent, $student_board, $studentId);

		// Format events for response
		$arr = array();
		foreach ($eventData as $event) {
			$key = $event->fDate;
			$eventOn = $event->fDate . ' (' . $event->fDay . ')';
			if ($event->tDate != '') {
				$key .= '-' . $event->tDate;
				$eventOn .= ' to ' . $event->tDate . ' (' . $event->tDay . ')';
			}
			if (!array_key_exists($key, $arr)) {
				$arr[$key]['event_type'] = $event->event_type;
				$arr[$key]['event_on'] = $eventOn;
				$arr[$key]['names'] = array();
				$arr[$key]['board'] = array();
			}
			array_push($arr[$key]['names'], $event->event_name);
			$arr[$key]['board'][$event->event_name] = $event->board;
		}

		$events = array();
		foreach ($arr as $value) {
			array_push($events, $value);
		}

		echo json_encode(array(
			'events' => $events,
			'displayDate' => $displayDate,
			'date' => $date,
			'status' => 'success'
		));
	}

	/**
	 * Get student calendar date range (copied from Parent_controller)
	 */
	private function getStudentCalendarDateRange($studentId) {
		// Get student's class section ID
		$student_class_section = $this->db->select("class_section_id")
			->from("student_year")
			->where("student_admission_id", $studentId)
			->where("acad_year_id", $this->yearId)
			->get()->row();

		if (empty($student_class_section)) {
			// Fallback to academic year if no calendar assigned
			$academic_start_month = (int)$this->settings->getSetting('academic_start_month');
			if($academic_start_month == '' || $academic_start_month > 12 || $academic_start_month < 1) {
				$academic_start_month = 3; // Default to March
			}
			$acad_year = explode("-", $this->acad_year->getAcadYear())[0];
			return array(
				'start_date' => $acad_year . '-' . sprintf('%02d', $academic_start_month) . '-01',
				'end_date' => ($acad_year + 1) . '-' . sprintf('%02d', $academic_start_month - 1) . '-28'
			);
		}

		// Get assigned calendar details
		$calendar = $this->db->select('cm.start_date, cm.end_date')
			->from('calendar_v2_master cm')
			->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = cm.id')
			->where('cea.assigned_section_id', $student_class_section->class_section_id)
			->where('cea.assigned_type', 'SEC')
			->where('cm.academic_year', $this->yearId)
			->get()->row();
		if ($calendar) {
			return array(
				'start_date' => $calendar->start_date,
				'end_date' => $calendar->end_date
			);
		} else {
			// Fallback to academic year if no calendar assigned
			$academic_start_month = (int)$this->settings->getSetting('academic_start_month');
			if($academic_start_month == '' || $academic_start_month > 12 || $academic_start_month < 1) {
				$academic_start_month = 3; // Default to March
			}
			$acad_year = explode("-", $this->acad_year->getAcadYear())[0];
			return array(
				'start_date' => $acad_year . '-' . sprintf('%02d', $academic_start_month) . '-01',
				'end_date' => ($acad_year + 1) . '-' . sprintf('%02d', $academic_start_month - 1) . '-28'
			);
		}
	}


	public function circular_inbox(){
		$data = $this->load_common_data();
		$data = $this->input->get('moduleName');
		$data['parent_id'] = $this->authorization->getAvatarStakeHolderId();
		// $data['student_id'] = $this->parent_model->getStudentIdOfLoggedInParent();
		$data['student_id'] = $this->parent_model->get_student_id_by_user_id($data['parent_id']);
		// echo "<pre>";print_r($data['student_id']);die();
		$modules = json_decode($this->settings->getSetting('deactivation_modules'));
		$is_deactivated = $this->parent_model->checkStudentPartiallyDeactivated($data['student_id']);
		$data['is_deactivated'] = 0;
		if(!empty($modules)){
			if($is_deactivated && in_array('Circulars', $modules)) {
				$data['is_deactivated'] = 1;
			}
		}
		
		$this->load->model('communication/circular_model', 'circular');
		$data['categories'] = $this->circular->getCircularCategories();

		$data['main_content'] = 'dashboard/parent/pages/common/circular_inbox.php';
		$this->load->view('dashboard/parent/layouts/mobile/inner_main', $data);

	}

	public function update_profile_confirmed()
	{
		$this->parent_model->store_edit_history('','Profile Confirmed');
		$result = $this->parent_model->update_profile_confirmedbyid($_POST['stdYearId']);
		echo json_encode($result);
	}

}

?>
