<div id="uploadEvaluation" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
    <div class="modal-dialog" style="margin:auto;top:3%">
      <?php if($this->mobile_detect->isMobile()) { ?>
        	<div class="modal-content" style="width:100%;margin:auto;margin-top:8%;border-radius: .75rem;">
        <?php } else { ?>
        	<div class="modal-content" style="width:50%;margin:auto;margin-top:8%;border-radius: .75rem;">
        <?php } ?>
        <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
            <h4 class="modal-title" id="modalHeader">Add Evaluation <span id="eval-upload"></span></h4>
        </div>
        <div class="modal-body">
        <form enctype="multipart/form-data" method="post" id="evaluation-form"  data-parsley-validate="" class="form-horizontal">
          	<div class="card-body" id="recording-data1"> 
	            <div class="col-md-12 pb-3">
					<input type="hidden" name="submission_file_id" id="submission_file_id">
					<input type="hidden" id="upload_lp_tasks_student_id">
	              	<div class="form-group">
	                	<label class="col-md-2">Upload File</label>
	              		<div class="col-md-10">
	              			<input type="file" name="evaluationFile" id="evaluationFile">
	              		</div>
	              	</div>
	              	<center>
	                	<button type="button" onclick="saveEvaluationFile()" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary">Submit</button>
	                	<button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
	              	</center>
	            </div>
          	</div>
        </form>
      </div>
    </div>
</div>
</div>

<div class="modal fade" id="resubmission-modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <?php if($this->mobile_detect->isMobile()) { ?>
        	<div class="modal-content" style="width:100%;margin:auto;margin-top:8%;border-radius: .75rem;">
        <?php } else { ?>
        	<div class="modal-content" style="width:60%;margin:auto;margin-top:8%;border-radius: .75rem;">
        <?php } ?>
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Ask for re-submission (<span id="re-std-name"></span>)</h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
                <div class="col-md-12">
                	<input type="hidden" id="re_std_task_id">
            		<div class="form-group">
                        <label class="col-md-3 pr-0" style="text-align: right;">Comments <span style="opacity: 0;">PP</span></label>
                        <div class="col-md-9 pl-0 mb-3">
                            <textarea rows="4" placeholder="Enter Comments for the Re-submission" class="form-control" id="resubmission_comments"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                    	<center>
                    		<a style="width:10rem;" type="button" class="btn btn-primary mr-1" onclick="sendForResubmission()">Confirm</a>
        					<button class="btn btn-danger" style="width: 10rem;" data-dismiss="modal">Cancel</button>
                    	</center>
                    </div>
                </div>
            </div> 
        </div>
    </div>
</div>

<div class="modal fade" id="editimage" role="dialog">
    <div class="modal-dialog" style="margin: auto;width: 100%">
        <div class="modal-content" style="width: 100%;margin:auto;border-radius: .75rem;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">Edit <span id="edit-image"></span></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body">
            	<div class="d-flex justify-content-center align-items-center">
            		<i id="canvas-loader" class="fa fa-spinner fa-spin" style="font-size: 40px;display: none;"></i>
            	</div>
            	<div id="edit-tools" style="margin:auto;text-align: center;">
            		<!-- <button data-toggle="tooltip" data-original-title="Zoom-In" class="btn btn-secondary m-1"><i class="fa fa-search-plus"></i></button>
            		<button data-toggle="tooltip" data-original-title="Zoom-Out" class="btn btn-secondary m-1"><i class="fa fa-search-minus"></i></button> -->
            		<button onclick="rotateCanvas()" data-toggle="tooltip" data-original-title="Rotate" class="btn btn-secondary m-1"><i class="fa fa-rotate-right"></i></button>
					<input type="color" name="pen_color" id="edit_pen_color" value="#e75480">
            	</div>
                <center>
                	<?php if($this->mobile_detect->isMobile()) { ?>
			        	<canvas id="can" height="700"></canvas>
			        <?php } else { ?>
                		<canvas id="can"  width="700" height="500"></canvas>
			        <?php } ?>
                </center>
                <span id="showimage">
                	
                </span>
                <input type="hidden" name="submission_file_id" id="edit_submission_file_id">
                <input type="hidden" id="edit_lp_tasks_student_id">
            </div> 
            <div class="modal-footer">
            	<button onclick="save_edited_image()" class="btn btn-success" id="save_photo">Save</button>
        		<button class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('commons/file_downloader.php'); ?>
<style type="text/css">
	.faq-links {
		text-decoration: none !important;
	}
	.btn {
		border-radius: 1.2rem !important;
    	padding: 4px 14px !important;
	}
	.modal-footer>* {
	    margin: 0px;
	}
</style>
<script type="text/javascript">
	var is_mobile = 0;
	var selected_student = {};
	var student_objects = {};
	// var acc = document.getElementsByClassName("accordion");
	// var i;

	// for (i = 0; i < acc.length; i++) {
	// 	// alert('in')
	//   acc[i].addEventListener("click", function() {
	//     this.classList.toggle("active");
	//     var panel = this.nextElementSibling;
	//     if (panel.style.maxHeight) {
	//       panel.style.maxHeight = null;
	//     } else {
	//       panel.style.maxHeight = panel.scrollHeight + "px";
	//     } 
	//   });
	// }

	function getStudentSubmissionsV2(task_id) {
		selected_student = {};
		student_objects = {};
		is_mobile = <?php echo ($this->mobile_detect->isMobile())?1:0; ?>;
		var section_id = $("#section_id_main").val();
		var staff_id = $("#staff_list").val();
		var filter = $('#filter_type').val();
		// var school_name = '<?php //echo $this->settings->getSetting("school_short_name") ?>';
		var school_name = 'demoschool';
		var id = staff_id;
		if(filter == 'class') {
			id = section_id;
		}
		$('#details_'+task_id).removeClass('active');
		$('#submissions_'+task_id).addClass('active');
		$('#read_'+task_id).removeClass('active');
		$('#discard_'+task_id).removeClass('discard');
		$('#direct_evaluation_'+task_id).removeClass('active');
		$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getTaskSubmissionData') ?>',
			type:'post',
			data: {'task_id':task_id,'id':id, 'type': filter},
	        beforeSend: function() {
	        	$('#opacity').css('opacity','0.5');
		        $('#loader').show();
		    },
			success : function(data){
				var data = $.parseJSON(data);
				var submissions = data.submissions;
				var task_type = selected_task.task_type;
				var status = selected_task.status;
				var require_evaluation = parseInt(selected_task.require_evaluation);
				var table='';
				var donwload_url = '';
				var created_by=selected_task.task_created_by;
				if(status=='disabled'){
					$("#information").html('<div><h4 style="color:#888;">No details can be seen as the task is unpublished</h4></div>');
				} else{
					if(task_type == 'Reading' || task_type == 'Viewing' || task_type == 'Writing-NoSubmission'){
						$("#information").html('<div><h4 style="color:#888;">No Submissions/Evaluations defined for this task.</h4></div>');
					} else{
						if(submissions.length==0){
							$("#information").html('<div><h4 style="color:#888;">No Submissions are done</h4></div>');
						}
						else{
							table +='<div class="accordion md-accordion" id="accordionEx" role="tablist" aria-multiselectable="true">';
							for(var i=0;i<submissions.length;i++){
								table+='<div class="card mb-2" style="border: 1px;border-radius:8px;">';
								//hearder
								table+='<div class="card-header" role="tab" id="heading'+submissions[i].id+'">';
	      						table+='<a  class="faq-links" data-toggle="collapse" data-parent="#accordionEx" href="#collapse'+submissions[i].id+'" onclick="getSubmittedFilesData('+submissions[i].id+', 0)" aria-expanded="true" aria-controls="collapse'+submissions[i].id+'"><i id="faq-link-'+submissions[i].id+'" class="toggler fa fa-angle-right fa-1x float-left" style="font-size: 1.5rem;font-weight:700;"></i>';
	      						table+='<h5 class="mb-0 ">';
	      						if(submissions[i].evaluation_status == 1) {
	      							table += '<span style="font-size: 1.6rem;" class="pl-2 fa fa-check pull-right text-success"></span>';
	      						}
	      						table+='<b style="padding-left: .50rem;">'+submissions[i].class_name+submissions[i].section_name+' : '+submissions[i].student_name+'</b><span class="pull-right"><small>Submitted on: </small><strong style="color:#EC8100;">'+moment(submissions[i].submission_on).format('DD-MM-YYYY')+'</strong></span>';
	      						table+='</h5>';
	      						table+='</a>';
	      						table+='</div>';
	      	// 					if(submissions[i].resubmission_status == 1) {
								// 	table += '<p><span class="text-danger">Sent for re-submission:</span> <small>'+submissions[i].resubmission_comment+'</small></p>';
								// }
								var std = {
									lp_tasks_student_id: submissions[i].id, 
									task_type: task_type, 
									eval_status: submissions[i].evaluation_status,
									student_name: submissions[i].student_name,
									resubmission_status: submissions[i].resubmission_status
								};
								student_objects[submissions[i].id] = std;
								
	        					//body
	        					table+='<div id="collapse'+submissions[i].id+'" class="collapse in" role="tabpanel" aria-labelledby="heading'+submissions[i].id+'" data-parent="#accordionEx">';
	        					table+='<div class="unread_box_no_style_new" style="border-radius: 0px; padding: 0px !important;">'
	        					table+='<div class="card-body">';
	        					table+='<div id="submitted_files_'+submissions[i].id+'"></div>';
	        					table+='</div>';
	        					table+='</div>';
	        					table+='</div>';
	        					table+= '</div>';
							}
							table +='</div>';

							$("#information").html(table);
						}
					}
				}
			},
			complete: function() {
				$('#loader').hide();
				$('#opacity').css('opacity','');
			},
			error: function (err) {
				console.log(err);
			}
		});
	}

	function toggleArrow(lp_tasks_student_id) {
		$('.toggler').not('#faq-link-'+lp_tasks_student_id).removeClass('fa-angle-down').addClass('fa-angle-right');
		// $('.toggler').not('#faq-link-'+lp_tasks_student_id).toggleClass('fa-angle-down');
		$('#faq-link-'+lp_tasks_student_id).toggleClass('fa-angle-right');
		$('#faq-link-'+lp_tasks_student_id).toggleClass('fa-angle-down');
		/*$('.faq-link').find('i').toggleClass('fa-angle-down');
		$('.faq-link').find('i').toggleClass('fa-angle-right');
		$('#faq-link-'+lp_tasks_student_id).find('i').toggleClass('fa-angle-down');
	    $('#faq-link-'+lp_tasks_student_id).find('i').toggleClass('fa-angle-right');*/
	}

	function getSubmittedFilesData(lp_tasks_student_id, need_refresh=1){
		toggleArrow(lp_tasks_student_id);
		if(!need_refresh && $('#faq-link-'+lp_tasks_student_id).hasClass('fa-angle-right')) {
			//closing the accordian should not call get data
			return false;
		}
		selected_student = student_objects[lp_tasks_student_id];
		$('#submitted_files_'+lp_tasks_student_id+'').html('<div class="text-center"><i style="font-size:40px;" class="fa fa-spinner fa-spin"></i></div>');
		$.ajax({
			url: '<?php echo site_url('student_tasks/Tasks/getSubmittedFilesV2'); ?>',
			type: 'post',
			data: {'lp_tasks_student_id':lp_tasks_student_id},
	      	success: function(data) {
				var data = $.parseJSON(data);
				var html='';
				var submitted_files = data.submitted_files;
				var task_student = data.task_student;
				$('#submit_button_'+lp_tasks_student_id+'').hide();
				if(submitted_files.length!=0) {
					if(!parseInt(task_student.evaluation_status) && !parseInt(task_student.resubmission_status)) {
						html += '<button type="button" class="btn btn-sm mb-2 btn-warning" onclick="showResubmission('+lp_tasks_student_id+', \''+selected_student.student_name+'\')">Send For Re-submission</button>';
					} else if(parseInt(task_student.resubmission_status)) {
						html += '<p><span class="text-danger">Sent for Re-submission:</span> <small>'+task_student.resubmission_comment+'</small></p>';
					}
					/*if(parseInt(selected_task.require_evaluation) && !parseInt(task_student.evaluation_status) && !parseInt(task_student.resubmission_status)) {
						html += '<button type="button" class="btn btn-sm btn-warning" onclick="showResubmission('+lp_tasks_student_id+', \''+selected_student.student_name+'\')">Send For Re-submission</button>';
					}*/
					var images = ['jpg', 'jpeg', 'png', 'gif'];
					var submission_url, evaluation_url;
					html += '<table class="table table-bordered">';
					html += '<thead>';
					html += '<tr>';
					html += '<th style="width:25%;">Submissions</th><th style="width:25%;">Evaluations</th><th style="width:25%;">Submission Comments</th><th style="width:30%;">Action</th>';
					html += '</tr>';
					html += '</thead>';
					html += '<tbody>';
					for(var i=0;i<submitted_files.length;i++) {
						html += '<tr>';
						// html += '<td>'+(i+1)+'</td>';
						html += '<td>';
						if(selected_student.task_type == 'Reading-Audio-Submission') {
							html += '<audio controlsList="nodownload" controls="" src="'+submitted_files[i].file_path+'">';
						} else {
							submission_url = "<?php echo site_url('student_tasks/tasks/downloadSubmissionAttachment/')?>"+submitted_files[i].file_id;
							if(images.includes(submitted_files[i].file_type)) {
								html += '<span data-path="'+submitted_files[i].file_path+'" class="image-view new_circleShape_buttons" data-toggle="tooltip" data-originaltitle="'+submitted_files[i].file_name+'" data-view_title="'+submitted_files[i].file_name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>';
								/*html += '<a class="gallery-item new_circleShape_buttons"  href="' + submitted_files[i].file_path + '" title="'+submitted_files[i].file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';*/
							} else if(submitted_files[i].file_type == 'pdf') {
								// html+='<a class="new_circleShape_buttons" onclick="viewPdf(\''+submitted_files[i].file_path+'\')"><i class="fa fa-eye" style="color:#428bca;"></i></a> ';
								html+='<a class="new_circleShape_buttons" onclick="pdfViewer('+submitted_files[i].file_id+', \''+selected_student.student_name+'\', '+(i+1)+', '+lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
								html += '<input type="hidden" id="sub_view_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';
							} else {
								if(!is_mobile) {
									html+='<a class="new_circleShape_buttons" onclick="pdfViewer('+submitted_files[i].file_id+', \''+selected_student.student_name+'\', '+(i+1)+', '+lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';	
								}
								html += '<input type="hidden" id="sub_view_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';
							}
							if(is_mobile) {
								html += '<a class="ml-2 new_circleShape_buttons" href="'+submission_url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
							} else {
								html += '<span class="ml-2 new_circleShape_buttons file-downloader" data-name="'+selected_student.student_name+'-'+(i+1)+'" data-url="'+submitted_files[i].file_path+'"><i class="fa fa-download" style="color:#fe970a;"></i></span>&nbsp;&nbsp;';
							}
							if(is_mobile) {
								html += '<br><span class="pt-2" style="display:inline-block">';
							} else {
								html += '<span>';
							}
							html += 'File '+(i+1)+' ('+submitted_files[i].file_type+')</span>';
						}
						html += '</td>';
						html += '<td>';
						if(submitted_files[i].evaluation_id == 0) {
							html += '-';
						} else {
							evaluation_url = "<?php echo site_url('student_tasks/tasks/downloadSubmissionAttachment/')?>"+submitted_files[i].evaluation_file_id;
							if(images.includes(submitted_files[i].evaluation_file_type)) {
								html += '<span data-path="'+submitted_files[i].evaluation_file_path+'" class="image-view new_circleShape_buttons mx-2" data-toggle="tooltip" data-originaltitle="'+submitted_files[i].evaluation_file_name+'" data-view_title="'+submitted_files[i].evaluation_file_name+'"><i class="fa fa-eye" style="color:#428bca;"></i></span>&nbsp;&nbsp;';
								/*html += '<a class="gallery-item new_circleShape_buttons"  href="' + submitted_files[i].evaluation_file_path + '" title="'+submitted_files[i].evaluation_file_name+'" data-gallery=""><i class="fa fa-eye" style="color:#428bca;"></i></a>&nbsp;&nbsp;';*/
							} else if(submitted_files[i].evaluation_file_type == 'pdf') {
								html+='<a class="new_circleShape_buttons" onclick="pdfViewer('+submitted_files[i].evaluation_id+', \''+selected_student.student_name+'\', '+(i+1)+', '+lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
								html += '<input type="hidden" id="sub_view_'+submitted_files[i].evaluation_id+'" value="'+submitted_files[i].evaluation_file_path+'">';
							} else {
								// html+='<a class="new_circleShape_buttons" onclick="pdfViewer('+submitted_files[i].evaluation_id+', \''+selected_student.student_name+'\', '+(i+1)+', '+lp_tasks_student_id+')"><i class="fa fa-eye" style="color:#428bca;"></i></a>';
								html += '<input type="hidden" id="sub_view_'+submitted_files[i].evaluation_id+'" value="'+submitted_files[i].evaluation_file_path+'">';
							}
							html += '<a class="new_circleShape_buttons" href="'+evaluation_url+'"><i class="fa fa-download" style="color:#fe970a;"></i></a>&nbsp;&nbsp;';
							if(is_mobile) {
								html += '<br><span class="pt-2" style="display:inline-block">';
							} else {
								html += '<span>';
							}
							html += 'File '+(i+1)+' ('+submitted_files[i].evaluation_file_type+')</span>';
						}
						html += '</td>';
						html +='<td>' +task_student.submission_comment+ '</td>';
						html += '<td>';
						if(task_student.evaluation_status == 0 && task_student.resubmission_status == 0) {
							if(images.includes(submitted_files[i].file_type)) {
								html += '<a class="new_circleShape_buttons"  href="javascript:void(0)" onclick="editsingleimage('+submitted_files[i].file_id+', \''+selected_student.student_name+'\', '+(i+1)+', '+lp_tasks_student_id+')" data-toggle="modal" data-target="#editimage"><i class="fa fa-edit" style="color:#428bca;"></i></a>';
								html += '<input type="hidden" id="img_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';
							} else if(['pdf', 'PDF'].includes(submitted_files[i].file_type)) {
								html += '<a class="new_circleShape_buttons"  href="javascript:void(0)" onclick="editPDF('+submitted_files[i].file_id+', \''+selected_student.student_name+'\', '+(i+1)+', '+lp_tasks_student_id+')"><i class="fa fa-edit" style="color:#428bca;"></i></a>';
								html += '<input type="hidden" id="pdf_'+submitted_files[i].file_id+'" value="'+submitted_files[i].file_path+'">';
							}
							html += '&nbsp;&nbsp;<a href="javascript:void(0)" class="new_circleShape_buttons" onclick="uploadEvaluation('+submitted_files[i].file_id+', \''+selected_student.student_name+'\', '+(i+1)+')" data-toggle="modal" data-target="#uploadEvaluation"><i class="fa fa-upload" style="color:#fe970a;"></i></a>';

						} else {
							html += 'No Action';
						}
						html += '</td>';
						html += '</tr>';
					}
					html += '</tbody>';
					html += '</table>';

					if(task_student.evaluation_status == 0 && task_student.resubmission_status == 0) {
						html += '<div>';
						html += '<label class="control-label" style="font-size: 1rem;">Evaluation Comment: </label>';
						html += '<textarea rows="3" class="form-control" id="evaluation_comment_'+lp_tasks_student_id+'"></textarea>';
						html += '<button type="button" onclick="saveEvaluation('+lp_tasks_student_id+')" class="btn btn-primary mt-2 text-right">Submit Evaluation</button>';
						html += '</div>';
					} else if(task_student.evaluation_status == 1){
						html += '<p style="font-size:1rem;"><b>Evaluation Comment: </b>'+task_student.evaluation_comments;
						html += '<p style="font-size:1rem;"><b>Evaluated on: </b><strong style="color:#EC8100;">'+moment(task_student.evaluation_on).format('DD-MM-YYYY')+'</strong></p>';
						if(is_task_admin) {
							html += '<a  id="cancel_evaluation_'+lp_tasks_student_id+'"  onclick="undoEvaluation('+lp_tasks_student_id+')" class="btn btn-sm btn-danger">Cancel Evaluation</a>';
						}
					}
				} else {
					html += '<p><strong>No Files Uploaded</strong></p>';
				} 
				$('#submitted_files_'+lp_tasks_student_id+'').html(html);
			},
		});
	}

	function uploadEvaluation(submission_file_id, student_name, index, lp_tasks_student_id) {
		$("#submission_file_id").val(submission_file_id);
		$("#upload_lp_tasks_student_id").val(lp_tasks_student_id);
		$("#eval-upload").html(' of File'+index+' for <b>'+student_name);
		$("#evaluate-btn").attr('disabled', false);
	}

	function saveEvaluationFile() {
		var file_data = $('#evaluationFile').prop('files');
        if (file_data.length === 0) { 
            return false; 
        }
        var form_data = new FormData();
        var submission_file_id = $("#submission_file_id").val();
        var lp_tasks_student_id = $("#upload_lp_tasks_student_id").val();
        saveEvaluatedFile(file_data[0], submission_file_id, lp_tasks_student_id);
	}

	function saveEvaluation(lp_tasks_student_id) {
		swal({
	      title: "Are you sure?",
	      text: "Confirm evaluation",
	      icon: "warning",
	      buttons: true,
	      dangerMode: true,
	    })
	    .then((confirm) => {
	      if (confirm) {
	      	var task_id = $("#task_id_hidden").val();
	      	var comment = $("#evaluation_comment_"+lp_tasks_student_id).val();
	        $.ajax({
	          url: '<?php echo site_url('student_tasks/tasks/saveEvaluation'); ?>',
	          type: 'post',
	          data: {'lp_tasks_student_id':lp_tasks_student_id, 'comment':comment},
	          success: function(data) {
	            if(data){
	              swal({
	                title: "Successful",
	                text: "Saved successfully",
	                icon: "success",
	              });
	              getSubmittedFilesData(lp_tasks_student_id);
	            }
	            else{
	              swal({
	                title: "Error",
	                text: "Failed to save",
	                icon: "error",
	              });
	            }
	          }
	        })
	      }
	    });
	}
</script>

<script type="text/javascript">
	//Evaluation for images
	function editsingleimage(id, student_name, index, lp_tasks_student_id){
		init('img_'+id);
		var name = 'File '+index+ ' of '+ student_name;
		$("#edit_submission_file_id").val(id);
		$("#edit_lp_tasks_student_id").val(lp_tasks_student_id);
		$("#edit-image").html(name);
		$("#editimage").modal('show');
	}

	var canvas, ctx, flag = false,
        prevX = 0,
        currX = 0,
        prevY = 0,
        currY = 0,
        dot_flag = false,
        image_element,
		degree=0,
		pen_color = '#e75480';

    var x = "green",
        y = 2;

    function make_base(canvas, context, image_id)
    {
    	$("#canvas-loader").show();
        base_image = document.getElementById(image_id);  
        // var img = document.createElement('img');
        var img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = document.getElementById(image_id).value;
        img.onload = () => {
        	image_element = img;
        	drawCanvas();
        };
        // context.drawImage(base_image, 0, 0, 500,500);
    }

    function drawCanvas(deg=0) {
    	var image = image_element;
    	var imgWidth = image.naturalWidth;
	  	var imgHeight = image.naturalHeight;
		var screenWidth  = canvas.width;
		var screenHeight = canvas.height;
		var scaleX = 1;
		var scaleY = 1;
		var canvasContext = ctx;

		if (imgWidth > screenWidth) {
		    scaleX = screenWidth/imgWidth;
		}
		if (imgHeight > screenHeight) {
		    scaleY = screenHeight/imgHeight;
		}
		var scale = scaleY;
		if(scaleX < scaleY) {
		    scale = scaleX;
		}
		if(scale < 1){
		    imgHeight = imgHeight*scale;
		    imgWidth = imgWidth*scale;          
		}

		canvas.height = imgHeight;
		canvas.width = imgWidth;

		$("#canvas-loader").hide();
		ctx.clearRect(0,0,canvas.width,canvas.height);
		ctx.save();
		ctx.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight, 0,0, canvas.width, canvas.height);
		ctx.restore();
    }

    function rotateCanvas() {
    	if(degree == 270) {
    		degree = 0;
    	} else {
    		degree += 90;
		}

		var canvas_width = canvas.width;
		var canvas_height = canvas.height;
		var image_width = image_element.naturalWidth;
		var image_height = image_element.naturalHeight;
		switch(degree) {
			case 0: 
				ctx.clearRect(0,0,canvas.width,canvas.height);
				ctx.save();
				canvas.setAttribute('width',canvas_height);
				canvas.setAttribute('height',canvas_width);
				ctx.translate(canvas.width/2,canvas.height/2);
				ctx.rotate(degree*Math.PI/180);
				ctx.drawImage(image_element, 0, 0, image_width, image_height, -canvas.width/2, -canvas.height/2, canvas.width, canvas.height);
				ctx.restore();
				break;
			
			case 90: 
				ctx.clearRect(0,0,canvas.width,canvas.height);
				ctx.save();
				canvas.setAttribute('width',canvas_height);
				canvas.setAttribute('height',canvas_width);
				ctx.translate(canvas.width/2,canvas.height/2);
				ctx.rotate(degree*Math.PI/180);
				ctx.drawImage(image_element, 0, 0, image_width, image_height, -canvas.height/2, -canvas.width/2, canvas.height, canvas.width);
				ctx.restore();
				break;

			case 180: 
				ctx.clearRect(0,0,canvas.width,canvas.height);
				ctx.save();
				canvas.setAttribute('width',canvas_height);
				canvas.setAttribute('height',canvas_width);
				ctx.translate(canvas.width/2,canvas.height/2);
				ctx.rotate(degree*Math.PI/180);
				ctx.drawImage(image_element, 0, 0, image_width, image_height, -canvas.width/2, -canvas.height/2, canvas.width, canvas.height);
				ctx.restore();
				break;

			case 270:
				ctx.clearRect(0,0,canvas.width,canvas.height);
				ctx.save();
				canvas.setAttribute('width',canvas_height);
				canvas.setAttribute('height',canvas_width);
				ctx.translate(canvas.width/2,canvas.height/2);
				ctx.rotate(degree*Math.PI/180);
				ctx.drawImage(image_element, 0, 0, image_width, image_height, -canvas.height/2, -canvas.width/2, canvas.height, canvas.width);
				ctx.restore();
				break;
		}
		ctx.resetTransform();
	}

    function init(image_id) {
        canvas = document.getElementById('can');
        canvas.height = 700;
        if(!is_mobile) {
        	canvas.width = 700;
        	canvas.height = 500;
        }
        ctx = canvas.getContext("2d");
        w = canvas.width;
        h = canvas.height;
        ctx.clearRect(0, 0, w, h);
        make_base(canvas, ctx, image_id);
        canvas.addEventListener("mousemove", function (e) {
            findxy('move', e)
        }, false);
        canvas.addEventListener("mousedown", function (e) {
            findxy('down', e)
        }, false);
        canvas.addEventListener("mouseup", function (e) {
            findxy('up', e)
        }, false);
        canvas.addEventListener("mouseout", function (e) {
            findxy('out', e)
        }, false);

        canvas.addEventListener("touchstart", function (e) {
        	mousePos = getTouchPos(canvas, e);
			var touch = e.touches[0];
			var mouseEvent = new MouseEvent("mousedown", {
			    clientX: touch.clientX,
			    clientY: touch.clientY
			});
			canvas.dispatchEvent(mouseEvent);
		}, false);

		canvas.addEventListener("touchend", function (e) {
  			var mouseEvent = new MouseEvent("mouseup", {});
  			canvas.dispatchEvent(mouseEvent);
		}, false);

		canvas.addEventListener("touchmove", function (e) {
  			var touch = e.touches[0];
  			var mouseEvent = new MouseEvent("mousemove", {
    			clientX: touch.clientX,
    			clientY: touch.clientY
  			});
  			canvas.dispatchEvent(mouseEvent);
		}, false);
    }

    function getTouchPos(canvasDom, touchEvent) {
		var rect = canvasDom.getBoundingClientRect();
		return {
			x: touchEvent.touches[0].clientX - rect.left,
			y: touchEvent.touches[0].clientY - rect.top
		};
	}

	$("#edit_pen_color").change(function(){
		pen_color = $(this).val();
	});
    
    function draw() {
        ctx.beginPath();
        ctx.moveTo(prevX, prevY);
        ctx.lineTo(currX, currY);
        ctx.strokeStyle = pen_color;
        ctx.lineWidth = '2';
        ctx.lineCap = 'round';
        ctx.stroke();
        ctx.closePath();
    }
    
    function erase() {
        var m = confirm("Want to clear");
        if (m) {
            ctx.clearRect(0, 0, w, h);
            document.getElementById("canvasimg").style.display = "none";
        }
    }

    function saveEvaluatedFile(file, submission_file_id, lp_tasks_student_id) {
    	var form_data = new FormData();
        var task_id = $("#task_id_hidden").val();
        form_data.append('file', file);
        form_data.append('submission_file_id', submission_file_id);
        $.ajax({
            url: '<?php echo site_url('student_tasks/tasks/saveEvaluationFile') ?>',
            type: 'post',
            data: form_data,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                var status = parseInt(data);
                if (status == 0) {
                    $(function(){
                        new PNotify({
                            title: 'Error',
                            text: 'Failed to save',
                            type: 'error',
                        });
                    });
                } else {
                    $(function(){
                        new PNotify({
                            title: 'Success',
                            text: 'Saved successfully',
                            type: 'success',
                        });
                    });
                    $("#editimage").modal('hide');
                    $("#uploadEvaluation").modal('hide');
                    getSubmittedFilesData(lp_tasks_student_id);
                }
                closeModal();
                $("#save_photo").html('Save').attr('disabled', false);
            }
        });
    }

    function save_edited_image() { 
    	$("#save_photo").html('Saving...').attr('disabled', true);   
	    var submission_file_id = $("#edit_submission_file_id").val();
	    var lp_tasks_student_id = $("#edit_lp_tasks_student_id").val();
        var dataURL = canvas.toDataURL('image/jpg');
	    var base64ImageContent = dataURL.replace(/^data:image\/(png);base64,/, "");
	    var blob = base64ToBlob(base64ImageContent, 'image/jpg');
	    saveEvaluatedFile(blob, submission_file_id, lp_tasks_student_id);
	}

  	function base64ToBlob(base64, mime) {
		mime = mime || '';
		var sliceSize = 1024;
		var byteChars = window.atob(base64);
		var byteArrays = [];
		for (var offset = 0, len = byteChars.length; offset < len; offset += sliceSize) {
			var slice = byteChars.slice(offset, offset + sliceSize);
			var byteNumbers = new Array(slice.length);
			for (var i = 0; i < slice.length; i++) {
			  	byteNumbers[i] = slice.charCodeAt(i);
			}
			var byteArray = new Uint8Array(byteNumbers);
			byteArrays.push(byteArray);
		}
		return new Blob(byteArrays, {type: mime});
  	}

    function findxy(res, e) {
        if (res == 'down') {
            prevX = currX;
            prevY = currY;
            currX = e.clientX - canvas.offsetLeft;
			currY = e.clientY - canvas.offsetTop - 50;
            flag = true;
            dot_flag = true;
            if (dot_flag) {
                ctx.beginPath();
                ctx.fillStyle = pen_color;
                ctx.fillRect(currX, currY, 2, 2);
                ctx.closePath();
                dot_flag = false;
            }
        }
        if (res == 'up' || res == "out") {
            flag = false;
        }
        if (res == 'move') {
            if (flag) {
                prevX = currX;
                prevY = currY;
                currX = e.clientX - canvas.offsetLeft;
                currY = e.clientY - canvas.offsetTop - 50;
                draw();
            }
        }
    }

    function showResubmission(lp_tasks_student_id, student_name) {
		$("#resubmission-modal").modal('show');
		$("#re-std-name").html(student_name);
		$("#re_std_task_id").val(lp_tasks_student_id);
	}

	function sendForResubmission() {
		var lp_tasks_student_id = $("#re_std_task_id").val();
		var re_submission_comments = $("#resubmission_comments").val();
		$.ajax({
	        url: '<?php echo site_url('student_tasks/tasks/confirmResubmission'); ?>',
	        type: 'post',
	        data: {'lp_tasks_student_id':lp_tasks_student_id, 'comments' : re_submission_comments},
	        success: function(data) {
	            if(data == 1){
	              	$(function(){
	                    new PNotify({
	                        title: 'Success',
	                        text: 'Successful',
	                        type: 'success',
	                    });
					});
					$("#resubmission-modal").modal('hide');
					getSubmittedFilesData(lp_tasks_student_id);
					// getStudentSubmissionsV2(selected_task.id);
	            } else{
	              $(function(){
	                new PNotify({
	                    title: 'Warning',
	                    text: 'Something Went Wrong',
	                    type: 'warning',
	                });
	              });
	            }
	        }
	    });
	}

	function undoEvaluation(lp_tasks_student_id) {
		swal({
	      title: "Cancel Evaluation",
	      text: "Are you sure you want to cancel current evaluation and re-evaluate?",
	      icon: "warning",
	      buttons: true,
	      dangerMode: true,
	    })
	    .then((confirm) => {
	      	if (confirm) {
		      	$.ajax({
					url: '<?php echo site_url('student_tasks/tasks/cancelEvaluation'); ?>',
					type: 'post',
					data: {'lp_tasks_student_id':lp_tasks_student_id},
					success: function(data) {
						if(data == 1){
							swal({
			                	title: "Successful",
			                	text: "Saved successfully",
			                	icon: "success",
			              	});
			              	getSubmittedFilesData(lp_tasks_student_id);
							// getStudentSubmissionsV2(selected_task.id);
						} else{
						  	swal({
			                	title: "Error",
			                	text: "Failed to save",
			                	icon: "error",
			              	});
						}
					}
				});
	      	}
	    });
	}
</script>