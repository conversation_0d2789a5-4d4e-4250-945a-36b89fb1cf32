<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index');?>">Academics</a></li>
    <li>Question Bank V2</li>
</ul>

<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-8 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Question Bank V2
                    </h3>
                </div>
                <div class="col-md-4 d-flex align-items-center justify-content-end pr-0">
                    <!-- Add Question Button -->
                    <!-- <a target="_blank" href="<?php // echo site_url('academics/question_bank/create'); ?>" title="Add Question" class="btn btn-primary">
                        <i class="fa fa-plus"></i> Add Question
                    </a> -->
                    <a title="Add Question" target="_blank" href="<?php echo site_url("academics/question_bank/create");?>" class="new_circleShape_res" style="background-color: #fe970a;">
                        <span class="fa fa-plus" style="font-size: 19px;"></span>
                    </a>
                    <!-- Kebab Menu (matching payroll style) -->
                    <div class="more ml-2">
                        <button id="more-btn" class="more-btn d-flex flex-column">
                            <span class="more-dot mb-1"></span>
                            <span class="more-dot mb-1"></span>
                            <span class="more-dot"></span>
                        </button>
                        <div class="more-menu" style="margin-right: -1%;display:none" id="display_menu">
                        <div class="more-menu-caret">
                            <div class="more-menu-caret-outer"></div>
                            <div class="more-menu-caret-inner"></div>
                        </div>
                        <div class="d-flex flex-column align-items-center">
                            <div class="more-menu-item action_btn" style="margin-bottom: 1rem;" data-button-type="export" role="presentation">
                                <button onclick="downloadCSVTemplate(this)"  class="btn btn-primary" title="Download CSV Template">Download</button>
                            </div>
                            <div class="more-menu-item action_btn" data-button-type="export" role="presentation">
                                <button onclick="$('#uploadCSVInput').click();" class="pull-right btn btn-info" title="Upload CSV">Upload CSV</button>
                            </div>
                        </div>
                        </div>
                    </div>
                    <!-- Hidden file input for CSV upload -->
                    <input type="file" id="uploadCSVInput" accept=".csv" style="display: none;" onchange="handleQuestionsCSVUpload(this)" />
                </div>
            </div>
        </div>

        <div class="card-body pt-0">
            <!-- Filters Card -->
            <div class="card card-outline card-primary mb-3 border-0">
                <div class="card-header d-flex justify-content-between" style="background-color: transparent;">
                    <h3 class="card-title">
                        Filters
                    </h3>
                    <!-- Expand and Collapse Filters -->
                    <div class="card-tools">
                        <button class="btn btn-info mr-2" id="refreshButton" disabled title="Click To Get Updated Questions List" onclick="refreshQuestions()" style="display: none;">
                            <i class="fa fa-refresh mr-0"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-tool" data-card-widget="collapse" onclick="toggleFilters(this)" title="Expand/Collapse Filters">
                            <i class="fa fa-plus mr-0"></i>
                        </button>
                    </div>
                    <!-- Clear All Filters Button -->
                    <div class="card-tools d-none">
                        <button type="button" class="btn btn-sm btn-secondary" id="clearFilters">
                            <i class="fa fa-times"></i> Clear All
                        </button>
                    </div>
                </div>
                <div class="card-body" id="filtersContainer">
                    <form id="filterForm">
                        <div class="row g-3">
                            <!-- Basic Filters -->
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Class</label>
                                <select class="form-control select-enhanced" name="class_id" id="filter_class_id">
                                    <option value="">All Classes</option>
                                    <?php if(!empty($classes)) { ?>
                                        <?php foreach($classes as $class) { ?>
                                            <option value="<?php echo $class->class_master_id; ?>"><?php echo $class->class_name; ?></option>
                                        <?php } ?>
                                    <?php } ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Subject</label>
                                <select class="form-control select-enhanced" name="subject_id" id="filter_subject_id">
                                    <option value="">All Subjects</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Learning Outcome</label>
                                <select class="form-control select-enhanced" name="learning_outcome" id="filter_learning_outcome">
                                    <option value="">All Learning Outcomes</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Question Type</label>
                                <select class="form-control select-enhanced" name="question_type" id="filter_question_type">
                                    <option value="">All Types</option>
                                    <option value="MCQ">Multiple Choice</option>
                                    <option value="Short Answer">Short Answer</option>
                                    <option value="Long Answer">Long Answer</option>
                                    <option value="Fill in the Blanks">Fill in the Blanks</option>
                                    <option value="True/False">True / False</option>
                                    <option value="Match">Match</option>
                                    <option value="Comprehension">Comprehension</option>
                                    <!-- <option value="Custom">Custom</option> -->
                                </select>
                            </div>
                        </div>

                        <div class="row g-3 mt-2">
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Difficulty Level</label>
                                <select class="form-control select-enhanced" name="difficulty_level" id="filter_difficulty_level">
                                    <option value="">All Levels</option>
                                    <option value="Easy">Easy</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Hard">Hard</option>
                                </select>
                            </div>
                            <!-- Advanced Filters -->
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Blooms</label>
                                <select class="form-control select-enhanced" name="status" id="filter_status">
                                    <option value="">All Status</option>
                                    <option value="draft">Draft</option>
                                    <option value="pending_review">Pending Review</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Status</label>
                                <select class="form-control select-enhanced" name="status" id="filter_status">
                                    <option value="">All Status</option>
                                    <option value="draft">Draft</option>
                                    <option value="pending_review">Pending Review</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Created By</label>
                                <select class="form-control select-enhanced" name="created_by" id="filter_created_by">
                                    <option value="">All Authors</option>
                                    <option value="me">My Questions</option>
                                    <?php if(!empty($teachers)) { ?>
                                        <?php foreach($teachers as $teacher) { ?>
                                            <option value="<?php echo $teacher->id; ?>"><?php echo $teacher->staff_name; ?></option>
                                        <?php } ?>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>

                        <div class="row g-3 mt-2">
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Date Range</label>
                                <input type="date" class="form-control" name="date_from" id="filter_date_from" placeholder="From Date">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold"></label>
                                <input type="date" class="form-control" name="date_to" id="filter_date_to" placeholder="To Date">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Marks Range</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="marks_min" id="filter_marks_min" placeholder="Min" step="0.5">
                                    <span class="input-group-text">to</span>
                                    <input type="number" class="form-control" name="marks_max" id="filter_marks_max" placeholder="Max" step="0.5">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold mb-0">Sort By</label>
                                <select class="form-control select-enhanced" name="sort_by" id="filter_sort_by">
                                    <option value="created_at_desc">Newest First</option>
                                    <option value="created_at_asc">Oldest First</option>
                                    <option value="question_text_asc">Question A-Z</option>
                                    <option value="question_text_desc">Question Z-A</option>
                                    <option value="difficulty_asc">Difficulty: Easy to Hard</option>
                                    <option value="difficulty_desc">Difficulty: Hard to Easy</option>
                                    <option value="usage_count_desc">Most Used</option>
                                    <option value="marks_desc">Highest Marks</option>
                                </select>
                            </div>
                            <!-- Search and Tags -->
                            <!-- <div class="col-md-6">
                                <label class="form-label fw-bold mb-0">Search Questions</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" id="filter_search" placeholder="Search in question text, tags, or content...">
                                    <button class="btn btn-secondary" type="button" id="searchBtn" title="Search Question">
                                        <i class="fa fa-search mr-0"></i>
                                    </button>
                                </div>
                            </div> -->
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary pull-right ml-2" id="applyFilters" title="Apply Filters">
                                    <i class="fa fa-filter"></i> Apply Filters
                                </button>
                                <button type="button" class="btn btn-secondary pull-right" id="resetFilters" title="Reset Filters">
                                    <i class="fa fa-refresh"></i> Reset
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="card-body pt-0">
    <!-- Action Buttons -->
    <div class="row mb-3 d-none">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group" role="group">
                    <a target="_blank" href="<?php echo site_url('academics/question_bank/create'); ?>" class="btn btn-primary">
                        <i class="fa fa-plus"></i> Add New Question
                    </a>
                    <button type="button" class="btn btn-secondary" id="bulkActionsBtn" disabled title="Bulk Actions">
                        <i class="fa fa-tasks"></i> Bulk Actions
                    </button>
                    <button type="button" class="btn btn-info" id="exportBtn" title="Export">
                        <i class="fa fa-download"></i> Export
                    </button>
                </div>
                <div class="btn-group d-none" role="group">
                    <button type="button" class="btn btn-secondary" id="viewModeGrid" title="Grid View">
                        <i class="fa fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-secondary" id="viewModeList" title="List View">
                        <i class="fa fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions List -->
    <div class="card px-0">
        <!-- <div class="card-header d-flex justify-content-between">
            <h3 class="card-title">
                Questions
            </h3>
        </div> -->
        <div class="card-body p-0">
            <!-- Loading State -->
            <div id="loadingState" class="text-center p-4" style="display: none;">
                <i class="fa fa-spinner fa-spin fa-2x text-primary"></i>
                <p class="mt-2 text-muted">Loading questions...</p>
            </div>

            <!-- Questions Container -->
            <div id="questionsContainer">
                <!-- Questions will be loaded here via AJAX -->
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="text-center p-5" style="display: none;">
                <i class="fa fa-question-circle fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Questions Found</h4>
                <p class="text-muted">Try adjusting your filters or create a new question.</p>
                <a target="_blank" href="<?php echo site_url('academics/question_bank/create'); ?>" class="btn btn-primary">
                    <i class="fa fa-plus"></i> Add First Question
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" aria-labelledby="bulkActionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionsModalLabel">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Select an action to perform on <span id="selectedCount">0</span> selected questions:</p>
                <div class="list-group">
                    <button type="button" class="list-group-item list-group-item-action" data-action="approve">
                        <i class="fa fa-check text-success"></i> Approve Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="reject">
                        <i class="fa fa-times text-danger"></i> Reject Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="archive">
                        <i class="fa fa-archive text-warning"></i> Archive Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="delete">
                        <i class="fa fa-trash text-danger"></i> Delete Selected
                    </button>
                    <button type="button" class="list-group-item list-group-item-action" data-action="export">
                        <i class="fa fa-download text-info"></i> Export Selected
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<div id="massUploadQuestions" data-backdrop="static" class="modal fade" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-scrollable" role="document">
        <div class="modal-content" style="margin-top: 1% !important; margin: auto; width: 80%;">
            <div class="modal-header">
                <h4 class="modal-title" id="modalHeader">Mass Upload Questions <span id="detectedType" class="text-muted ml-2"></span></h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" onclick="cancelCSVPreview()" id="csvCloseBtn" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <span id="getUploadedQuestionsInstructions" class="text-info" style="font-size: 13px; display: none;">Note: Click on 'Apply Filter' to view recently uploaded question(s).</span>
                <!-- Progress Bar -->
                <div id="csvUploadProgressBarContainer" class="progress mb-3" style="height: 20px; display: none;">
                    <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuemin="0" aria-valuemax="100">
                        <span id="uploadProgressText">0%</span>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered" id="csvPreviewTable"></table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="csvCancelBtn" onclick="cancelCSVPreview()" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary mt-0" id="csvUploadBtn" onclick="uploadQuestions()">Upload Questions</button>
            </div>
        </div>
    </div>
</div>

<style>
.new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}

.more-menu {
    width: 100px;
}

/* More Button / Dropdown Menu */

.more-btn,
.more-menu-btn {
    background: none !important;
    border: 0 none !important;
    line-height: normal !important;
    overflow: visible !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    width: 100% !important;
    text-align: left !important;
    outline: none !important;
    cursor: pointer !important;
}

.more-dot {
    background-color: #aab8c2;
    margin: 0 auto;
    display: inline-block;
    width: 5px;
    height: 5px;
    margin-right: 1px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.more-menu {
    position: absolute;
    top: 100%;
    z-index: 900;
    float: left;
    padding: 10px 0;
    margin-top: 9px;
    background-color: #fff;
    border: 1px solid #ccd8e0;
    border-radius: 4px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
    opacity: 0;
    transform: translate(0, 15px) scale(.95);
    transition: transform 0.1s ease-out, opacity 0.1s ease-out;
    /* pointer-events: none; */
}

.more-menu-caret {
    position: absolute;
    top: -10px;
    right: 12px;
    width: 18px;
    height: 10px;
    float: right;
    overflow: hidden;
}

.more-menu-caret-outer,
.more-menu-caret-inner {
    position: absolute;
    display: inline-block;
    margin-left: -1px;
    font-size: 0;
    line-height: 1;
}

.more-menu {
    right: 0% !important;
    width: 120px;
}

.more-menu-caret-outer {
    border-bottom: 10px solid #c1d0da;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    height: auto;
    left: 0;
    top: 0;
    width: auto;
}

.more-menu-caret-inner {
    top: 1px;
    left: 1px;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-bottom: 9px solid #fff;
}

.more-menu-items {
    margin: 0;
    list-style: none;
    padding: 0;
}

.more-menu-item {
    display: flex;
}

.more-menu-btn {
    min-width: 100% !important;
    color: #66757f !important;
    cursor: pointer !important;
    display: block !important;
    font-size: 13px !important;
    line-height: 18px !important;
    padding: 5px 20px !important;
    position: relative !important;
    white-space: nowrap !important;
}


.more-btn:hover .more-dot,
.show-more-menu .more-dot {
    background-color: #516471;
}

.show-more-menu .more-menu {
    opacity: 1;
    transform: translate(0, 0) scale(1);
    pointer-events: auto;
}
/* Custom Styles for Question Bank List */
.select-enhanced {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%********' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.question-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.question-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.question-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.question-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.difficulty-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.question-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.question-card:hover .question-actions {
    opacity: 1;
}

.question-stats {
    font-size: 0.875rem;
    color: #6c757d;
}

.filter-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.filter-tag .remove-filter {
    margin-left: 0.25rem;
    cursor: pointer;
    color: #6c757d;
}

.filter-tag .remove-filter:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .card-tools {
        margin-top: 1rem;
    }
}

#questionsDT_filter{
    width: 25% !important;
}

.dt-buttons{
    float: right !important;
    margin-left: 10px !important;
}

.ellipsis {
    display: none;
}

.custom-btn {
    width: 40px;
    height: 30px;
    border: none;
    border-radius: 15px;   /* Rounded corners */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #fff;           /* White icon color */
    font-size: 14px;       /* Icon size */
    transition: 0.2s ease-in-out;
}

.custom-btn.view {
    background-color: #17a2b8; /* Bootstrap info color */
}

.custom-btn.edit {
    background-color: #007bff; /* Bootstrap primary color */
}

.custom-btn.delete {
    background-color: #dc3545; /* Bootstrap danger color */
}

.custom-btn:hover {
    opacity: 0.85;  /* Slight fade effect on hover */
}
</style>

<script>
    window.MathJax = { tex: { inlineMath: [['\\(', '\\)'], ['$', '$']], displayMath: [['\\[', '\\]'], ['$$','$$']] }, options: { skipHtmlTags: ['script','noscript','style','textarea','pre'] } };
</script>
<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
<script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
let currentPage = 1;
let perPage = 25;
let selectedQuestions = [];
let currentFilters = {};
let viewMode = 'list'; // 'list' or 'grid'
let lastLoadedQuestions = [];

// Question Bank List Management
$(document).ready(function() {
    const rememberTime = 30 * 60 * 1000; // 30 minutes in ms = 1800000
    const now = Date.now();
    const isFilterOpen = localStorage.getItem('isFilterOpen') === "true";
    const lastToggle = parseInt(localStorage.getItem('filterToggleTimestamp'), 10);

    const $btn = $('[title="Expand/Collapse Filters"]');
    const $icon = $btn.find('i');
    const $filters = $('#filtersContainer');

    // Check if timestamp is valid & not expired
    if (isFilterOpen && lastToggle && (now - lastToggle <= rememberTime)) {
        $filters.show();
        $icon.removeClass('fa-plus').addClass('fa-minus');
        $('#refreshButton').hide();
        $('#refreshButton').prop('disabled', true);
    } else {
        $filters.hide();
        $icon.removeClass('fa-minus').addClass('fa-plus');

        // Clear expired state
        localStorage.removeItem('isFilterOpen');
        localStorage.removeItem('filterToggleTimestamp');

        $('#refreshButton').show();
        $('#refreshButton').prop('disabled', false);
    }

    $('#more-btn').on('click', function(event) {
        event.stopPropagation(); 
        toggleMenu();
    });
    $('#filter_marks_min').on('change', function () {
        let minValRaw = $(this).val();
        let maxValRaw = $('#filter_marks_max').val();

        // Allow empty input without forcing to 0
        if (minValRaw === '') return;

        let minVal = parseFloat(minValRaw);
        let maxVal = maxValRaw === '' ? NaN : parseFloat(maxValRaw);

        if (minVal < 0) minVal = 0;

        // Enforce min <= max only if max is not empty
        if (!isNaN(maxVal) && minVal > maxVal) {
            minVal = maxVal;
        }

        $(this).val(minVal);
    });

    $('#filter_marks_max').on('change', function () {
        let maxValRaw = $(this).val();
        let minValRaw = $('#filter_marks_min').val();

        // Allow empty input without forcing to 0
        if (maxValRaw === '') return;

        let maxVal = parseFloat(maxValRaw);
        let minVal = minValRaw === '' ? NaN : parseFloat(minValRaw);

        if (maxVal < 0) maxVal = 0;

        // Enforce max >= min only if min is not empty
        if (!isNaN(minVal) && maxVal < minVal) {
            maxVal = minVal;
        }

        $(this).val(maxVal);
    });

    $('#filter_date_from').on('change', function () {
        let fromRaw = $(this).val();
        let toRaw = $('#filter_date_to').val();

        // If from date is empty, remove min restriction on to date
        if (fromRaw === '') {
            $('#filter_date_to').removeAttr('min');
            return;
        }

        // Set minimum selectable date for "to date"
        $('#filter_date_to').attr('min', fromRaw);

        let fromDate = new Date(fromRaw);
        let toDate = toRaw === '' ? null : new Date(toRaw);

        // Enforce from <= to only if to is not empty
        if (toDate && fromDate > toDate) {
            $('#filter_date_to').val(fromRaw); // set to date = from date
        }
    });

    $('#filter_date_to').on('change', function () {
        let toRaw = $(this).val();
        let fromRaw = $('#filter_date_from').val();

        if (toRaw === '') return;

        let toDate = new Date(toRaw);
        let fromDate = fromRaw === '' ? null : new Date(fromRaw);

        // Enforce to >= from only if from is not empty
        if (fromDate && toDate < fromDate) {
            $(this).val(fromRaw); // set to date = from date
        }
    });

    // Global variables

    // Initialize the page
    initializePage();
});

function initializePage() {
    // Load initial questions
    loadQuestions('onLoad');

    // Setup event listeners
    setupEventListeners();

    // Setup filter dependencies
    setupFilterDependencies();

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
}

function setupEventListeners() {
    // Filter form submission
    $('#applyFilters').on('click', function(e) {
        e.preventDefault();
        currentPage = 1;
        applyFilters();
    });

    // Reset filters
    $('#resetFilters, #clearFilters').on('click', function() {
        resetFilters();
    });

    // Search functionality
    $('#searchBtn').on('click', function() {
        currentPage = 1;
        applyFilters();
    });

    // $('#filter_search').on('keypress', function(e) {
    //     if (e.which === 13) { // Enter key
    //         currentPage = 1;
    //         applyFilters();
    //     }
    // });

    // View mode toggle
    $('#viewModeList').on('click', function() {
        setViewMode('list');
    });

    $('#viewModeGrid').on('click', function() {
        setViewMode('grid');
    });

    // Bulk actions
    $('#bulkActionsBtn').on('click', function() {
        if (selectedQuestions.length > 0) {
            $('#selectedCount').text(selectedQuestions.length);
            $('#bulkActionsModal').modal('show');
        }
    });

    // Bulk action handlers
    $('#bulkActionsModal').on('click', '[data-action]', function() {
        const action = $(this).data('action');
        performBulkAction(action);
    });

    // Export functionality
    $('#exportBtn').on('click', function() {
        exportQuestions();
    });
}

function setupFilterDependencies() {
    // Class change loads subjects
    $('#filter_class_id').on('change', function() {
        const classId = $(this).val();
        loadSubjects(classId);
    });

    // Subject change loads learning outcomes
    $('#filter_subject_id').on('change', function() {
        const subjectId = $(this).val();
        loadLearningOutcomes(subjectId);
    });
}

function loadSubjects(classId) {
    if (!classId) {
        $('#filter_subject_id').html('<option value="">All Subjects</option>');
        loadLearningOutcomes('');
        return;
    }

    $('#filter_subject_id').html('<option value="">Loading...</option>');

    $.ajax({
        url: '<?php echo site_url('academics/question_bank/get_subjects_list'); ?>',
        type: 'POST',
        data: { class_master_id: classId },
        success: function(response) {
            try {
                const subjects = JSON.parse(response);
                let options = '<option value="">All Subjects</option>';

                subjects.forEach(function(subject) {
                    options += `<option value="${subject.subject_master_id}">${subject.subject_name}</option>`;
                });

                $('#filter_subject_id').html(options);
            } catch(e) {
                console.error('Error parsing subjects:', e);
                $('#filter_subject_id').html('<option value="">Error loading subjects</option>');
            }
        },
        error: function() {
            $('#filter_subject_id').html('<option value="">Error loading subjects</option>');
        }
    });
}

function loadLearningOutcomes(subjectId) {
    let class_master_id = $('#filter_class_id').val();
    if (!class_master_id) {
        $('#filter_learning_outcome').html('<option value="">All Learning Outcomes</option>');
        return;
    }
    if (!subjectId) {
        $('#filter_learning_outcome').html('<option value="">All Learning Outcomes</option>');
        return;
    }

    $('#filter_learning_outcome').html('<option value="">Loading...</option>');

    $.ajax({
        url: '<?php echo site_url('academics/question_bank/get_learning_outcomes'); ?>',
        type: 'POST',
        data: { subject_master_id: subjectId, class_master_id: class_master_id },
        success: function(response) {
            try {
                const outcomes = JSON.parse(response);
                let options = '<option value="">All Learning Outcomes</option>';

                outcomes.forEach(function(outcome) {
                    options += `<option value="${outcome.id}">${outcome.learning_outcome_name}</option>`;
                });

                $('#filter_learning_outcome').html(options);
            } catch(e) {
                console.error('Error parsing learning outcomes:', e);
                $('#filter_learning_outcome').html('<option value="">Error loading learning outcomes</option>');
            }
        },
        error: function() {
            $('#filter_learning_outcome').html('<option value="">Error loading learning outcomes</option>');
        }
    });
}

function refreshQuestions(){
    applyFilters('Refresh');
}

function toggleFilters(btn){
    const now = Date.now();
    const $icon = $(btn).find('i');
    const $filters = $('#filtersContainer');

    $filters.stop(true, true).slideToggle(200, function () {
        const isVisible = $filters.is(':visible');

        // Save state + timestamp
        localStorage.setItem('isFilterOpen', isVisible);
        localStorage.setItem('filterToggleTimestamp', now);

        // Set icon explicitly
        if (isVisible) {
            $('#refreshButton').hide();
            $('#refreshButton').prop('disabled', true);
            $icon.removeClass('fa-plus').addClass('fa-minus');
        } else {
            $('#refreshButton').show();
            $('#refreshButton').prop('disabled', false);
            $icon.removeClass('fa-minus').addClass('fa-plus');
        }
    });
}

// Toggle kebab menu like payroll page
function toggleMenu(){
    var menu = $('.more-menu');
    if (!menu.is(':visible')) {
        menu.show();
        $('.more').addClass('show-more-menu');
        $(document).on('click', hideMenuOutside);
    } else {
        menu.hide();
        $('.more').removeClass('show-more-menu');
        $(document).off('click', hideMenuOutside);
    }
}
function hideMenuOutside(event){
    if (!$(event.target).closest('.more').length) {
        $('.more-menu').hide();
        $('.more').removeClass('show-more-menu');
        $(document).off('click', hideMenuOutside);
    }
}

function applyFilters(callFrom = 'applyFilters') {
    if(callFrom == 'applyFilters'){
        // Collect filter values
        currentFilters = {
            class_id: $('#filter_class_id').val(),
            subject_id: $('#filter_subject_id').val(),
            learning_outcome: $('#filter_learning_outcome').val(),
            question_type: $('#filter_question_type').val(),
            difficulty_level: $('#filter_difficulty_level').val(),
            status: $('#filter_status').val(),
            created_by: $('#filter_created_by').val(),
            date_from: $('#filter_date_from').val(),
            date_to: $('#filter_date_to').val(),
            // search: $('#filter_search').val(),
            marks_min: $('#filter_marks_min').val(),
            marks_max: $('#filter_marks_max').val(),
            sort_by: $('#filter_sort_by').val()
        };
    } else {
        currentFilters = {};
    }

    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });

    // Load questions with filters
    loadQuestions();
}

function resetFilters() {
    $('#filterForm')[0].reset();
    currentFilters = {};
    currentPage = 1;
    loadQuestions('Reset');
}

function loadQuestions(callFrom = 'applyFilters') {
    $('#questionsContainer').hide();
    showLoading(true);
    showEmptyState(false);
    const requestData = {
        page: currentPage,
        per_page: perPage,
        view_mode: viewMode,
        ...currentFilters
    };

    if(callFrom != 'applyFilters'){
        requestData.sort_by = 'created_at_desc'
    }

    if(!requestData.sort_by){
        requestData.sort_by = 'created_at_desc'
    }

    $.ajax({
        url: '<?php echo site_url('academics/question_bank/get_questions_list'); ?>',
        type: 'POST',
        data: requestData,
        success: function(response) {
            try {
                const data = JSON.parse(response);

                if (data.success) {
                    if (data.questions.length === 0) {
                        showLoading(false);
                        showEmptyState(true);
                    } else {
                        showLoading(false);
                        showEmptyState(false);
                        $('#questionsContainer').show();
                        displayQuestions(data.questions);
                    }
                } else {
                    showError(data.message || 'Error loading questions');
                }
            } catch(e) {
                console.error('Error parsing response:', e);
                showError('Error loading questions');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            showError('Failed to load questions. Please try again.');
        }
    });
}

function displayQuestions(questions) {
    let html = '';

    if (viewMode === 'list') {
        html = generateListView(questions);
    } else {
        html = generateGridView(questions);
    }

    // Insert the HTML first
    $('#questionsContainer').html(html);

    // Initialize DataTable on the generated table (list mode only)
    if (viewMode === 'list') {
        // var currentDateTime = new Date();
        // var day = String(currentDateTime.getDate()).padStart(2, '0');
        // var month = currentDateTime.toLocaleString('default', { month: 'short' });
        // var year = currentDateTime.getFullYear();
        // var hours = String(currentDateTime.getHours()).padStart(2, '0');
        // var minutes = String(currentDateTime.getMinutes()).padStart(2, '0');
        // var seconds = String(currentDateTime.getSeconds()).padStart(2, '0');
        // var formattedDateTime = `${day}-${month}-${year} ${hours}-${minutes}-${seconds}`;
        // const fileName = `Question_Bank_${formattedDateTime}`;
        const $table = $('#questionsContainer table');
        if ($.fn.DataTable && $table.length) {
            // Destroy any existing instance
            if ($.fn.dataTable.isDataTable($table)) { $table.DataTable().destroy(); }
            $table.DataTable({
                dom: 'lBfrtip',
                paging: true,
                info: true,
                searching: true,
                lengthChange: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                ordering: false,
                drawCallback: function(){
                    const container = document.getElementById('questionsContainer');
                    if (container && window.MathJax && MathJax.typesetPromise) {
                        MathJax.typesetPromise([container]);
                    }
                },
                buttons: [
                    // {
                    //     extend: 'excelHtml5',
                    //     text: 'Excel',
                    //     filename: fileName,
                    //     className: 'btn btn-info',
                    //     title: '',
                    //     exportOptions: {
                    //         columns: ':not(:first-child)'
                    //     }
                    // }
                ]
            });
        }
    }

    // Then trigger MathJax on the container (list and grid once at insert)
    const container = document.getElementById('questionsContainer');
    if (container && window.MathJax && MathJax.typesetPromise) {
        MathJax.typesetPromise([container]).catch(function(e){ console.warn('MathJax typeset error', e); });
    }

    // Event listeners are already set up via onclick attributes in the HTML
}

function generateListView(questions) {
    let html = '<div class="col-md-12 my-2 table-responsive"><table class="table table-bordered table-hover" id="questionsDT" style="white-space: nowrap !important;">';
    html += `
        <thead>
            <tr style="white-space: nowrap !important;">
                <th width="20">Actions</th>
                <!-- <th width="40">
                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
                </th> -->
                <th width="20">#</th>
                <th width="200">Question</th>
                <th width="20">Type</th>
                <th width="20">Difficulty</th>
                <th width="20">Marks</th>
                <th width="20">Status</th>
                <th width="20">Created By</th>
                <th width="20">Created At</th>
            </tr>
        </thead>
        <tbody>
    `;

    questions.forEach(function(question, index) {
        html += generateQuestionRow(question, index);
    });

    html += '</tbody></table></div>';
    return html;
}

function generateQuestionRow(question, index) {
    const questionHtml = question.question_text || '';

    const statusClass = getStatusClass(question.status);
    const difficultyClass = getDifficultyClass(question.difficulty_level);

    return `
        <tr class="question-row" data-question-id="${question.id}" style="white-space: nowrap !important;">
            <td id="actions-${question.id}">
                <div class="btn-group btn-group-sm">
                    <button type="button" class="custom-btn view btn-sm mr-2" onclick="viewQuestion(${question.id})" title="View">
                        <i class="fa fa-eye mr-0"></i>
                    </button>
                    <button type="button" class="custom-btn edit btn-sm mr-2 d-none" onclick="editQuestion(${question.id})" title="Edit">
                        <i class="fa fa-edit mr-0"></i>
                    </button>
                    <button type="button" class="custom-btn duplicate btn-sm d-none" onclick="duplicateQuestion(${question.id})" title="Duplicate">
                        <i class="fa fa-copy mr-0"></i>
                    </button>
                    <button type="button" class="custom-btn delete btn-sm" onclick="deleteQuestion(${question.id})" title="Delete">
                        <i class="fa fa-trash-o mr-0"></i>
                    </button>
                </div>
            </td>
            <!-- <td>
                <input type="checkbox" class="question-checkbox" value="${question.id}" onchange="updateBulkActionsButton()">
            </td> -->
            <td>${index + 1}</td>
            <td>
                <div class="question-preview">
                    <div class="fw-bold">${questionHtml}</div>
                </div>
            </td>
            <td>
                <span class="badge badge-secondary question-type-badge">${question.question_type}</span>
            </td>
            <td>
                <span class="badge ${difficultyClass} difficulty-badge">${question.difficulty_level || 'N/A'}</span>
            </td>
            <td>
                <span class="fw-bold">${question.max_marks}</span>
                ${question.negative_marks > 0 ? `<br><small class="text-danger">-${question.negative_marks}</small>` : ''}
            </td>
            <td>
                <span class="badge ${statusClass}">${formatStatus(question.status)}</span>
            </td>
            <td>
                ${question.created_by_name}
            </td>
            <td>
                ${question.created_at_formatted_without_time}
            </td>
        </tr>
    `;
}

// Utility functions
function getStatusClass(status) {
    const statusClasses = {
        'draft': 'badge-secondary',
        'pending_review': 'badge-warning',
        'approved': 'badge-success',
        'rejected': 'badge-danger',
        'archived': 'badge-dark'
    };
    return statusClasses[status] || 'badge-secondary';
}

function getDifficultyClass(difficulty) {
    const difficultyClasses = {
        'Easy': 'badge-success',
        'Medium': 'badge-warning',
        'Hard': 'badge-danger'
    };
    return difficultyClasses[difficulty] || 'badge-secondary';
}

function formatStatus(status) {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function showLoading(show) {
    if (show) {
        $('#loadingState').show();
        // $('#questionsContainer').hide();
    } else {
        $('#loadingState').hide();
        // $('#questionsContainer').show();
    }
}

function showEmptyState(show) {
    if (show) {
        $('#emptyState').show();
        // $('#questionsContainer').hide();
    } else {
        $('#emptyState').hide();
        // $('#questionsContainer').show();
    }
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Error',
        text: message
    });
}

// Global functions for question actions
function viewQuestion(questionId) {
    window.open('<?php echo site_url('academics/question_bank/view/'); ?>' + questionId, '_blank');
}

function editQuestion(questionId) {
    window.location.href = '<?php echo site_url('academics/question_bank/edit/'); ?>' + questionId;
}

function duplicateQuestion(questionId) {
    Swal.fire({
        title: 'Duplicate Question?',
        text: 'This will create a copy of the question that you can modify.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, duplicate it!',
        reverseButtons: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        allowEnterKey: false,
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '<?php echo site_url('academics/question_bank/duplicate/'); ?>' + questionId;
        }
    });
}

function refreshPendingQuestionRow(questionId) {
    $('#actions-' + questionId).html('<span class="text-primary">Refresh Pending Click On Apply Filters</span>');
}

function deleteQuestion(questionId) {
    Swal.fire({
        title: 'Delete Question?',
        text: 'This action cannot be undone!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!',
        reverseButtons: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        allowEnterKey: false,
    }).then((result) => {
        if (result.isConfirmed) {
            // Perform delete action
            $.ajax({
                url: '<?php echo site_url('academics/question_bank/delete/'); ?>' + questionId,
                type: 'POST',
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.success) {
                        Swal.fire('Deleted!', 'Question has been deleted.', 'success');
                        refreshPendingQuestionRow(questionId); // Reload the list
                    } else {
                        Swal.fire('Error!', data.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error!', 'Failed to delete question.', 'error');
                }
            });
        }
    });
}

// Missing function implementations
function generateGridView(questions) {
    let html = '<div class="row">';

    questions.forEach(function(question) {
        const questionText = question.question_text.length > 150 ?
            question.question_text.substring(0, 150) + '...' :
            question.question_text;

        const statusClass = getStatusClass(question.status);
        const difficultyClass = getDifficultyClass(question.difficulty_level);

        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="badge ${statusClass}">${formatStatus(question.status)}</span>
                        <span class="badge ${difficultyClass}">${question.difficulty_level || 'N/A'}</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">${question.question_type}</h6>
                        <p class="card-text">${questionText}</p>
                        <small class="text-muted">
                            ${question.class_name} - ${question.subject_name}
                            ${question.topic_name ? ' - ' + question.topic_name : ''}
                        </small>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                ${question.created_at_formatted}<br>
                                by ${question.created_by_name}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-primary btn-sm" onclick="viewQuestion(${question.id})" title="View">
                                    <i class="fa fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="editQuestion(${question.id})" title="Edit">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="duplicateQuestion(${question.id})" title="Duplicate">
                                    <i class="fa fa-copy"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteQuestion(${question.id})" title="Delete">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function setViewMode(mode) {
    viewMode = mode;

    // Update button states
    if (mode === 'list') {
        $('#viewModeList').addClass('active');
        $('#viewModeGrid').removeClass('active');
    } else {
        $('#viewModeGrid').addClass('active');
        $('#viewModeList').removeClass('active');
    }

    // Reload questions with new view mode
    if (typeof lastLoadedQuestions !== 'undefined' && lastLoadedQuestions.length > 0) {
        displayQuestions(lastLoadedQuestions);
    }
}

function performBulkAction(action) {
    if (selectedQuestions.length === 0) {
        Swal.fire('Warning', 'No questions selected', 'warning');
        return;
    }

    switch(action) {
        case 'delete':
            Swal.fire({
                title: 'Delete Selected Questions?',
                text: `This will delete ${selectedQuestions.length} question(s). This action cannot be undone.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete them!',
                reverseButtons: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
            }).then((result) => {
                if (result.isConfirmed) {
                    // TODO: Implement bulk delete
                    Swal.fire('Success', 'Questions deleted successfully', 'success');
                    $('#bulkActionsModal').modal('hide');
                    loadQuestions('Delete');
                }
            });
            break;
        case 'export':
            exportQuestions(selectedQuestions);
            break;
        default:
            console.error('Unknown bulk action:', action);
    }
}

function exportQuestions(questionIds = null) {
    // TODO: Implement export functionality
    const exportData = questionIds || selectedQuestions;

    Swal.fire({
        title: 'Export Questions',
        text: 'Export functionality will be implemented soon.',
        icon: 'info'
    });
}

function toggleSelectAll(checkbox) {
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    questionCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        if (checkbox.checked) {
            if (!selectedQuestions.includes(cb.value)) {
                selectedQuestions.push(cb.value);
            }
        } else {
            const index = selectedQuestions.indexOf(cb.value);
            if (index > -1) {
                selectedQuestions.splice(index, 1);
            }
        }
    });
    updateBulkActionsButton();
}

function updateBulkActionsButton() {
    // Update selectedQuestions array
    selectedQuestions = [];
    document.querySelectorAll('.question-checkbox:checked').forEach(cb => {
        selectedQuestions.push(cb.value);
    });

    // Update bulk actions button
    const bulkBtn = document.getElementById('bulkActionsBtn');
    if (selectedQuestions.length > 0) {
        bulkBtn.disabled = false;
        bulkBtn.innerHTML = `<i class="fa fa-tasks"></i> Bulk Actions (${selectedQuestions.length})`;
    } else {
        bulkBtn.disabled = true;
        bulkBtn.innerHTML = '<i class="fa fa-tasks"></i> Bulk Actions';
    }

    // Update select all checkbox
    const selectAllCheckbox = document.getElementById('selectAll');
    const totalCheckboxes = document.querySelectorAll('.question-checkbox').length;
    const checkedCheckboxes = document.querySelectorAll('.question-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCheckboxes === totalCheckboxes) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Store last loaded questions for view mode switching (declared globally above)
// Override displayQuestions to store the data
const originalDisplayQuestions = window.displayQuestions;
window.displayQuestions = function(questions) {
    lastLoadedQuestions = questions;
    return originalDisplayQuestions(questions);
};

function downloadCSVTemplate(btn){
    toggleMenu();
    // btn.disabled = true;
    // btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Downloading...';
    Swal.fire({
        title: 'Select Question Type',
        input: 'select',
        inputOptions: {
            'All': 'All',
            'MCQ': 'MCQ',
            'True/False': 'True/False',
            'Short Answer': 'Short Answer',
            'Long Answer': 'Long Answer',
            'Fill in the Blanks': 'Fill in the Blanks',
            'Match': 'Match'
        },
        inputPlaceholder: 'Select a question type',
        showCancelButton: true,
        confirmButtonText: 'Download',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        allowOutsideClick: false,
        allowEscapeKey: false,
        allowEnterKey: false,
        inputValidator: (value) => {
            return value ? null : 'Please select a question type!';
        }
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            const type = result.value;

            const formData = new FormData();
            formData.append('type', type);

            fetch('<?= base_url('academics/question_bank/download_template') ?>', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.blob();
            })
            .then(blob => {
                const filename = `question_bank_template_${type}.csv`;
                const a = document.createElement('a');
                const url = URL.createObjectURL(blob);
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            })
            .catch(err => {
                console.error(err);
                Swal.fire('Error', 'Download failed. Please try again later.', 'error');
            });
        } else {
            // btn.disabled = false;
            // btn.innerHTML = 'Download Template';
        }
    });
}

let parsedCSVData = [];
let selectedQuestionType = '';

function handleQuestionsCSVUpload(input){
    const file = input.files[0];
    if (!file) {
        Swal.fire({
            icon: 'warning',
            title: 'No File Selected',
            text: 'Please choose a CSV file to upload.',
        });
        return;
    }
    const fileName = file.name.toLowerCase();
    const isCSV = fileName.endsWith('.csv');

    if (!isCSV) {
        Swal.fire({
            icon: 'error',
            title: 'Invalid File',
            text: 'Only .csv files are allowed.',
        });
        input.value = ''; // Reset file input
        return;
    }
    // Read as ArrayBuffer to preserve encoding and decode as UTF-8 with BOM handling
    const reader = new FileReader();
    reader.onload = function(e) {
        const buffer = e.target.result;
        let text = '';
        try {
            const decoder = new TextDecoder('utf-8', { fatal: false });
            text = decoder.decode(buffer);
        } catch(err) {
            // Fallback: attempt without options
            text = new TextDecoder('utf-8').decode(buffer);
        }

        // Strip BOM if present
        if (text.charCodeAt(0) === 0xFEFF) {
            text = text.slice(1);
        }

        Papa.parse(text, {
            header: true,
            skipEmptyLines: true,
            complete: function(results) {
                if (results.data.length === 0) {
                    Swal.fire('Empty File', 'No data found in the CSV.', 'error');
                    return;
                }

                const normalizeHeader = (s) => (s || '').toString().trim().replace(/^"|"$/g, '');
                const headers = results.meta.fields.map(h => normalizeHeader(h));
                // 🔥 Trim row keys
                results.data = results.data.map(row => {
                    const cleanedRow = {};
                    for (let key in row) {
                        const nk = normalizeHeader(key);
                        cleanedRow[nk] = row[key];
                    }
                    return cleanedRow;
                });
                const detectedType = detectQuestionTypeFromHeaders(headers);

                if (!detectedType) {
                    Swal.fire('Unknown Format', 'Unable to detect question type from the CSV headers.', 'error');
                    input.value = ''; // reset
                    return;
                }
                selectedQuestionType = detectedType;
                $('#detectedType').text(`(${selectedQuestionType})`);
                parsedCSVData = results.data;
                renderCSVPreview(parsedCSVData);
                $('#massUploadQuestions').modal('show');
            }
        });
    };
    reader.onerror = function() {
        Swal.fire('Error', 'Failed to read the CSV file.', 'error');
    };
    reader.readAsArrayBuffer(file);
}

const headerMap = {
    'MCQ': ['Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Option 1', 'Option 2', 'Option 3', 'Option 4', 'Option 5', 'Correct Option 1', 'Correct Option 2', 'Correct Option 3', 'Correct Option 4', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints'],
    'True/False': ['Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Correct Option', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints'],
    'Short Answer': ['Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Correct Answer 1', 'Correct Answer 2', 'Correct Answer 3', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints'],
    'Long Answer': ['Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Sample Answer 1', 'Sample Answer 2', 'Sample Answer 3', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints'],
    'Fill in the Blanks': ['Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Blank 1', 'Blank 2', 'Blank 3', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints'],
    'Match': ['Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Left 1', 'Right 1', 'Left 2', 'Right 2', 'Left 3', 'Right 3', 'Left 4', 'Right 4', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints'],
    'All': ['Question Type', 'Question Text', 'Class Name', 'Subject Name', 'Learning Outcome', 'Difficulty Level', 'Option 1', 'Option 2', 'Option 3', 'Option 4', 'Option 5', 'Correct Option 1', 'Correct Option 2', 'Correct Option 3', 'Correct Option 4', 'Correct Option', 'Correct Answer 1', 'Correct Answer 2', 'Correct Answer 3', 'Sample Answer 1', 'Sample Answer 2', 'Sample Answer 3', 'Blank 1', 'Blank 2', 'Blank 3', 'Left 1', 'Right 1', 'Left 2', 'Right 2', 'Left 3', 'Right 3', 'Left 4', 'Right 4', 'Max Marks', 'Negative Marks', 'Blooms Taxonomy', 'Skills', 'Custom Tags', 'Hints']
};

function detectQuestionTypeFromHeaders(headers) {
    const normalized = headers.map(h => (h || '').toString().trim().replace(/^"|"$/g, ''));
    // Ensure "All" gets highest priority
    if (headerMap['All'].every(h => normalized.includes(h))) {
        return 'All';
    }
    for (const [type, expectedHeaders] of Object.entries(headerMap)) {
        const isMatch = expectedHeaders.every(h => normalized.includes(h));
        if (isMatch) return type;
    }
    return null;
}

function renderCSVPreview(data) {
    parsedCSVData = data;

    const table = $('#csvPreviewTable');
    table.empty();

    if (!data.length) {
        table.append('<tr><td colspan="100%">No data to preview</td></tr>');
        return;
    }

    const headers = Object.keys(data[0]);

    // 🔁 Move 'Status' to the beginning instead of the end
    const updatedHeaders = ['Status', ...headers];

    // Build header
    let thead = '<thead><tr>';
    updatedHeaders.forEach(header => {
        thead += `<th>${header}</th>`;
    });
    thead += '</tr></thead>';

    // Build rows
    let tbody = '<tbody>';
    data.forEach((row, i) => {
        tbody += `<tr id="previewRow-${i}">`;
        // Status cell first
        tbody += `<td class="status-cell text-center"><i class="fa fa-clock-o text-muted"></i></td>`;
        // Then other columns
        headers.forEach(header => {
            tbody += `<td>${row[header] || ''}</td>`;
        });
        tbody += '</tr>';
    });
    tbody += '</tbody>';

    table.append(thead + tbody);
    $('#csvUploadBtn').html('Upload Completed');
    $('#csvUploadBtn').prop('disabled', false);
    $('#getUploadedQuestionsInstructions').hide();
    // Show modal
    $('#massUploadQuestions').modal({
        backdrop: 'static',
        keyboard: false
    });
}

function cancelCSVPreview() {
    parsedCSVData = [];
    $('#csvPreviewTable').empty();
    $('#uploadCSVInput').val('');
    $('#massUploadQuestions').modal('hide');
}

function disableUploadControls(disable = true) {
        $('#csvUploadBtn').prop('disabled', disable);
        $('#csvUploadBtn').html('Please wait...');
        $('#csvCloseBtn').prop('disabled', disable);
        $('#csvCancelBtn').prop('disabled', disable);
        $('#csvCloseBtn').css('cursor', 'default');
}

function uploadQuestions(){
    if (!parsedCSVData.length) {
        Swal.fire('No Data', 'Please select and preview a valid CSV before uploading.', 'warning');
        return;
    }
    disableUploadControls(true);
    const totalRows = parsedCSVData.length;
    let successCount = 0;
    let failCount = 0;
    const errorLogs = [];
    $('#csvUploadProgressBarContainer').show();
    $('#uploadProgressBar').css('width', '0%').attr('aria-valuenow', 0);
    $('#uploadProgressText').text(`0/${totalRows} (0%)`);
    function processRow(index) {
        if (index >= totalRows) {
            Swal.close();
            $('#csvUploadProgressBarContainer').hide();
            $('#csvCancelBtn').prop('disabled', false);
            $('#csvCloseBtn').prop('disabled', false);
            $('#csvCloseBtn').css('cursor', 'pointer');
            $('#csvUploadBtn').html('Upload Completed');
            if(successCount > 0){
                $('#getUploadedQuestionsInstructions').show();
            }
            Swal.fire({
                title: 'Upload Complete',
                html: `✅ Success: ${successCount}<br>❌ Failed: ${failCount}`,
                icon: failCount === 0 ? 'success' : 'warning',
                showConfirmButton: true,
                confirmButtonText: 'OK',
            });
            // Do not re-enable cancel or upload
            return;
        }

        const row = parsedCSVData[index];
        const statusCell = $(`#previewRow-${index} .status-cell`);
        statusCell.html('<i class="fa fa-spinner fa-spin text-primary"></i>');
        statusCell.css('cursor', 'not-allowed');
        $.ajax({
            url: '<?= base_url('academics/question_bank/process_single_question') ?>',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                row: row,
                question_type: selectedQuestionType
            }),
            success: function (data) {
                let res = JSON.parse(data);
                statusCell.css('cursor', 'default');
                if (!res) {
                    statusCell.html(`<span class="text-danger" title="Invalid response">Invalid response</span>`);
                    failCount++;
                    errorLogs.push(`Row ${index + 2}: Invalid response`);
                    return;
                }
                if (res.status) {
                    statusCell.html(`<span class="text-success" title="${res.remarks}">${res.remarks}</span>`);
                    successCount++;
                } else {
                    statusCell.html(`<span class="text-danger" title="${res.remarks}">${res.remarks}</span>`);
                    failCount++;
                    errorLogs.push(`Row ${index + 2}: ${res.remarks}`);
                }
            },
            error: function (xhr) {
                let responseMessage = '';
                try { responseMessage = (xhr && xhr.responseText) ? xhr.responseText : ''; } catch(_) {}
                let remarks = 'Internal Server Error';
                if (responseMessage && responseMessage.includes('Duplicate')) {
                    remarks = 'Duplicate Question';
                }
                statusCell.css('cursor', 'default');
                statusCell.html(`<span class="text-danger" title="${remarks}">${remarks}</span>`);
                failCount++;
                errorLogs.push(`Row ${index + 2}: AJAX error`);
            },
            complete: function () {
                const processed = index + 1;
                const progress = (processed / totalRows) * 100;
                $('#uploadProgressBar')
                    .css('width', `${progress}%`)
                    .attr('aria-valuenow', Math.round(progress));
                $('#uploadProgressText').text(`${processed}/${totalRows} (${Math.round(progress)}%)`);

                setTimeout(() => processRow(index + 1), 150);
            }
        });
    }

    processRow(0);
}
</script>
