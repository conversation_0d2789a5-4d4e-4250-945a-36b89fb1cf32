<div class="row">
  <div class="panel panel-default">
    <div class="panel-heading" style="border-bottom: none;">
      <h3 id="stu_print"  class="panel-title">Fee Receipt</h3>
        <ul class="panel-controls">
         <?php 
          if ( !empty($cancel)== 1) { ?>
            <a class="btn btn-info" id="stu_print" onclick="close_window()"  href="javascript:void(0)"><i class="fa fa-mail-reply"></i>Close</a>
          <?php }else{ ?>
            <a class="btn btn-info" id="stu_print" href="<?php echo site_url('feesv2/fees_collection/fee_student_blueprints/'.$fee_trans->student_id);?>"><i class="fa fa-mail-reply"></i>Next fee collection</a>
          <?php } ?>
          <button id="stu_print" class="btn btn-danger" onclick="print_receipt()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
        </ul>
    </div>
    <div class="panel-body" id="printArea">
        <table class="table table-bordered" style="width: 100%;margin-bottom: 0; margin-top: -25px">
            <thead>
                <tr>
                    <th colspan="3">
                        &emsp; &emsp; &emsp; &emsp;  &emsp; &emsp; &emsp; &emsp; <?php echo $fee_trans->student->stdName ?> <br>
                        &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; <?php echo $fee_trans->student->clsName ?> <br>
                        &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp;  &emsp; &emsp;<?php echo $fee_trans->student->admission_no;?> 
                    </th>
                    <th colspan="1" style="margin-top:-30px">
                        <br>
                      <span style="margin-left: 62%;" ><?php echo $fee_trans->receipt_number ?></span><br>
                      <span style="margin-left: 60%;" > <?php echo date('d-m-Y', strtotime($fee_trans->paid_datetime)) ?></span><br>
                       <br>
                       <br>
                    </th>
                </tr>
            </thead>
        </table>

        <table class="table table-bordered" style="width: 100%; margin-bottom: 0; min-height: 120px;">
            <tbody>
              <?php 
                $i=1; 
                $cnt= 0; 
                $sl=0;
                $totalAmount = 0;
                foreach ($fee_trans->comp as $key => $val) { 
                    $totalAmount += $val->amount_paid;
                    ?>
                <tr>
                  <?php if(!$sl) { ?>
                    <td width="10%" style="text-align: right;"  rowspan="<?= $fee_trans->no_of_comp->comp_count ?>"><?php echo $i++ ?></td>
                    <?php $sl = $fee_trans->no_of_comp->comp_count; }
                      $sl--;
                    ?>
                    <?php if($fee_trans->no_of_ins->ins_count >= 2){ ?>
                    <?php if(!$cnt) { ?>
                    <td style="text-align: right;"  width="30%" rowspan="<?= $fee_trans->no_of_comp->comp_count ?>" ><?= $val->insName .' ' .$val->compName  ?></td>
                    <?php $cnt = $fee_trans->no_of_comp->comp_count; }
                      $cnt--;
                    ?>
                    <?php } ?>
                    <td style="text-align: right;" width="35%"></td>
                    <td width="25%"><?php echo $val->amount_paid ?></td>
                </tr>
               <?php } ?>
                
            </tbody>
            <tfoot>
                
                <?php if ($fee_trans->concession_amount != 0) { ?>
                    <tr>
                      <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Rebate (-)</th>
                      <th><?php echo number_format($fee_trans->concession_amount,2,'.','');  ?></th>
                    </tr>
                <?php } ?>
                 <?php if ($fee_trans->fine_amount != 0) { ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Fine Amount</th>
                    <th><?php echo $fee_trans->fine_amount ?></th>
                  </tr>
                <?php } ?>

                  <?php $totalCollectAmount = $fee_trans->amount_paid + $fee_trans->fine_amount; ?>
                <?php if ($fee_trans->discount_amount != 0) {
                    $totalCollectAmount = $fee_trans->amount_paid - $fee_trans->discount_amount;
                 ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Discount (-)</th>
                    <th><?php echo $fee_trans->discount_amount ?></th>
                  </tr>
                <?php } ?>
              <tr>
                <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" > &emsp;&emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp;  &emsp; &emsp; <span  style="text-align: left;" id="words_amount1"></span> <span  style="float: right;">Total Fee</span></th>
                <th><?php echo number_format($totalCollectAmount,2,'.','');?></th> 
              </tr>
              <tr>
              </tr>
            </tfoot>
          
        </table> 

         <table class="table table-bordered" style="width: 100%;margin-bottom: 0; margin-top: 40px;">
            <thead>
                <tr>
                    <th colspan="3">
                        &emsp; &emsp; &emsp; &emsp;  &emsp; &emsp; &emsp; &emsp; <?php echo $fee_trans->student->stdName ?> <br>
                        &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; <?php echo $fee_trans->student->clsName ?> <br>
                        &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; <?php echo $fee_trans->student->admission_no;?> 
                    </th>
                    <th colspan="1" style="padding-bottom: 20px; margin-top: 40px;">
                        <br>
                      <span style="margin-left: 62%;" ><?php echo $fee_trans->receipt_number ?></span><br>
                      <span style="margin-left: 60%;" > <?php echo date('d-m-Y', strtotime($fee_trans->paid_datetime)) ?></span><br>
                       <br>
                       <br>
                    </th>
                </tr>
            </thead>
        </table>

        <table class="table table-bordered" style="margin-bottom: 0; min-height: 120px;">
            <tbody>
              <?php 
                $i=1; 
                $cnt= 0; 
                $sl=0;
                $totalAmount = 0;
                foreach ($fee_trans->comp as $key => $val) { 
                    $totalAmount += $val->amount_paid;
                    ?>
                <tr>
                  <?php if(!$sl) { ?>
                    <td style="text-align: right;"  width="10%" rowspan="<?= $fee_trans->no_of_comp->comp_count ?>"><?php echo $i++ ?></td>
                    <?php $sl = $fee_trans->no_of_comp->comp_count; }
                      $sl--;
                    ?>
                    <?php if($fee_trans->no_of_ins->ins_count >= 2){ ?>
                    <?php if(!$cnt) { ?>
                    <td width="30%" style="text-align: right;"  rowspan="<?= $fee_trans->no_of_comp->comp_count ?>" ><?= $val->insName .' '.$val->compName  ?></td>
                    <?php $cnt = $fee_trans->no_of_comp->comp_count; }
                      $cnt--;
                    ?>
                    <?php } ?>
                    <td style="text-align: right;" width="35%"></td>
                    <td width="25%"><?php echo $val->amount_paid ?></td>
                </tr>
               <?php } ?>
                
            </tbody>
            <tfoot>
               <?php if ($fee_trans->concession_amount != 0) { ?>
                    <tr>
                      <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Rebate (-)</th>
                      <th><?php echo number_format($fee_trans->concession_amount,2,'.','');  ?></th>
                    </tr>
                <?php } ?>
                <?php if ($fee_trans->fine_amount != 0) { ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Fine Amount</th>
                    <th><?php echo $fee_trans->fine_amount ?></th>
                  </tr>
                <?php } ?>

                <?php $totalCollectAmount = $fee_trans->amount_paid + $fee_trans->fine_amount; ?>
                <?php if ($fee_trans->discount_amount != 0) {
                    $totalCollectAmount = $fee_trans->amount_paid - $fee_trans->discount_amount;
                 ?>
                  <tr>
                    <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" style="text-align: right;">Discount (-)</th>
                    <th><?php echo $fee_trans->discount_amount ?></th>
                  </tr>
                <?php } ?>
              <tr>
                <th colspan="<?php echo ($fee_trans->no_of_ins->ins_count > 1) ? '3' : '2' ?>" > &emsp; &emsp; &emsp;&emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp; &emsp;  &emsp;<span  style="text-align: left;" id="words_amount2"></span> <span  style="float: right;">Total Fee</span></th>
                <th><?php echo number_format($totalCollectAmount,2,'.','');?></th> 
              </tr>
              <tr>
              </tr>
            </tfoot>
          
        </table> 
        
    </div>
  </div>
 </div>


<script type="text/javascript">
  function close_window() {
    window.close();
  }
</script>


<script type="text/javascript">

  function print_receipt(){
    var restorepage = document.body.innerHTML;
    var printcontent = document.getElementById('printArea').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

 $(document).ready(function() {
    var amount="<?php echo $fee_trans->amount_paid + $fee_trans->fine_amount ?>";
    var words = new Array();
    words[0] = 'Zero';
    words[1] = 'One';
    words[2] = 'Two';
    words[3] = 'Three';
    words[4] = 'Four';
    words[5] = 'Five';
    words[6] = 'Six';
    words[7] = 'Seven';
    words[8] = 'Eight';
    words[9] = 'Nine';
    words[10] = 'Ten';
    words[11] = 'Eleven';
    words[12] = 'Twelve';
    words[13] = 'Thirteen';
    words[14] = 'Fourteen';
    words[15] = 'Fifteen';
    words[16] = 'Sixteen';
    words[17] = 'Seventeen';
    words[18] = 'Eighteen';
    words[19] = 'Nineteen';
    words[20] = 'Twenty';
    words[30] = 'Thirty';
    words[40] = 'Forty';
    words[50] = 'Fifty';
    words[60] = 'Sixty';
    words[70] = 'Seventy';
    words[80] = 'Eighty';
    words[90] = 'Ninety';
    amount = amount.toString();
    var atemp = amount.split(".");
    var number = atemp[0].split(",").join("");
    var n_length = number.length;
    var words_string = "";
    if (n_length <= 9) {
        var n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
        var received_n_array = new Array();
        for (var i = 0; i < n_length; i++) {
            received_n_array[i] = number.substr(i, 1);
        }
        for (var i = 9 - n_length, j = 0; i < 9; i++, j++) {
            n_array[i] = received_n_array[j];
        }
        for (var i = 0, j = 1; i < 9; i++, j++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                if (n_array[i] == 1) {
                    n_array[j] = 10 + parseInt(n_array[j]);
                    n_array[i] = 0;
                }
            }
        }
        value = "";
        for (var i = 0; i < 9; i++) {
            if (i == 0 || i == 2 || i == 4 || i == 7) {
                value = n_array[i] * 10;
            } else {
                value = n_array[i];
            }
            if (value != 0) {
                words_string += words[value] + " ";
            }
            if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Crores ";
            }
            if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Lakhs ";
            }
            if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                words_string += "Thousand ";
            }
            if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                words_string += "Hundred and ";
            } else if (i == 6 && value != 0) {
                words_string += "Hundred " ; 
            }
        }

         words_string = words_string.split(" ").join(" ")  +"Rupees Only";
    }  
    $('#words_amount1').html(words_string);
    $('#words_amount2').html(words_string);
 
    
});     
</script>
