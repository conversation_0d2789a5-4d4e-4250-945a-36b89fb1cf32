<ul class="breadcrumb" id="parent_breadcums">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
</ul>
<div id="loader" class="loaderclass" style="display:none;"></div>
<div class="row" id="opacity">
	<div class="col-md-12">
		<div class="card panel_new_style">
			<div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            	<h3 class="card-title panel_title_new_style"><strong>Add Task in <?php if($this->settings->getSetting('student_task_module_name') != null) {echo $this->settings->getSetting('student_task_module_name');} else {echo 'Student Task';}  ?></strong>
                <!-- <input type="hidden" name="filers_val" id="filers_val" value="show"> -->
              </h3>
			</div>
      		<div class="card-body px-3 pt-0">
      			<form action="<?php echo site_url('student_tasks/tasks/publishNewTaskMobile')?>" enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="add_task_form">
                    <div class="card-body p-0">                            
                        <div class="form-group">
                            <label>Task Name <span style="color: red;"><sup>*</sup></span> </label>
                            <div>
                                <input class="form-control" placeholder="Enter Task Name (max 100 characters)" name="task_name" class="form-control" id="task_name" type="text" required="" maxlength="100" data-parsley-error-message="Enter task name to proceed"/>
                            </div>
                        </div>



						<div class="form-group">
                            <label class="col-md-3 pr-0">Description Templates</label>
                            <div class="col-md-9  pl-0">
							<select id="templateName" name="templates" class="form-control" required="" onchange="applyTemplate()">
								<?php
									foreach ($templates as $temp) {
										$selected = '';
										if ($homework_default_template === $temp['name']) $selected = 'selected';
										$value = $temp['name'];
										echo "<option value='$value' $selected>$value</option>";
									}
								?>
							</select>
							</div>
                        </div>



                        <div class="form-group">
                            <label>Description </label>
                            <div>
                                <textarea value="" placeholder="Enter Task Description" class="form-control summernote" name="task_description" id="task_description"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                        	<label>Task Type <span style="color: red;"><sup>*</sup></span> </label>
                        	<div>
                        		<select class="form-control" name="task_type" id="task_type" required data-parsley-error-message="Select task type to proceed" onchange="sab_kuchh()">
                        			<option value="">Select Type</option>
                        			<option value="Reading">Reading</option>
                        			<option value="Writing-Submission">Writing with Submission</option>
                        			<option value="Writing-NoSubmission">Writing without Submission</option>
                        			<option value="Viewing">Viewing</option>
									<option value="Direct Evaluation">Direct Evaluation</option>
                        		</select>
                        	</div>
                        </div>
						<div class="form-group">
                        	<label class="">Consider this Task as</label>
                        	<div class="">
                        		<select class="form-control" name="cosider_this_task_as" id="cosider_this_task_as" required data-parsley-error-message="Select task type to proceed">
                        			<option value="Classwork">Classwork</option>
                        			<option value="Homework">Homework</option>
                        			<option value="Assignment">Assignment</option>
                        			<option value="Project">Project</option>
                        			<option value="Presentation">Presentation</option>
                        		</select>
                        	</div>
                        </div>
                         
                        <div class="form-group">
                        	<label class="col-xs-12 pl-0">Assign To</label>
                        	<div class="col-xs-12 d-flex justify-content-between px-4">
                        		<label style="font-weight: 500;"><input class="radio-inline mr-1" checked="" type="radio" name="assign_type" id="section_type" value="section">Section</label>
                        		<label style="font-weight: 500;"><input class="radio-inline mr-1" type="radio" name="assign_type" id="group_type" value="group">Group</label>
                        		<label style="font-weight: 500;"><input class="radio-inline mr-1" type="radio" name="assign_type" id="student_type" value="student">Students Individually</label>
                        	</div>
                        </div>   
                        <div class="form-group">
                        	<div class="row">
	                        	<div class="col-xs-12 p-0">
		                        	<label>Class <span style="color: red;"><sup>*</sup></span> </label>
		                        	<div>
						                <select name="class_id" id="class_id" class="form-control" onchange="getSectionsandSubjects()" required data-parsley-error-message="Select class to proceed">
						                    <option value="">Select Class</option>
						                    <?php
						                        foreach($classesList as $cs => $cl){
						                            echo "<option value='$cl->classId'>$cl->class_name</option>";
						                        }
						                    ?>
						                </select>
		                        	</div>
	                        	</div>
	                        	<div id="sections_div" class="col-xs-12 p-0">
		                        	<label>Section <span style="color: red;"><sup>*</sup></span> </label>
		                        	<div>
						                <select name="section_ids[]" id="section_ids" class="form-control" size="3" multiple="" required="" disabled="true">
						                 
						                </select>
		                        	</div>
	                        	</div>
	                        	<div id="groups_div" class="col-xs-6 pr-0" style="display: none;">
		                        	<label>Group <span style="color: red;"><sup>*</sup></span> </label>
						            <select name="group_id" id="group_id" class="form-control" disabled="true"></select>
	                        	</div>
	                        	<div id="student_div" class="col-xs-6 pr-0" style="display: none;">
		                        	<label>Students <span style="color: red;"><sup>*</sup></span> </label>
						            <select name="student_ids[]" id="student_ids" size="4" multiple="" class="form-control" disabled="true"></select>
	                        	</div>
                        	</div>
                        </div>
                        <div class="form-group">
                        	<label>Subject <span style="color: red;"><sup>*</sup></span> </label>
                        	<div>
                        		<select class="form-control" name="subject_id" id="subject_id" onchange="getAssessments()" required="" data-parsley-error-message="Select subject to proceed" disabled="true">
                        		</select>
                        	</div>
                        </div>
                        <?php
			                $date = date('d-m-Y',strtotime('+2 days'));
			              ?>
                        <div class="form-group no_show">
			                <label>Submission Date <span style="color: red;"><sup>*</sup></span> </label>
			                <div class="input-group date" id="datePicker">
			                  <input type="text" class="form-control" id="task_last_date" name="task_last_date" placeholder="Select Date" required="" value="<?php echo $date; ?>">
			                  <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
			                </div>
			            </div>
			            <div class="form-group no_show">
			                <label >Submission Time</label>
			                <div class="input-group date col-md-9" id="datePicker1222">
			                  <input type="text" class="form-control" id="task_last_time" name="task_last_time" placeholder="Select Time" required="" value="23:50">
			                  <span class="input-group-addon"><span class="glyphicon glyphicon-calendar"></span></span>
			                </div>
			            </div>
						<div class="form-group no_show">
                        	<label class="p-0 d-flex align-items-center">
                        		<input type="checkbox" id="require_evaluation" name="require_evaluation" disabled="true"><span id="require_evaluation_span" style="color:#aba3a3;">Enable Evaluation</span>
                        	</label>
                        	<div id="evaluation_release_status" style="display: none;">
                        		<label class="d-flex mb-0 align-items-center">
	                        		<input type="checkbox" id="release_evaluation" name="do_not_release_evaluation">
	                        		<span>Do not release evaluation automatically</span>
	                        	</label>
	                        	<span class="help-block mt-0">(Do not release evaluation immediately to the students as you evaluate.)</span>
                        	</div>
                        </div>
			            <div class="form-group">
                        	<label>Add E-library Resources </label>
                        	<div>
                        		<input type="hidden" name="resources_selected_ids" id="resources_selected_ids">
                        		<a style="color:#fe970a; text-decoration: underline;cursor: pointer;" onclick="getResources()" data-toggle="modal" data-target="#add_resources_modal">Click Here to Add Resources to this task</a><br>
                        		<span id="resources_added">
                        			
                        		</span>
                        	</div>
                        </div>
						<div class="form-group">
			                <label >Add direct attachments</label>
			                <div class="input-group date col-md-9">
							<input type="file" name="attachment[]" multiple="multiple" id="attachment" class="form-control" accept="application/pdf">

			                </div>
							<?php
								$resource_size= $this->settings->getSetting('resources');
								if($resource_size && !empty($resource_size)) {
									$resource_size= $resource_size->resource_size;
								} else {
									$resource_size= '2MB';
								}
							?>
					<div class="help-block">Max size <?php echo $resource_size; ?>, allowed pdf only</div>
			            </div>
                        <?php 
                        $school_name = $this->settings->getSetting('school_short_name');
                        if($school_name == 'demoschool') { ?>
	                        <div class="form-group">
	                        	<label>Add Assessment </label>
	                        	<div>
	                        		<select class="form-control" name="assessment_id" id="assessment_id" data-parsley-error-message="Select assessment" disabled="true">
	                        		</select>
	                        	</div>
	                        </div>
						<?php } ?>
						<div class="form-group">
							<label for="publish_time__a">Publish Immediate
								<input style="margin: auto 10px;" id="publish_time__a" name="pub_tym" type="radio" class="radio-inline" value="immediate" <?php if($this->settings->getSetting('student_task_publish_time_default') == 'Immediate' || $this->settings->getSetting('student_task_publish_time_default') == null) {echo 'checked';} ?>>
							</label>
							<!-- <span style="opacity: 0;">PP</span> -->
							<label for="publish_time__b">Publish Later
								<input style="margin: auto 10px;" id="publish_time__b" name="pub_tym" type="radio" class="radio-inline" value="later" <?php if($this->settings->getSetting('student_task_publish_time_default') == 'Later') {echo 'checked';} ?>>
							</label>
						</div>
					<div class="form-group" id="p_det_tym" style="display: <?php if($this->settings->getSetting('student_task_publish_time_default') == 'Immediate' || $this->settings->getSetting('student_task_publish_time_default') == null) {echo 'none';} else {echo 'auto';} ?>;">
					<label >Publish Date & Time <font color="red">*</font></label>
					<div class="">
						<input type="text" maxlength="10" class="form-control" value="<?php echo date('d-m-Y',time()); ?>" id="publishing_date_v2" name="publishing_date_v2">
					</div><br>
					<div class="">
						<input type="time" maxlength="8" class="form-control" value="<?php if($this->settings->getSetting('student_task_publish_time_default') == 'Immediate' ) echo date('h:i A',time()); else echo date('H:i', strtotime($default_publish_time)); ?>" id="publishing_time_v2" name="publishing_time_v2">
					</div>
					</div>

                        <div class="col-xs-12 mt-4">
	                        <center>
	                        	<button style="width: 45%;" type="button" class="btn btn-primary mr-1" id="publish_task_btn">Publish</button>
	            				<button type="button" onclick="resetFormInput()" class="btn btn-danger" style="width: 45%;">Cancel</button>
	                    	</center>
                        </div>                   
                    </div>
                </form>
      		</div>
    	</div>
  	</div>
</div>
<div class="modal fade" id="add_resources_modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:94%;margin:auto;border-radius: .75rem;margin-top: 15%;">
            <div class="modal-header" style="border-top-right-radius: .75rem;border-top-left-radius: .75rem;">
                <h4 class="modal-title">List of Resources <b>Grade (<span id="resource-class-name"></span>)</b></h4>
                <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <div class="modal-body px-0" style="overflow:auto;">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal" id="add_resources_form">
                    	<div class="card-body p-0" id="resources_filter">
                    		<div class="row">
                    			<div class="col-xs-12 pl-0">
									<label>Select Resource Type</label>
									<select class="form-control" name="resource_type_modal" id="resource_type_modal" onchange="getSubjectResources()">
									<option value="all">All Types</option>
									<?php 
										foreach ($resource_types as $key => $value) {
											echo '<option value="'.$value->resource_type.'">'.ucwords($value->resource_type).'</option>';
										}
									?>
									</select>
								</div>

									<div class="col-xs-12 pl-0">
										<label>Select Subject</label>
										<select class="form-control" name="resource_subject_id" id="resource_subject_id" onchange="getSubjectResources()">
										</select>
									</div>

									<div class="col-xs-12 pl-0">
							          <label>Date</label>
							          <div id="reportrange" class="dtrange" style="width: 100%">
							              <span></span>
							              <input type="hidden" id="r_from_date">
							              <input type="hidden" id="r_to_date">
							          </div>
							        </div>
							</div>
                    	</div>
						<label class="mt-3 mb-2">Added Resource(s)</label>
                    	<div class="card-body" id="temp_selected_resources" style="border: 1px solid #ccc;border-radius: .8rem;margin-bottom: 1.5rem;min-height: 5rem;">
                    		Resources not added.
                    	</div>
						<label class="mt-3 mb-2"><b>Resource(s)</b></label>
                        <div class="card-body p-2" id="resources_body" style="height:23rem; overflow-y: scroll;border: 1px solid #ccc;border-radius: .8rem;margin-bottom: 1.5rem;">

                        </div>
                    </form>
                </div>
            </div> 
            <div class="modal-footer" id="btns_modal" style="border-bottom-left-radius: .75rem;border-bottom-right-radius: .75rem;">
            	
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<div class="visible-xs">
  <a href="<?php echo site_url('student_tasks/tasks');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
	

	document.getElementById('attachment').addEventListener('change', function(event) {
		const files = event.target.files;
		const maxSize =  parseInt('<?php echo $resource_size; ?>'.replace('MB', ''));
		let allValid = true;

		for (let i = 0; i < files.length; i++) {
			const file = files[i];
			// Check file type
			if (file.type !== 'application/pdf') {
				allValid = false;
				alert(`File "${file.name}" is not a PDF.`);
				
				break;
			}

			// Check file size
			var file_size= parseFloat(file.size / 1024 / 1024);
			if (file_size > maxSize) {
				allValid = false;
				alert(`File "${file.name}" exceeds the size limit of <?php echo $resource_size; ?> MB.`);
				break;
			}
		}

		if (allValid) {
			$("#publish_task_btn").show();
			// alert('All files are valid.');
		} else {
			$("#publish_task_btn").hide();
		}
	});
</script>

<script>
	$(document).ready(function() {
        $("#publishing_date_v2").datepicker({
            todayBtn: "linked",
            language: "it",
            autoclose: true,
            todayHighlight: true,
            format: 'dd-mm-yyyy',
            orientation: "top",
            startDate: "today"
        });
		if('<?php echo$this->settings->getSetting('student_task_publish_time_default'); ?>' == 'Later') {
			$("#publishing_time_v2").val('<?php echo date('H:i', strtotime($default_publish_time)); ?>');
		}
    });

	$("input[name='pub_tym']").click(function() {
		$("#publishing_date_v2").val('<?php echo date('d-m-Y', time()) ?>');
		var value= $(this).val();
		if(value == 'later') {
			$("#publishing_time_v2").val('<?php echo date('H:i', strtotime($default_publish_time)); ?>');
			$("#p_det_tym").show();
		} else {
			$("#publishing_time_v2").val('<?php echo date('H:i', time()) ?>');
			$("#p_det_tym").hide();
		}
	});

	function sab_kuchh() {
		enableDisableEvaluation();
		enableDisableSubmissionDate();
		if($("#task_type").val() == 'Writing-Submission') {
			$(".no_show").show()
		} else {
			$(".no_show").hide()
		}

	}

</script>
<style type="text/css">
.modal-open{
	overflow:inherit;
}
	.circular-panel-xs {
    overflow-y: hidden;
    overflow-wrap: break-word;
    min-height: 50px;
    padding-left: 5px !important;
    padding-right: 5px !important;
    border-bottom: 1px solid #ccc;
}
 #video-player{
    object-fit: cover;
    width: 100%;
    height: 500px;
}
	.resources_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}
	.resources_main_class {
		padding: .4rem 1.4rem;
	    border-radius: 20rem;
	    margin: 3px;
	    display: inline-block;
	    cursor: pointer;
	    background: #e0f1ff;
	    color: #000000;
	}

	.names {
	border: 1px solid #ccc;
    margin-bottom: .5rem;
    border-radius: 10px;
    display: flex;
    height: 8rem;
    overflow: auto;
    padding: .5rem 0.2rem;
	}
	.dialogWide > .modal-dialog {
    	width: 50% !important;
    	margin-left: 25%;
	}
	.list-group-item{
        margin-bottom: 1px;
    }
    .label-default,.label-success,.label-danger {
	    cursor: pointer;
	}
	.list-group-item.active_new{
	    background-color: #ebf3f9;
	    border-color: #ebf3f9;
	    color: #737373;
	}
	.list-group-item.active_new, .list-group-item.active_new:hover, .list-group-item.active_new:focus{
	    background: #ebf3f9;
	    color: #737373;
	}
	.list-group-item{
		border:none;
	}
	.loaderclass {
		border: 8px solid #eee;
		border-top: 8px solid #7193be;
		border-radius: 50%;
		width: 48px;
		height: 48px;
		position: fixed;
		z-index: 1;
		animation: spin 2s linear infinite;
		margin-top: 30%;
		margin-left: 40%;
		position: absolute;
		z-index: 99999;
	}
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	.active_new{
		background: #6893ca;
	}
	.discard{
		background: #C82333;
	}
	.new_circleShape_buttons {
    padding: .35rem .55rem;
    border-radius: 50%;
    font-size: 16px;
    height: 3rem;
    width: 3rem;
    text-align: center;
    vertical-align: middle;
    box-shadow: 0px 2px 8px #ccc;
    cursor: pointer;
	}
  .loader-background {
	    width: 100%;
	    height: 25%;            
	    position: absolute;
	    display: none;
	    top: 0;
	    left: 0;
	    opacity: 0.8;
	    z-index: 10;
	    background-color: #000;
	    border-radius: 8px;
	}
	.unread_box_no_style_new{
		position: relative;
        min-height: 4.6rem;
        border-radius: 8px;
        padding: 12px 20px !important;
        background-color: #f5f5f5
    }
    .panel_title_new_style1{
        font-size: 16px !important;
        color: #bfbfbf !important;
        font-weight: 400 !important;
    }
	.new_circleShape {
     padding: 3px; 
    border-radius: 50%;
    color: white;
    font-size: 22px;
    height: 3.6rem;
    width: 3.6rem;
    text-align: center;
    vertical-align: middle;
	}

.new_circleShape_leftBtn {
    /* padding: 8px 14px; */
    border-radius: 50%;
    color: white;
    font-size: 22px;
    height: 3.6rem;
    width: 3.6rem;
    text-align: center;
    vertical-align: middle;
    line-height: 1.6;
}

	.btn .fa{
		margin-right: 0px;
	}
	.label{
		border-radius: .45em;
	}
	.fa-check-circle{
		color: white;
	}
	/* .btn-primary, .btn-danger,.btn-warning,.btn-success{
		border-radius: .65rem;
	} */
	.form-control{
		border-radius: .45rem;
	}
	.input-group-addon{
		border-radius: .45rem;
	}
	p{
		margin-bottom: .5rem;
	}
	input[type=checkbox]{
		margin: 0px 4px;
	}
	label{
		font-size: 14px;		
	}
</style>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">
  $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(29, 'days'),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#r_from_date').val(start.format('DD-MM-YYYY'));
        $('#r_to_date').val(end.format('DD-MM-YYYY'));
        var subject_id = $("#resource_subject_id").val();
        getResources(subject_id);
    });
  
  $("#reportrange span").html(moment().subtract(29, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#r_from_date').val(moment().subtract(29, 'days').format('DD-MM-YYYY'));
  $('#r_to_date').val(moment().format('DD-MM-YYYY'));
</script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/plugins/summernote/summernote.js"></script>
<script>

function applyTemplate() {
	// $(".note-editor").css('width', '700px');
      var templateName = $("#templateName option:selected").val();
      var loading = '<div class="text-center" style="margin-top:15%;"><div class="spinner-border" role="status" style="width: 6rem; height: 6rem;"><span class="sr-only">Loading...</span></div></div>';
      $.ajax({
        url: '<?php echo site_url('student_tasks/tasks/getTemplate'); ?>',
        data: {
          'templateName': templateName
        },
        type: "post",
        beforeSend: function() {
          $(".summernote").code(loading);
        },
        success: function(data) {
          templateData = $.parseJSON(data);
          $('.summernote').code(templateData);
        },
        error: function(err) {
          console.log(err);
        }
      });
    }

var selected_resources_count=0;
$(document).ready(function() {
	$('#datePicker,#task_last_date').datepicker({
		format: 'dd-M-yyyy',
		"autoclose": true,
		startDate: new Date()
	});

	$('#datePicker1,#task_last_time').datetimepicker({
		format: 'HH:mm',
	});

	document.querySelectorAll('input[name="assign_type"]').forEach((elem) => {
	    elem.addEventListener("change", function(event) {
	      	var item = event.target.value;
	      	toggleGroup(item);
	    });
	});
	
	$("#publish_task_btn").click(function(){
		
		var $form = $('#add_task_form');
		let publishing_date_v22= $("#publishing_date_v2").val();
		if(!publishing_date_v22.toString().trim()) {
			return Swal.fire({
					icon: 'error',
					title: 'Invalid date selection',
					html: 'The publish date must be in the future.',
					confirmButtonColor: '#d33',
					confirmButtonText: 'Try again'
				});
		}

	    if ($form.parsley().validate()){
			
			var publishing_time_v2= $("#publishing_time_v2").val();
			let isFuture = isFutureTime(publishing_time_v2);
			let is_selected_publish_later= $("input#publish_time__b").is(':checked');
			
			if (!isFuture && is_selected_publish_later) {
				return Swal.fire({
					icon: 'error',
					title: 'Invalid time selection',
					html: 'The publish time must be in the future.',
					confirmButtonColor: '#d33',
					confirmButtonText: 'Try again'
				});
			} else {
				var form= $('#add_task_form')[0];
				$("#publish_task_btn").html("Please Wait...").attr('disabled', true);
				var newForm= new FormData(form);
				newForm.append('body', $('#task_description').code());
				// form.append('body', $('#task_description').code());
				// $("#body").html($('#task_description').code());
				$("#add_task_form").submit();
			}
			
		} else {
			$("#publish_task_btn").html("Publish").attr('disabled', false);
		}
	});
});

function isFutureDate(dateString) {
			if (!dateString) return false; // Handle empty input
			
			// Parse DD-MM-YYYY format
			const [day, month, year] = dateString.split('-');
			const inputDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
			
			// Get current date (at start of day for accurate comparison)
			const currentDate = new Date();
			currentDate.setHours(0, 0, 0, 0);
			inputDate.setHours(0, 0, 0, 0);
			return inputDate > currentDate;
		}

		function isPastDate(dateString) {
			if (!dateString) return false; // Handle empty input
			
			// Parse DD-MM-YYYY format
			const [day, month, year] = dateString.split('-');
			const inputDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
			
			// Get current date (at start of day for accurate comparison)
			const currentDate = new Date();
			currentDate.setHours(0, 0, 0, 0);
			inputDate.setHours(0, 0, 0, 0);
			return inputDate < currentDate;
		}

function isFutureTime(publishingTime) {
	let publishing_date_v2= $("#publishing_date_v2").val();

			if (isFutureDate(publishing_date_v2)) {
				return true;
			}
			if (isPastDate(publishing_date_v2)) {
				return false;
			}

			if (!publishingTime) return false;
			
			// Get current time
			const now = new Date();
			const currentHours = now.getHours();
			const currentMinutes = now.getMinutes();
			
			// Normalize the input (trim, uppercase, remove extra spaces)
			const normalizedTime = publishingTime.trim().toUpperCase().replace(/\s+/g, ' ');
			
			let inputHours, inputMinutes;
			
			// Check for 12-hour format (with AM/PM)
			const twelveHourFormat = /^(\d{1,2}):(\d{2})\s*(AM|PM)?$/i.exec(normalizedTime);
			if (twelveHourFormat) {
				inputHours = parseInt(twelveHourFormat[1], 10);
				inputMinutes = parseInt(twelveHourFormat[2], 10);
				const period = twelveHourFormat[3] || '';
				
				// Convert to 24-hour format
				if (period === 'PM' && inputHours !== 12) {
					inputHours += 12;
				} else if (period === 'AM' && inputHours === 12) {
					inputHours = 0;
				}
			} 
			// Check for 24-hour format
			else {
				const twentyFourHourFormat = /^(\d{1,2}):(\d{2})$/.exec(normalizedTime);
				if (!twentyFourHourFormat) return false; // Invalid format
				
				inputHours = parseInt(twentyFourHourFormat[1], 10);
				inputMinutes = parseInt(twentyFourHourFormat[2], 10);
				
				// Validate 24-hour range
				if (inputHours < 0 || inputHours > 23 || inputMinutes < 0 || inputMinutes > 59) {
					return false;
				}
			}
			
			// Compare hours first, then minutes
			if (inputHours > currentHours) {
				return true;
			} else if (inputHours === currentHours) {
				return inputMinutes > currentMinutes;
			}
			
			return false;
		}

function enableDisableSubmissionDate(){
	var task_type = $("#task_type").val();
	if (task_type=='Viewing') {
		$("#task_last_date").attr("disabled", true);
		$("#task_last_time").attr('disabled', true);
	}
	else{
		$("#task_last_date").attr("disabled", false);
		$("#task_last_time").attr('disabled', false);
	}	
}

function resetFormInput() {
	$('#add_task_form').trigger("reset");
	$("#resources_added").html('');
	$("#require_evaluation").attr('disabled', true);
	$("#section_ids").html('');
	$("#group_id").html('<option value="">Select Group</option>')
	$("#student_ids").html('')
	toggleGroup('section');
	enableDisableEvaluation();
	enableDisableSubmissionDate();
}

function toggleGroup(type) {
	$("#sections_div").hide();
	$("#section_ids").prop('required',false);
	$("#groups_div").hide();
	$("#group_id").prop('required',false);
	$("#student_div").hide();
	$("#student_ids").prop('required',false);
	if(type == 'section') {
		$("#sections_div").show();
		$("#section_ids").prop('required',true);
	} else if(type == 'group') {
		$("#groups_div").show();
		$("#group_id").prop('required',true);
	} else {
		$("#student_div").show();
		$("#student_ids").prop('required',true);
	}
}

function enableDisableEvaluation(){
	var task_type = $("#task_type").val();
	if(task_type=='Writing-Submission'){
		$("#require_evaluation").attr("disabled", false);
		$("#require_evaluation_span").css('color','black');
	}
	else{
		$('#require_evaluation').prop('checked', false);
		$("#require_evaluation").attr("disabled", true);
		$("#require_evaluation_span").css('color','#aba3a3');
		$('#release_evaluation').prop('checked', false);
		$('#evaluation_release_status').hide();
	}	
}

$("#require_evaluation").change(function() {
	if($(this).is(':checked')) {
		$('#evaluation_release_status').show();
	} else {
		$('#release_evaluation').prop('checked', false);
		$('#evaluation_release_status').hide();
	}
});

function publishTask(){
		$("#publish_task").prop('disabled',true);
		var $form = $('#add_task_form');
        if ($form.parsley().validate()){
        	$("#add_task_modal").modal('hide');
            var form = $('#add_task_form')[0];
            var formData = new FormData(form);
              $.ajax({
                url: '<?php echo site_url('student_tasks/Tasks/publishNewTask'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
					$("#publish_task").prop('disabled',false);
					if(+data){
						$(function(){
							var url = '<?php echo site_url('student_tasks/tasks') ?>';
          		window.location.href = url ;
				        });
						var today = new Date();
						var dd = today.getDate();
						var mm = today.getMonth()+1; 
						var yyyy = today.getFullYear();
						if(dd<10) 
						{
						    dd='0'+dd;
						} 
						if(mm<10) 
						{
						    mm='0'+mm;
						} 
						today = dd+'-'+mm+'-'+yyyy;
				        $("#task_name").val('');
				        $("#task_description").val('');
				        $("#task_type").val('');
				        $("#class_id").val('');
				        $("#task_last_date").val(today);
				        $("#subject_id").html('');
				        $("#section_ids").html('');
				        $("#resources_added").html('');
				        $("#resources_body").html('');
				        $("#require_evaluation").prop("checked", false);
				        $("#download_status").prop("checked", false);
				        $("#temp_selected_resources").html('');
					}
					else{
						$(function(){
				          new PNotify({
				              title: 'Warning',
				              text: 'Something Went Wrong',
				              type: 'warning',
				          });
				        });
					}
				
                }
            });
		}
	}

	function getAssessments() {
		var subject_id = $("#subject_id").val();
		$("#assessment_id").html('');
		$("#assessment_id").prop('disabled',true);
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getSubjectAssessments'); ?>',
            data: {'subject_id': subject_id},
            type: "post",
            success: function (data) {
            	var data = $.parseJSON(data);
            	if(data.length){
            		$("#assessment_id").prop('disabled',false);
	            	var assessment_options='<option value="">Select Subject</option>';
	            	for(var i=0;i<data.length;i++){
	            		assessment_options+='<option value="'+data[i].id+'">'+data[i].name+' ('+data[i].total_questions+' questions)</option>';
	            	}
	            	$("#assessment_id").html(assessment_options);
            	}
            },
            error: function (err) {
            	console.log(err);
            }
        });
	}

function getSectionsandSubjects(){
		var class_id = $("#class_id").val();
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getSectionsandSubjects'); ?>',
            data: {'class_id': class_id},
            type: "post",
            success: function (data) {
            	var data = $.parseJSON(data);
            	var sectionsList = data.sectionsList;
            	var subjectsList = data.subjectsList;
            	var groupsList = data.groupsList;
            	var studentsList = data.studentsList;
            	var sections_options='';
            	var stuednt_options = '';
        		var group_options = '<option value="">Select Group</option>';
            	if(subjectsList.length==0){
            		$("#subject_id").html('');
            		$("#section_ids").html('');
            		$("#group_id").html('');
        			$("#student_ids").html('');
            		$("#subject_id").prop('disabled',true);
            		$("#section_ids").prop('disabled',true);
            		$("#group_id").prop('disabled',true);
        			$("#student_ids").prop('disabled',true);
            		bootbox.dialog({
	                	title: "Warning....",
	                    message: "<h4>There are no subjects for the selected Class</h4>",
	                    className: "medium",
	                    buttons: {
	                    		ok: {
	                        	label: "Ok",
	                        	className: 'btn btn-primary'
	                    	}
	                 	}
	                });
            	}
            	else{
            		$("#subject_id").prop('disabled',false);
            		$("#section_ids").prop('disabled',false);
            		$("#group_id").prop('disabled',false);
        			$("#student_ids").prop('disabled',false);
	            	var subjects_options='';
	            	for(var i=0;i<sectionsList.length;i++){
	            		sections_options+='<option value="'+sectionsList[i].sectionId+'">'+sectionsList[i].section_name+'</option>';
	            	}
	            	for(var i=0;i<subjectsList.length;i++){
	            		subjects_options+='<option value="'+subjectsList[i].subject_id+'">'+subjectsList[i].subject_name+'</option>';
	            	}
	            	for(var i=0;i<groupsList.length;i++){
            		group_options+='<option value="'+groupsList[i].id+'">'+groupsList[i].group_name+'</option>';
	            	}
	            	for(var i=0;i<studentsList.length;i++){
	            		stuednt_options+='<option value="'+studentsList[i].id+'">'+studentsList[i].student_name+'</option>';
	            	}
	            	$("#section_ids").html(sections_options);
	            	$("#subject_id").html(subjects_options);
	            	$("#group_id").html(group_options);
	            	$("#student_ids").html(stuednt_options);
            	}
            },
            error: function (err) {
            	console.log(err);
            }
        });
	}

	function getSubjectResources() {
		var subject_id = $("#resource_subject_id").val();
		getResources(subject_id);
	}

	function getResources(sub_id="all"){
		sub_id=$("#subject_id").val() || sub_id;
        // $("#add_resources_modal").modal("show");
        $("#resources_body").html('');
        if(selected_resources_count == 0) {
			$("#temp_selected_resources").html('Resources Not Added.');
		}
        var resource_type = $("#resource_type_modal").val();
        var class_name_temp = $("#class_id option:selected");
        var subject_name_temp = $("#subject_id option:selected");
        var subject_selected = $("#subject_id").val();
        var from_date = $("#r_from_date").val();
        var to_date = $("#r_to_date").val();
	    var subjects = document.getElementById('subject_id').options;
	    // var options = '';
	    var options = '<option selected value="all">All</option>';
	    var selected = '';
	    if(sub_id) {
	    	subject_selected = sub_id;
	    }
	    Array.from(subjects).forEach((subject) => {
	    	selected = '';
	    	if(subject.value == subject_selected) {
	    		selected = 'selected';
	    	}
	    	options += '<option '+selected+' value="'+subject.value+'">'+subject.text+'</option>';
	    });
	    $("#resource_subject_id").html(options);
	    subject_name_temp = $("#resource_subject_id option:selected");
        var class_name=class_name_temp.text();
        var class_id=class_name_temp.val();
        var subject_name=subject_name_temp.text();
        var selected_subject_id=subject_name_temp.val();
        $("#resource-class-name").html(class_name);
        if(selected_subject_id == '') return false;
    	if(class_id == '') return false;
		$.ajax({
            url: '<?php echo site_url('student_tasks/Tasks/getResources'); ?>',
            data: {'resource_type':resource_type,'class_name':class_name,'subject_name':subject_name, 'subject_id': selected_subject_id, 'from_date': from_date, 'to_date':to_date},
            type: "post",
            success: function (data) {
            	var data = $.parseJSON(data);
            	var resources = data.resources;
            	var resourcesList = '';
            	if(resources.length==0){
            		resourcesList+='<div><h4 style="color:#888;"><center>No Resources are available</center></h4></div>'
            	}
            	// <input type="checkbox" style="cursor:pointer;" name="resource_ids[]" class="resources_checkbox" value="${resources[i].id}">
            	for(var i=0;i<resources.length;i++){
            		var date = moment(resources[i].created_on).format('DD-MM-YYYY');
			 		resourcesList+=`<div class="col-md-4" style="padding:5px;">
		              <div class="names">
		                <div style="width: 85%;padding: 5px 10px;">
		                  <b>Name : </b>${resources[i].name}<br>
		                  <b>Type : </b>${resources[i].resource_type}<br>
		                  <b>Date : </b>${date}<br>
		                  <b>Subject : </b>${resources[i].subject_name}
		                </div>
		                <div style="width: 10%;">
		               	<a class="new_circleShape_buttons" onclick="oneStepResources('${resources[i].id}','${resources[i].name}')" style="cursor:pointer;background-color:#fe970a;color:white;padding: .35rem .75rem;"><span class="fa fa-plus" style="line-height:3rem"></span></a>
		                </div>
		              </div>
		            </div>`;
			 	}
			 	var btns_list='<a class="btn btn-danger" style="width:10rem;margin-bottom:3px;" data-dismiss="modal">Cancel</a><button class="btn btn-primary" data-dismiss="modal" style="width:10rem">Add Resources</button>';
			 	$("#resources_body").html(resourcesList);
			 	$("#btns_modal").html(btns_list);
            },
            error: function (err) {
            	console.log(err);
            }
        });
	}
	function oneStepResources(id,name){
		if(selected_resources_count == 0) {
			$("#temp_selected_resources").html('');
		}
		var html='';
		var html_main='';
		if($("#temp_add_btn_"+id).length==0){
			selected_resources_count=selected_resources_count+1;
			html+=`<div id="temp_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_class">
					<input type="hidden" name="resource_ids[]" value="${id}">
						${name}&nbsp;&nbsp;
						<span class="fa fa-times remove"></span>
					</div>`;
			html_main+=`<div id="main_add_btn_${id}" onclick="removeOneStepResource('add_btn_${id}')" class="resources_main_class">
				<input type="hidden" name="main_resource_ids[]" value="${id}">
					${name}&nbsp;&nbsp;
					<span class="fa fa-times remove"></span>
				</div>`;
			$("#temp_selected_resources").append(html);
			$("#resources_added").append(html_main);
		}
	}
	function removeOneStepResource(div_id){
		$("#temp_"+div_id).remove();
		$("#main_"+div_id).remove();
		selected_resources_count--;
		if(selected_resources_count == 0) {
			$("#temp_selected_resources").html('Resources Not Added.');
		}
	}
	function confirmResources(){
		var resources_ids=[];
		$('input:checkbox.resources_checkbox').each(function () {
			if(this.checked){
				resources_ids.push($(this).val());
			}
	  	});
	  	resources_ids_string = JSON.stringify(resources_ids);
	  	$("#resources_selected_ids").val(resources_ids_string);
	  	$.ajax({
			url:'<?php echo site_url('student_tasks/Tasks/getSelectedResources') ?>',
			type:'post',
			data: {'resources_ids_string':resources_ids_string},
			success : function(data){
				var data = $.parseJSON(data);
            	var resources = data.resources;
            	var resourcesSelectedList = '';
            	for(var i=0;i<resources.length;i++){
            		resourcesSelectedList+=resources[i].name+',';
			 	}
			 	$("#resources_added").html(resourcesSelectedList);
			}
      	});
	}
</script>