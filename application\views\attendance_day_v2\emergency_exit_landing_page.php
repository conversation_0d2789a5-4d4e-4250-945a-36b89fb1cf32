
<!-- Add these script references at the top of your file or in the head section -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<!-- DataTables Buttons Extension -->
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2');?>">Attendance</a></li>
    <li>Emergency Exit</li>
</ul>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2');?>">
                                <span class="fa fa-arrow-left"></span>
                            </a> 
                            Emergency Exit
                        </h3>
                        <div class="ml-auto">
                            <a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/create_emergency_exit');?>" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Create Emergency Exit
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Date Range Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Date Range</label>
                                <div id="reportrange" class="dtrange" style="width: 100%">                                            
                                    <span></span>
                                    <input type="hidden" id="from_date">
                                    <input type="hidden" id="to_date">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Section</label>
                                <select id="class_section_id" class="form-control">
                                    <option value="">All Sections</option>
                                    <?php foreach ($class_section as $section): ?>
                                    <option value="<?= $section->sectionID ?>"><?= $section->class_name.$section->section_name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4" style="margin-top:24px;">
                            <button type="button" class="btn btn-primary" id="search_btn">
                                <i class="fa fa-download"></i> <span id="search_btn_text">Get</span>
                            </button>
                        </div>
                    </div>

                    <!-- Table for Emergency Exit Records -->
                    <div class="table-responsive">
                        <table id="emergency_exit_table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Student Name</th>
                                    <th>Class/Section</th>
                                    <th>Transport Route</th>
                                    <th>Pick-up By</th>
                                    <th>Parent Contact</th>
                                    <th>Pick-up DateTime</th>
                                    <th>Remarks</th>
                                    <th>Allowed By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here by DataTables -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewDetailsModal" tabindex="-1" role="dialog" aria-labelledby="viewDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 50%; margin: auto;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewDetailsModalLabel">
                    <i class="fa fa-info-circle"></i> Emergency Exit Details
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="details-container">
                    <div class="detail-item">
                        <label class="detail-label">Student Information</label>
                        <div class="detail-content">
                            <div class="detail-row">
                                <span class="detail-key">Name:</span>
                                <span class="detail-value" id="modal_student_name"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-key">Class/Section:</span>
                                <span class="detail-value" id="modal_class_section"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-key">Transport Route:</span>
                                <span class="detail-value" id="modal_transport_route"></span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">Pick-up Information</label>
                        <div class="detail-content">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="detail-row">
                                        <span class="detail-key">Picked up by</span>
                                        <span class="detail-value" id="modal_pickup_by"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-key">Contact</span>
                                        <span class="detail-value" id="modal_parent_contact"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-key">Date & Time</span>
                                        <span class="detail-value" id="modal_pickup_datetime"></span>
                                    </div>
                                </div>
                                <div class="col-md-6 text-center">
                                    <div id="photo_container" class="position-relative" style="display: none;">
                                        <div class="photo-loader">
                                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                                        </div>
                                        <img id="modal_photo" class="img-fluid rounded shadow" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                    <div id="default_photo" class="position-relative" style="display: none;">
                                        <img class="img-fluid rounded shadow" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <label class="detail-label">Additional Information</label>
                        <div class="detail-content">
                            <div class="detail-row">
                                <span class="detail-key">Remarks:</span>
                                <span class="detail-value" id="modal_remarks"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-key">Allowed by:</span>
                                <span class="detail-value" id="modal_allowed_by"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-key">Authorized by:</span>
                                <span class="detail-value" id="modal_authorized_by"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.modal-header {
    border-radius: 8px 8px 0 0;
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 1rem 1.5rem;
}

/* Details Container Styles */
.details-container {
    max-width: 800px;
    margin: 0 auto;
}

.detail-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.detail-content {
    padding: 0.5rem 0;
}

.detail-content .row {
    margin: 0;
}

.detail-content .col-md-6 {
    padding: 0 15px;
}

.detail-row {
    display: flex;
    margin-bottom: 0.75rem;
    line-height: 1.5;
    align-items: flex-start;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-key {
    color: #6c757d;
    font-weight: 600;
    min-width: 120px;
    padding-right: 1rem;
}

.detail-value {
    color: #212529;
    flex: 1;
}

/* Photo Container Styles */
#modal_photo_container {
    margin-bottom: 2rem;
}

#modal_photo {
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

#modal_photo:hover {
    transform: scale(1.02);
}

/* Button Styles */
.btn {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Add these styles to your existing CSS */
.photo-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 5px;
}

#photo_container {
    min-height: 150px;
    min-width: 150px;
    display: inline-block;
    margin-top: 5px;
}

#modal_photo {
    position: relative;
    z-index: 2;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#default_photo img {
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    opacity: 0.7;
}

/* Enhanced Scrollbar Styles for DataTable */
.table-responsive::-webkit-scrollbar {
    width: 16px;
    height: 16px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 8px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 8px;
    border: 2px solid #f1f1f1;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.table-responsive::-webkit-scrollbar-corner {
    background: #f1f1f1;
}

/* For Firefox */
.table-responsive {
    scrollbar-width: auto;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* DataTable specific styling for horizontal scrolling */
.dataTables_wrapper {
    overflow-x: auto;
    width: 100%;
}

.dataTables_scroll {
    overflow-x: auto;
    width: 100%;
}

.dataTables_scrollBody {
    overflow-x: auto;
    overflow-y: auto;
    max-height: 500px;
}

/* Table styling for better scrolling experience */
#emergency_exit_table {
    width: 100% !important;
    min-width: 1200px; /* Ensure minimum width for proper scrolling */
    table-layout: fixed;
}

#emergency_exit_table th,
#emergency_exit_table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 8px 12px;
    vertical-align: middle;
}

/* Specific column width adjustments */
#emergency_exit_table th:nth-child(1),
#emergency_exit_table td:nth-child(1) {
    width: 50px !important;
    min-width: 50px;
    max-width: 50px;
}

#emergency_exit_table th:nth-child(2),
#emergency_exit_table td:nth-child(2) {
    width: 150px !important;
    min-width: 150px;
    max-width: 150px;
}

#emergency_exit_table th:nth-child(3),
#emergency_exit_table td:nth-child(3) {
    width: 120px !important;
    min-width: 120px;
    max-width: 120px;
}

#emergency_exit_table th:nth-child(4),
#emergency_exit_table td:nth-child(4) {
    width: 130px !important;
    min-width: 130px;
    max-width: 130px;
}

#emergency_exit_table th:nth-child(5),
#emergency_exit_table td:nth-child(5) {
    width: 140px !important;
    min-width: 140px;
    max-width: 140px;
}

#emergency_exit_table th:nth-child(6),
#emergency_exit_table td:nth-child(6) {
    width: 120px !important;
    min-width: 120px;
    max-width: 120px;
}

#emergency_exit_table th:nth-child(7),
#emergency_exit_table td:nth-child(7) {
    width: 150px !important;
    min-width: 150px;
    max-width: 150px;
}

#emergency_exit_table th:nth-child(8),
#emergency_exit_table td:nth-child(8) {
    width: 200px !important;
    min-width: 200px;
    max-width: 200px;
    white-space: normal; /* Allow wrapping for remarks */
    word-wrap: break-word;
}

#emergency_exit_table th:nth-child(9),
#emergency_exit_table td:nth-child(9) {
    width: 120px !important;
    min-width: 120px;
    max-width: 120px;
}

#emergency_exit_table th:nth-child(10),
#emergency_exit_table td:nth-child(10) {
    width: 100px !important;
    min-width: 100px;
    max-width: 100px;
}

.table-responsive {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-x: auto;
}

/* Style for the horizontal scrollbar */
.dataTables_wrapper .dataTables_scroll {
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Ensure buttons don't wrap */
#emergency_exit_table .btn {
    white-space: nowrap;
    min-width: 80px;
    font-size: 12px;
    padding: 4px 8px;
}

/* DataTables header styling */
.dataTables_scrollHead {
    overflow: visible !important;
    margin-bottom: 0 !important;
}

.dataTables_scrollHeadInner {
    width: 100% !important;
}

/* Ensure proper alignment of scrollable content */
.dataTables_scrollBody table {
    width: 100% !important;
    margin-top: 0 !important;
}

/* Remove gap between header and body */
.dataTables_scrollBody {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Ensure no gap in the wrapper */
.dataTables_wrapper .dataTables_scroll {
    margin: 0 !important;
    padding: 0 !important;
}

/* Remove any default table margins */
#emergency_exit_table {
    margin: 0 !important;
    border-collapse: collapse !important;
}

/* Aggressive gap removal for DataTables */
.dataTables_wrapper * {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.dataTables_scrollHead,
.dataTables_scrollBody,
.dataTables_scrollFoot {
    margin: 0 !important;
    padding: 0 !important;
    border-spacing: 0 !important;
}

/* Target the specific table elements */
.dataTables_scrollHead table,
.dataTables_scrollBody table,
.dataTables_scrollFoot table {
    margin: 0 !important;
    border-spacing: 0 !important;
    border-collapse: collapse !important;
}

/* Remove any gaps in the scroll container */
.dataTables_scroll {
    margin: 0 !important;
    padding: 0 !important;
    border-spacing: 0 !important;
}

/* Ensure table headers and cells have no extra spacing */
#emergency_exit_table th,
#emergency_exit_table td {
    margin: 0 !important;
    padding: 8px 12px !important;
    border-spacing: 0 !important;
    vertical-align: middle;
}

/* Add some spacing for better readability */
#emergency_exit_table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Ensure the table container has proper overflow handling */
.table-responsive {
    position: relative;
}

/* Fix for DataTables responsive issues */
.dataTables_wrapper .dataTables_scroll {
    clear: both;
}

/* Ensure proper display of ellipsis for long text */
#emergency_exit_table td {
    position: relative;
}

#emergency_exit_table td[title] {
    cursor: help;
}

/* DataTables Buttons Styling - Following attendance_day_v2 pattern */
div.dt-buttons,
.dataTables_wrapper .dt-buttons {
    float: right;
    margin-bottom: 10px;
}

.dataTables_filter input {
    background-color:#f8f9fa;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
    padding: 4px 8px;
}

.dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
}

.dt-buttons {
    font-size: 14px;
    background: cornflowerblue;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize date range picker
    $("#reportrange").daterangepicker({
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment()
    }, function(start, end) {
        $('#reportrange span').html(start.format('DD-MM-YYYY') + ' to ' + end.format('DD-MM-YYYY'));
        $('#from_date').val(start.format('YYYY-MM-DD'));
        $('#to_date').val(end.format('YYYY-MM-DD'));
    });
    
    // Set initial values
    $('#reportrange span').html(moment().subtract(29, 'days').format('DD-MM-YYYY') + ' to ' + moment().format('DD-MM-YYYY'));
    $('#from_date').val(moment().subtract(29, 'days').format('YYYY-MM-DD'));
    $('#to_date').val(moment().format('YYYY-MM-DD'));
    
    // Initialize DataTable with AJAX source
    var dataTable = $('#emergency_exit_table').DataTable({
        "serverSide": false,
        "ajax": {
            "url": "<?php echo site_url('attendance_day_v2/attendance_day_v2/get_emergency_exit_records'); ?>",
            "type": "POST",
            "data": function(d) {
                d.from_date = $('#from_date').val();
                d.to_date = $('#to_date').val();
                d.class_section_id = $('#class_section_id').val();
            },
            "dataSrc": function(json) {
                // Check if the response is valid and has data
                if (!json || typeof json !== 'object') {
                    console.error('Invalid JSON response:', json);
                    return [];
                }
                
                // If the response is an array, return it directly
                if (Array.isArray(json)) {
                    return json;
                }
                
                // If the response has a data property, return that
                if (json.data && Array.isArray(json.data)) {
                    return json.data;
                }
                
                // Otherwise, return an empty array
                console.error('Unexpected JSON structure:', json);
                return [];
            }
        },
        "columns": [
            { "data": null, "render": function(data, type, row, meta) { return meta.row + 1; }, "width": "50px" },
            { "data": "student_name", "defaultContent": "", "width": "150px", "render": function(data, type, row) {
                if (type === 'display' && data && data.length > 20) {
                    return '<span title="' + data + '">' + data.substring(0, 20) + '...</span>';
                }
                return data || '';
            }},
            { "data": "class_section", "defaultContent": "", "width": "120px" },
            { "data": "transport_route", "defaultContent": "", "width": "130px", "render": function(data, type, row) {
                if (type === 'display' && data && data.length > 15) {
                    return '<span title="' + data + '">' + data.substring(0, 15) + '...</span>';
                }
                return data || '';
            }},
            { "data": null, "render": function(data, type, row) {
                if (!row.pickup_name && !row.emergency_exit_pickup_by) return "";
                var pickupText = (row.pickup_name || "") + ' (' + (row.emergency_exit_pickup_by || "") + ')';
                if (type === 'display' && pickupText.length > 18) {
                    return '<span title="' + pickupText + '">' + pickupText.substring(0, 18) + '...</span>';
                }
                return pickupText;
            }, "width": "140px"},
            { "data": "parent_contact", "defaultContent": "", "width": "120px" },
            { "data": "emergency_exit_time", "defaultContent": "", "width": "150px" },
            { "data": "emergency_exit_remarks", "defaultContent": "", "width": "200px", "render": function(data, type, row) {
                if (type === 'display' && data && data.length > 30) {
                    return '<span title="' + data + '">' + data.substring(0, 30) + '...</span>';
                }
                return data || (type === 'display' ? 'No remarks' : '');
            }},
            { "data": "allowed_by_name", "defaultContent": "", "width": "120px", "render": function(data, type, row) {
                if (type === 'display' && data && data.length > 15) {
                    return '<span title="' + data + '">' + data.substring(0, 15) + '...</span>';
                }
                return data || '';
            }},
            {
                "data": null,
                "render": function(data, type, row) {
                    if (type === 'export') {
                        return 'View Details';
                    }
                    return '<button type="button" class="btn btn-info btn-sm view-details" data-id="' + row.emergency_exit_id + '">View Details</button>';
                },
                "width": "100px"
            }
        ],
        "paging": true,
        "searching": true,
        "ordering": false,
        "info": true,
        "autoWidth": false,
        "responsive": false,
        "scrollX": false,
        "scrollCollapse": false,
        "language": {
            "search": "",
            "searchPlaceholder": "Enter Search...",
            "emptyTable": "No records found"
        },
        "dom": 'lBfrtip',
        "buttons": [
            {
                extend: 'excelHtml5',
                text: 'Excel',
                title: function() {
                    var from_date = $('#from_date').val();
                    var to_date = $('#to_date').val();
                    var section_name = $('#class_section_id option:selected').text() || 'All Sections';
                    return 'Emergency Exit Records - ' + from_date + ' to ' + to_date + ' - ' + section_name;
                },
                filename: function() {
                    var from_date = $('#from_date').val();
                    var to_date = $('#to_date').val();
                    var section_name = $('#class_section_id option:selected').text() || 'All Sections';
                    return 'Emergency_Exit_Records_' + from_date + '_to_' + to_date + '_' + section_name.replace(/[^a-zA-Z0-9]/g, '_');
                },
                className: 'btn btn-info',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8],
                    format: {
                        body: function(data, row, column, node) {
                            // For columns with render functions, get the raw data
                            var table = $('#emergency_exit_table').DataTable();
                            var rowData = table.row(row).data();

                            switch(column) {
                                case 1: // Student Name
                                    return rowData.student_name || '';
                                case 3: // Transport Route
                                    return rowData.transport_route || '';
                                case 4: // Pick-up By
                                    if (!rowData.pickup_name && !rowData.emergency_exit_pickup_by) return "";
                                    return (rowData.pickup_name || "") + ' (' + (rowData.emergency_exit_pickup_by || "") + ')';
                                case 7: // Emergency Exit Remarks
                                    return rowData.emergency_exit_remarks || '';
                                case 8: // Allowed By Name
                                    return rowData.allowed_by_name || '';
                                case 9: // Actions
                                    return 'View Details';
                                default:
                                    return data;
                            }
                        }
                    }
                }
            }
        ],
        "error": function(xhr, error, thrown) {
            console.error('DataTables error:', error, thrown);
            // Reset search button state on error
            resetSearchButton();
        },
        "initComplete": function(settings, json) {
            // Adjust the search filter container
            $('#emergency_exit_table_filter').css({
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'flex-end'
            });
            
            // Make sure the input is properly aligned
            $('#emergency_exit_table_filter input').css({
                'margin-right': '0px'
            });
        }
    });
    
    // Search button click handler
    $('#search_btn').on('click', function() {
        // Prevent double clicks
        if ($(this).prop('disabled')) {
            return;
        }
        
        // Show loading state
        $(this).prop('disabled', true);
        $('#search_btn_text').text('Please Wait...');
        $(this).find('i').removeClass('fa-download').addClass('fa-spinner fa-spin');
        
        dataTable.ajax.reload(function() {
            // Reset button state after data is loaded
            resetSearchButton();
            
            // Redraw the table to ensure proper column widths
            dataTable.columns.adjust().draw();
            
            // Ensure horizontal scroll is properly initialized
            setTimeout(function() {
                dataTable.columns.adjust();
            }, 100);
        });
    });
    
    // Function to reset search button state
    function resetSearchButton() {
        $('#search_btn').prop('disabled', false);
        $('#search_btn_text').text('Get');
        $('#search_btn').find('i').removeClass('fa-spinner fa-spin').addClass('fa-download');
    }
    
    // Handle window resize to adjust column widths
    $(window).on('resize', function() {
        if (dataTable) {
            dataTable.columns.adjust();
        }
    });
    
    // Initialize tooltips for truncated text
    function initializeTooltips() {
        $('#emergency_exit_table tbody').on('mouseenter', 'td span[title]', function() {
            $(this).tooltip({
                placement: 'top',
                trigger: 'hover'
            });
        });
    }
    
    // Call tooltip initialization after table is drawn
    dataTable.on('draw', function() {
        initializeTooltips();
    });
    
    // Handle View Details button click
    $('#emergency_exit_table').on('click', '.view-details', function() {
        var id = $(this).data('id');
        
        // Reset modal content before showing
        resetModalContent();
        
        // Show loading state
        $('#viewDetailsModal').modal('show');
        
        // Fetch details from backend
        $.ajax({
            url: "<?php echo site_url('attendance_day_v2/Attendance_day_v2/get_emergency_exit_details'); ?>",
            type: "POST",
            data: { id: id },
            dataType: "json",
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    console.log('Response data:', data);
                    
                    // Update individual elements instead of replacing the entire modal body
                    $('#modal_student_name').text(data.student_name || '');
                    $('#modal_class_section').text(data.class_section || '');
                    $('#modal_transport_route').text(data.transport_route || '');
                    $('#modal_pickup_by').text((data.pickup_name || '') + ' (' + (data.emergency_exit_pickup_by || '') + ')');
                    $('#modal_parent_contact').text(data.parent_contact || '');
                    $('#modal_pickup_datetime').text(data.emergency_exit_time || '');
                    $('#modal_remarks').text(data.emergency_exit_remarks || 'No remarks provided');
                    $('#modal_allowed_by').text(data.allowed_by_name || '');
                    $('#modal_authorized_by').text(data.authorized_by_name || 'Not available');
                    
                    // Handle photo
                    var photoContainer = $('#photo_container');
                    var photoImg = $('#modal_photo');
                    console.log('Photo URL:', data.photo_url);
                    
                    if (data.photo_url) {
                        $('#photo_container').show();
                        $('#default_photo').hide();
                        
                        // Create a new image object to test loading
                        var img = new Image();
                        img.onload = function() {
                            if ($('#viewDetailsModal').is(':visible')) {
                                $('.photo-loader').hide();
                                $('#modal_photo').attr('src', data.photo_url);
                            }
                        };
                        img.onerror = function() {
                            if ($('#viewDetailsModal').is(':visible')) {
                                console.error('Failed to load image:', data.photo_url);
                                $('#photo_container').hide();
                                showDefaultPhoto(data.emergency_exit_pickup_by);
                            }
                        };
                        img.src = data.photo_url;
                    } else {
                        $('#photo_container').hide();
                        showDefaultPhoto(data.emergency_exit_pickup_by);
                        console.log('No photo URL available, showing default photo');
                    }
                } else {
                    $('.modal-body').html('<div class="alert alert-danger">Failed to load details. Please try again.</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                $('.modal-body').html('<div class="alert alert-danger">An error occurred while loading details. Please try again.</div>');
            }
        });
    });
    
    // Function to show default photo based on pickup type
    function showDefaultPhoto(pickupType) {
        var defaultPhotoUrl;
        
        if (pickupType === 'Mother') {
            defaultPhotoUrl = 'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/sample_girl_image.png';
        } else {
            // For Father, Other, or any other type
            defaultPhotoUrl = 'https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Admission process/student_image.jpg';
        }
        
        $('#default_photo img').attr('src', defaultPhotoUrl);
        $('#default_photo').show();
    }
    
    // Function to reset modal content
    function resetModalContent() {
        // Clear all text fields
        $('#modal_student_name').text('');
        $('#modal_class_section').text('');
        $('#modal_transport_route').text('');
        $('#modal_pickup_by').text('');
        $('#modal_parent_contact').text('');
        $('#modal_pickup_datetime').text('');
        $('#modal_remarks').text('');
        $('#modal_allowed_by').text('');
        
        // Reset photo containers
        $('#photo_container').hide();
        $('#default_photo').hide();
        $('#modal_photo').attr('src', '');
        $('.photo-loader').show();
    }

    // Reset modal content when modal is hidden
    $('#viewDetailsModal').on('hidden.bs.modal', function() {
        resetModalContent();
    });
    
    // Error handling for DataTables
    $.fn.dataTable.ext.errMode = 'throw';
});
</script>
