<script type="text/javascript">
var gYears = '<?= json_encode($yearinfo)?>';
var gYear = $.parseJSON(gYears);
// console.log(gYear);
// var yearLength = gYear.length;
var yearLength = Object.keys(gYear).length;
// alert(yearLength);
$(document).ready(function() {
    // Mobile debugging and initialization
    if (window.matchMedia('(max-width: 768px)').matches) {

        // Force mobile button functionality
        setTimeout(function() {
            $('.mobile-save-btn').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Trigger the save-step1 functionality manually
                if ($(this).hasClass('save-step1')) {

                    var inputDob = $("input[name='student_dob']").val();
                    var inputClass = $("#class").val();
                    var $form = $('#std-form');

                    if ($form.parsley().validate()) {
                        // Trigger the existing save functionality
                        $('.save-step1').not('.mobile-save-btn').trigger('click');
                    } else {
                        alert('Please fill in all required fields correctly.');
                    }
                }
            });
        }, 1000);
    }

    var dob = '<?php echo date('Y-m-d',strtotime($final_preview->dob)) ;?>';
    var dob_date = '<?php echo $final_preview->dob ?>'
    if (dob != '' && dob_date != '00-00-0000') {
        var age = getAge(dob);
        $('#age_cal').html(age);
    }
});

function get_parent_data() {
    var insert_id = '<?php echo $insert_id ?>';

    var documenttypes = '<?php echo json_encode($documents) ?>';
    var docsArry = $.parseJSON(documenttypes);

    var documents_required_fields = '<?php echo json_encode($documents_required_fields) ?>';
    var required_fields_arr = $.parseJSON(documents_required_fields);

    var admission_form = '<?php echo json_encode($admission_form) ?>';
    var admission_form_arr = $.parseJSON(admission_form);

    var new_document_type = '<?php echo $config_val['document_input_version'] ?>';
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_parent_data'); ?>',
        type: 'post',
        data: {
            'af_id': insert_id
        },
        success: function(data) {
            var resdata = $.parseJSON(data);
            if (new_document_type == 'V1') {
                $('#newDocTabV1').html(_construct_document_table(resdata, docsArry,
                    required_fields_arr, admission_form_arr));
            } else {
                $('#newDocTabV1').html(_construct_document_table_new(resdata, docsArry,
                    required_fields_arr, admission_form_arr));
            }
        }
    });
}

function _construct_document_table(resdata, docsArry, required_fields_arr, admission_form_arr) {
    var html = '<div class="row" id="doc-upload-cards" style="margin-top: 24px;">';
    for (var i = 0; i < docsArry.length; i++) {
        var requiredColor = '';
        var required = '';
        if ($.inArray(docsArry[i], required_fields_arr) !== -1) {
            requiredColor = '<span style="color:red">*</span>';
            required = 'required';
        }
        var filespath = 0;
        var doc_rowId = 0;
        var doc_disabledUpload = '';
        $.each(admission_form_arr, function(key, val) {
            if (val.document_type == docsArry[i]) {
                filespath = 1;
                doc_rowId = val.id;
                doc_disabledUpload = 'disabled';
                required = '';
            }
        });

        var inputId = "doc-upload" + (i + 1);
        var docForId = "document_for" + (i + 1);
        var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
        if (filespath == 1) {

            html += `
                 <div class="col-md-6 mb-4 p-0">
                    <div class="upload-card">
                        <label class="upload-label">${docsArry[i]} ${requiredColor}</label>
                        <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style=" border-radius:6px; border: 1px dashed #D9D1FF;height:10rem" id="document_row_${i+1}">
                            <div class="d-flex align-items-center flex-grow-1" style="min-width:0;">
                                <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:#FF2D2D; border-radius:10px;">
                                    <span style="color:#fff; font-weight:700; font-size:1.1em;">PDF</span>
                                </div>
                                <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                                    ${docsArry[i]}
                                </div>
                            </div>
                            <div class="d-flex align-items-center ms-3" style="gap:16px;">
                                <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="deletedocument_row_new(${doc_rowId},${i+1},'${required}')">
                                    <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                        <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            html += `
                <div class="col-md-6 mb-4 p-0">
                    <div class="upload-card">
                        <label class="upload-label">${docsArry[i]} ${requiredColor}</label>
                        <div class="border border-dashed rounded p-4 text-center position-relative" id="document_row_${i+1}"
                        style="cursor: pointer;border: 1px dashed #D9D1FF;height:10rem;" onclick="document.getElementById('${inputId}').click();">
                            <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                            <div class="d-flex flex-column align-items-center mt-4">
                                <i class="bi bi-upload" style="font-size: 24px;"></i>
                                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                                <small class="text-muted">PNG, JPG or PDF (max.${max_size_string} MB)</small>
                            </div>
                            <input type="file" id="${inputId}" name="document_file_path" style="display:none;" ${required} accept="image/jpeg, image/png, application/pdf" onchange="upload_document_file_path(${i+1}, this,${doc_rowId},${required})" ${doc_disabledUpload}>
                            <input type="hidden" name="document_for" id="${docForId}" value="${docsArry[i]}">
                            <div id="afterSuccessUploadShow${i+1}"></div>
                            <span style="color:red;" id="doc_error${i+1}"></span>
                            <span id="percentage_doc_completed${i+1}" style="font-size: 20px; display: none;"></span>
                        </div>
                    </div>
                </div>`;
        }

    }

    html += `
        <div class="col-md-6 mb-4 p-0 mt-3">
            <div class="upload-card">
                <label class="upload-label"> </label>
                    <div class="add-more-doc-card border border-dashed rounded p-4 text-center"
                        style="cursor: pointer; border: 1px dashed #D9D1FF;height:10rem;display: flex;align-items: center;justify-content: center;"
                        onclick="onAddMoreDocument()">
                        <div class="d-flex justify-content-center align-items-center gap-2">
                            <span style="color: #6c63ff; font-weight: bold; font-size: 18px;">+</span>
                            <span style="color: #6c63ff; font-weight: bold;">Add More Documents</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    html += '</div>';

    return html;
}

window.onAddMoreDocument = function() {
    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: left;margin: 0;">
                    <span>Upload Document</span>
                </div>`,
        input: 'text',
        inputPlaceholder: 'Enter document name',
        showCancelButton: true,
        confirmButtonText: 'Submit',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        customClass: {
            popup: 'terms-confirm-popup mobile-popup-spacing',
            confirmButton: 'swal2-submit-btn',
            cancelButton: 'swal2-cancel-btn'
        },
        buttonsStyling: false,
        didOpen: () => {
            // Add mobile button spacing CSS
            const style = document.createElement('style');
            style.textContent = `
                    @media (max-width: 768px) {
                        .mobile-popup-spacing .swal2-actions {
                            margin-bottom: 20px !important;
                        }
                        .mobile-popup-spacing .swal2-confirm,
                        .mobile-popup-spacing .swal2-cancel {
                            margin-bottom: 10px !important;
                        }
                        .mobile-popup-spacing .swal2-popup {
                            margin-bottom: 20px !important;
                        }
                    }
                `;
            document.head.appendChild(style);

            const title = Swal.getTitle();
            if (title) {
                title.style.paddingTop = '20px';
                title.style.paddingBottom = '10px';
                title.style.textAlign = 'left';
                title.style.paddingLeft = '20px';
                title.style.paddingRight = '20px';
            }

            // Add real-time validation to input field
            const input = Swal.getInput();
            if (input) {
                input.addEventListener('input', function(e) {
                    let value = e.target.value;
                    // Remove any characters that are not letters or spaces
                    let filteredValue = value.replace(/[^a-zA-Z\s]/g, '');

                    // If the value changed, update the input
                    if (value !== filteredValue) {
                        e.target.value = filteredValue;
                    }
                });

                input.addEventListener('keypress', function(e) {
                    // Allow only letters, spaces, backspace, delete, and arrow keys
                    const char = String.fromCharCode(e.which);
                    if (!/[a-zA-Z\s]/.test(char) && e.which !== 8 && e.which !== 46 && e.which !== 37 && e.which !== 39) {
                        e.preventDefault();
                    }
                });

                input.addEventListener('paste', function(e) {
                    // Handle paste events
                    setTimeout(() => {
                        let value = e.target.value;
                        let filteredValue = value.replace(/[^a-zA-Z\s]/g, '');
                        e.target.value = filteredValue;
                    }, 10);
                });
            }
        },
        preConfirm: (value) => {
            if (!value) {
                Swal.showValidationMessage('Please enter a document name');
                return false;
            }

            // Trim whitespace
            value = value.trim();

            // Check if empty after trimming
            if (!value) {
                Swal.showValidationMessage('Please enter a document name');
                return false;
            }

            // Validate: only letters and spaces allowed
            const namePattern = /^[a-zA-Z\s]+$/;
            if (!namePattern.test(value)) {
                Swal.showValidationMessage('Document name can only contain letters and spaces');
                return false;
            }

            // Check minimum length (at least 2 characters)
            if (value.length < 2) {
                Swal.showValidationMessage('Document name must be at least 2 characters long');
                return false;
            }

            // Check maximum length (reasonable limit)
            if (value.length > 50) {
                Swal.showValidationMessage('Document name cannot exceed 50 characters');
                return false;
            }

            return value;
        }
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            addDocumentCard(result.value);
        }
    });
};


function addDocumentCard(docName) {
    // Find the Add More card and insert before it
    var $addMoreCard = $('#doc-upload-cards .add-more-doc-card').closest('.col-md-6');
    var idx = $('.upload-card').length; // For unique IDs

    var inputId = "doc-upload" + (idx + 1);
    var docForId = "document_for" + (idx + 1);

    // For dynamically added documents, set default values
    var doc_rowId = 0; // New document, no existing row ID
    var required = ''; // Dynamically added documents are typically not required

    var cardHtml = `
        <div class="col-md-6 mb-4 p-0">
            <div class="upload-card">
                <label class="upload-label">${docName}</label>
                <div class="border border-dashed rounded p-4 text-center position-relative" id="document_row_${idx+1}"
                    style="cursor: pointer; border: 1px dashed #D9D1FF;height:10rem" onclick="document.getElementById('${inputId}').click();">
                   <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                    <div class="d-flex flex-column align-items-center mt-4">
                        <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                        <small class="text-muted">SVG, PNG, JPG, PDF or GIF (max. 2MB)</small>
                    </div>
                    <input type="file" id="${inputId}" name="document_file_path" style="display:none;" accept="image/jpeg, image/png, image/svg+xml, image/gif, application/pdf" onchange="upload_document_file_path(${idx+1}, this, ${doc_rowId}, '${required}')">
                    <input type="hidden" name="document_for" id="${docForId}" value="${docName}">
                    <span style="color:red;" id="doc_error${idx+1}"></span>
                    <span id="percentage_doc_completed${idx+1}" style="font-size: 20px; display: none;"></span>
                </div>
            </div>
        </div>
    `;
    $(cardHtml).insertBefore($addMoreCard);
}

function _construct_document_table_new(resdata, docsArry, required_fields_arr, admission_form_arr) {
    var documents_arr = [];
    var category = $('#category option:selected').text();
    docsArry.forEach(doc => {
        const { relation, condition } = doc;

        if ($.type(condition) === 'undefined') {
            documents_arr.push(doc);
            return;
        }

        const checks = {
            student: resdata.nationality,
            father: resdata.f_nationality,
            mother: resdata.m_nationality
        };

        const nationality = checks[relation];

        if(condition == 'All' || condition == ''){
            documents_arr.push(doc);
        }else if (nationality === 'Indian' && condition === 'if_nationality_indian') {
            documents_arr.push(doc);
        } else if (nationality && nationality !== 'Indian' && condition === 'if_nationality_other') {
            documents_arr.push(doc);
        }
    });
    var html = '<div class="row" id="doc-upload-cards" style="margin-top: 24px;">';

    for (var i = 0; i < documents_arr.length; i++) {
        var doc = documents_arr[i];
        var requiredColor = '';
        var required = '';

        if (doc.required === true || doc.required === 'true') {
            required = 'required';
            requiredColor = '<span style="color:red">*</span>';
        }

        var filespath = 0;
        var doc_rowId = 0;
        var doc_disabledUpload = '';

        $.each(admission_form_arr, function(key, val) {
            if (val.document_type == doc.name) {
                filespath = 1;
                doc_rowId = val.id;
                doc_disabledUpload = 'disabled';
                // required = '';
            }
        });

        var inputId = "doc-upload" + (i + 1);
        var docForId = "document_for" + (i + 1);
        var isAadharOrPan = doc.view_type && ['aadhar', 'pan'].includes(doc.view_type.toLowerCase());
        var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
        if (filespath == 1) {
            html += `
            <div class="col-md-6 mb-4 p-0">
                <div class="upload-card">
                    <label class="upload-label">${doc.name} ${requiredColor}</label>
                    <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style=" border-radius:6px; border: 1px dashed #D9D1FF;height:10rem" id="document_row_${i+1}">
                        <div class="d-flex align-items-center flex-grow-1" style="min-width:0;">
                            <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:#FF2D2D; border-radius:10px;">
                                <span style="color:#fff; font-weight:700; font-size:1.1em;">PDF</span>
                            </div>
                            <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                                ${doc.name}
                            </div>
                        </div>
                        <div class="d-flex align-items-center ms-3" style="gap:16px;">
                            <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="deletedocument_row_new(${doc_rowId},${i+1},'${required}')">
                                <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                    <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        } else {
            html += `
            <div class="col-md-6 mb-4 p-0">
                <div class="upload-card">
                    <label class="upload-label">${doc.name} ${requiredColor}</label>
                    <div class="border border-dashed rounded p-4 text-center position-relative" id="document_row_${i+1}"
                    style="cursor: pointer;border: 1px dashed #D9D1FF;height:10rem;" ${isAadharOrPan ? `onclick="show_upload_document_modal('${doc.view_type}','${doc.name}','${i+1}','${required}')"` : `onclick="document.getElementById('${inputId}').click();"`}>
                        <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                            <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                        </div>
                        <div class="d-flex flex-column align-items-center mt-4">
                            <span><strong style="color: #6c63ff;">${isAadharOrPan ? 'Upload Document' : 'Click to upload'}</strong></span>
                            <small class="text-muted">${isAadharOrPan ? 'Upload your document and enter the required details' : 'PNG, JPG or PDF (max. '+max_size_string +' MB)'}</small>
                        </div>
                        ${!isAadharOrPan ? `<input type="file" id="${inputId}" name="document_file_path" style="display:none;" ${required} accept="image/jpeg, image/png, application/pdf" onchange="upload_document_file_path(${i+1}, this,${doc_rowId},'${required}')" ${doc_disabledUpload}>` : ''}
                        <input type="hidden" name="document_for" id="${docForId}" value="${doc.name}">
                        <span style="color:red;" id="doc_error${i+1}"></span>
                        <span id="percentage_doc_completed${i+1}" style="font-size: 20px; display: none;"></span>
                    </div>
                </div>
            </div>`;
        }
    }

    html += `
    <div class="col-md-6 mb-4 p-0 mt-3">
        <div class="upload-card">
            <label class="upload-label"> </label>
                <div class="add-more-doc-card border border-dashed rounded p-4 text-center"
                    style="cursor: pointer; border: 1px dashed #D9D1FF;height:10rem;display: flex;align-items: center;justify-content: center;"
                    onclick="onAddMoreDocument()">
                    <div class="d-flex justify-content-center align-items-center gap-2">
                        <span style="color: #6c63ff; font-weight: bold; font-size: 18px;">+</span>
                        <span style="color: #6c63ff; font-weight: bold;">Add More Documents</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    html += '</div>';

    return html;
}

function get_language_selection(selectedClass) {
    var admsettingId = '<?php echo $admission_setting_id  ?>';
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_language_class_wise_data'); ?>',
        type: 'post',
        data: {
            'selectedClass': selectedClass,
            'admsettingId': admsettingId
        },
        success: function(data) {
            var res_data = JSON.parse(data);
            if (res_data != '') {
                $('#language_selection').html(construct_language_selection(res_data));
            }
        }
    });

}

function construct_language_selection(res_data) {
    var langChoices = {
        'lang_1_choice': '<?php echo $final_preview->lang_1_choice; ?>',
        'lang_2_choice': '<?php echo $final_preview->lang_2_choice; ?>',
        'lang_3_choice': '<?php echo $final_preview->lang_3_choice; ?>'
    };

    var html = '';
    // Heading in its own row with margin
    html += `<div class="row p-0"><div class="col-12"><h5 class="mb-5" style="font-weight:600;">Language Selection</h5></div></div>
        <hr style="margin: 0 0 40px 0; border-top: 1px E8E8E8 !important;">`;
    html += `<div class="row">`;
    for (i in res_data) {
        html += `<div class="col-md-4 mb-4 mx-4 p-0">`; // 3 columns in a row, with spacing
        for (j in res_data[i]) {
            if (j != 'required') {
                // Label above select
                html += `<div class="form-group mb-3">`;
                html +=
                    `<label class="form-label mb-2" style="display:block;">${i.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</label>`;
                html += `<select class="form-control" name="${j}" ${res_data[i]['required']}>`;
                html += '<option value="">Select Language</option>';
                for (k in res_data[i][j]) {
                    var selected = '';
                    if (res_data[i][j][k] == langChoices[j]) {
                        selected = 'selected';
                    }
                    html += `<option ${selected} value="${res_data[i][j][k]}">${res_data[i][j][k]}</option>`;
                }
                html += `</select>`;
                html += `</div>`;
            }
        }
        html += `</div>`;
    }
    html += `</div>`;
    return html;
}

function get_subject_details(selectedClass) {
    var admsettingId = '<?php echo $admission_setting_id  ?>';
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_subject_details'); ?>',
        type: 'post',
        data: {
            'selectedClass': selectedClass,
            'admsettingId': admsettingId
        },
        success: function(data) {
            var res_data = JSON.parse(data);
            console.log('Subject details:', res_data);
            if (res_data == 1) {
                $('.subject_btn').show();
            } else {
                $('.subject_btn').hide();
            }
        }
    });
}

function mobileSaveStep6() {
    var $form = $('#document-form');
    var af_id = '<?php echo $final_preview->id ?>';
    var adm_setting_id = '<?php echo $final_preview->admission_setting_id ?>';

    $.ajax({
        url: '<?php echo site_url('admission_controller/check_document_uploaded'); ?>',
        type: 'post',
        data: {
            'af_id': af_id,
            'adm_setting_id': adm_setting_id
        },
        success: function(data) {
            var resData = data.trim();

            if (resData == 0) {
                alert('Upload the documents');
                return false;
            }

            // Validation logic after documents are confirmed
            var required = '<?php echo isset($required_fields['year']['required']) ? $required_fields['year']['required'] : '' ?>';

            if (required == 'required') {
                if (typeof yearLength !== 'undefined' && yearLength == 0) {
                    $('#schooling_year, #schooling_school, #schooling_class, #schooling_board, #school_address, #registration_no, #medium_of_instruction')
                        .removeAttr('required');
                } else if (typeof yearLength !== 'undefined' && yearLength > 0) {
                    var resData = [];
                    for (var k in schoolYears) {
                        var valueCheck = $('#displayschoolName' + schoolYears[k]).html();
                        resData.push(valueCheck);
                    }
                    var found = 0;
                    $.each(resData, function(index, value) {
                        if (value == '') {
                            found = 0;
                            return false;
                        } else {
                            found = 1;
                        }
                    });
                    if (found) {
                        $('.removeAttrFileUpload').removeAttr('required');
                    }else {
                        alert("Enter previous details, then you can 'Save and Preview'.");
                        return false;
                    }
                    if (!$form.parsley().validate()) {
                        alert('Please fill in all required fields correctly.');
                        return false;
                    }
                }
            } else {
                $('#schooling_year, #schooling_school, #schooling_class, #schooling_board, #school_address')
                    .removeAttr('required');
            }

            // Disable button
            $('.save-step3.mobile-save-btn').text('Please wait...').prop('disabled', true);

            // Go to next step
            var currentStep = 5;
            var nextStep = findNextAvailableStep(currentStep);

            if (nextStep === currentStep) {
                nextStep = 6;
            }
            if (nextStep === 6) {
                goto_preview_page('document-form');
            }
            mobileGoToStep(nextStep);

            // Re-enable button
            $('.save-step3.mobile-save-btn').text('Save & Preview').prop('disabled', false);
        }
    });
}

// Function to find the next available step
function findNextAvailableStep(currentStep) {
    var nextStep = currentStep + 1;
    var maxStep = 6; // Maximum possible step

    while (nextStep <= maxStep) {
        if ($('#step-form-' + nextStep).length > 0) {
            console.log('Found next available step:', nextStep);
            return nextStep;
        }
        nextStep++;
    }

    console.log('No next step found, staying at current step');
    return currentStep;
}

// Function to find the previous available step
function findPreviousAvailableStep(currentStep) {
    console.log('findPreviousAvailableStep: Starting from step', currentStep);
    var prevStep = currentStep - 1;
    var minStep = 1; // Minimum possible step

    while (prevStep >= minStep) {
        console.log('findPreviousAvailableStep: Checking step', prevStep);
        var stepExists = $('#step-form-' + prevStep).length > 0;
        console.log('findPreviousAvailableStep: Step', prevStep, 'exists:', stepExists);

        if (stepExists) {
            console.log('Found previous available step:', prevStep);
            return prevStep;
        }
        prevStep--;
    }

    console.log('No previous step found, staying at current step');
    return currentStep;
}

// Custom mobile stepper update function that handles step number mapping correctly
function updateMobileStepperCustom(stepNumber) {
    if (!window.matchMedia('(max-width: 768px)').matches) return;

    console.log('updateMobileStepperCustom called with stepNumber:', stepNumber);

    // Define step titles based on actual step numbers
    const stepTitles = {
        1: 'Student Details',
        2: 'Parent Details',
        3: 'Guardian Details',
        4: 'Medical Details',
        5: 'Document Upload',
        6: 'Preview & Submit'
    };

    // Get total number of available steps
    var totalAvailableSteps = 0;
    for (var i = 1; i <= 6; i++) {
        if ($('#step-form-' + i).length > 0) {
            totalAvailableSteps++;
        }
    }

    // Calculate current step position among available steps
    var currentPosition = 0;
    for (var i = 1; i <= stepNumber; i++) {
        if ($('#step-form-' + i).length > 0) {
            currentPosition++;
        }
    }

    // Calculate progress percentage
    var progressPercentage = (currentPosition / totalAvailableSteps) * 100;

    console.log('Mobile stepper update:', {
        stepNumber: stepNumber,
        title: stepTitles[stepNumber],
        currentPosition: currentPosition,
        totalAvailableSteps: totalAvailableSteps,
        progressPercentage: progressPercentage
    });

    // Update the mobile stepper elements
    var currentStepElement = document.getElementById('mobile-current-step');
    var stepTitleElement = document.getElementById('mobile-step-title');
    var totalStepsElement = document.getElementById('mobile-total-steps');
    var progressBarElement = document.getElementById('mobile-progress-bar');

    if (currentStepElement) {
        currentStepElement.textContent = 'Step ' + currentPosition;
    }

    if (stepTitleElement && stepTitles[stepNumber]) {
        stepTitleElement.textContent = stepTitles[stepNumber];
    }

    if (totalStepsElement) {
        totalStepsElement.textContent = totalAvailableSteps;
    }

    if (progressBarElement) {
        progressBarElement.style.width = progressPercentage + '%';
    }

    // Update icon if the function exists
    if (typeof updateMobileStepIcon === 'function') {
        updateMobileStepIcon(stepNumber);
    }
}

// Mobile-compatible goToStep function
function mobileGoToStep(stepNumber) {
    console.log('mobileGoToStep called with stepNumber:', stepNumber);

    if (window.matchMedia('(max-width: 768px)').matches) {
        // Hide all step forms
        $('.step-form').addClass('d-none');

        // Check if target step form exists
        var targetForm = $('#step-form-' + stepNumber);
        console.log('Target step form #step-form-' + stepNumber + ' exists:', targetForm.length > 0);

        if (targetForm.length === 0) {
            console.error('Target step form does not exist: #step-form-' + stepNumber);
            return;
        }

        // Show the form for the target step
        targetForm.removeClass('d-none');
        console.log('Showing step form:', stepNumber);
        console.log('Step form is now visible:', !targetForm.hasClass('d-none'));

        // Special debugging for document step

        // Update mobile stepper with custom logic
        if (window.matchMedia('(max-width: 768px)').matches) {
            updateMobileStepperCustom(stepNumber);
        }

        // For mobile, we need to manually track the active step
        // since we don't have the desktop stepper elements visible
        $('.step').removeClass('active previous');

        // Try to find and update desktop stepper if it exists (for consistency)
        var $targetStep = $('.step[data-step="' + stepNumber + '"]');
        if ($targetStep.length > 0) {
            $targetStep.addClass('active');

            // Add previous class to earlier steps
            $('.step').each(function() {
                var thisStep = parseInt($(this).attr('data-step'));
                if (thisStep < stepNumber) {
                    $(this).addClass('previous');
                }
            });
        } else {
            // If desktop stepper elements don't exist, create a virtual tracking
            console.log('Desktop stepper elements not found, using mobile-only navigation');
        }

        // Scroll to top of the form for better mobile UX
        $('html, body').animate({
            scrollTop: $('#step-form-' + stepNumber).offset().top - 100
        }, 300);

        console.log('Mobile step navigation completed to step:', stepNumber);
    } else {
        // On desktop, use the original goToStep function
        if (typeof goToStep === 'function') {
            goToStep(stepNumber);
        }
    }
}

// Mobile save functions
function mobileSaveStep1() {
    var inputDob = $("input[name='student_dob']").val();
    var inputClass = $("#class").val();
    var $form = $('#std-form');


    if ($form.parsley().validate()) {
        console.log('Form is valid, proceeding with save...');

        // Get form data
        var form = $('#std-form')[0];
        var formData = new FormData(form);

        // Show loading state
        $('#mobile-paybtn').text('Please wait...').prop('disabled', true);

        // Make AJAX call directly (replicated from update_student_after_dob_check)
        $.ajax({
            url: '<?php echo site_url('admission_controller/updateStudentDetails'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                console.log('Student update response:', data);
                $('#mobile-paybtn').text('Save & Proceed').prop('disabled', false);

                if (data != '') {
                    // Navigate to step 2 (mobile-compatible)
                    mobileGoToStep(2);
                    get_language_selection(inputClass);
                    get_subject_details(inputClass);
                    fill_single_parent();

                } else {
                    alert("Something went wrong in Student data, try again.");
                }
            },
            error: function() {
                console.log('Student update failed');
                $('#mobile-paybtn').text('Save & Proceed').prop('disabled', false);
                alert("Something went wrong, try again.");
            }
        });
    } else {
        console.log('Form validation failed');
        alert('Please fill in all required fields correctly.');
    }
}



function mobileSaveStep4() {
    var $form = $('#guardian-form');

    if ($form.parsley().validate()) {

        var form = $('#guardian-form')[0];
        var formData = new FormData(form);

        // Show loading state
        $('.save-step4.mobile-save-btn').text('Please wait...').prop('disabled', true);

        // Make AJAX call similar to the original save-step4
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_guardian_details'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                $('.save-step4.mobile-save-btn').text('Save & Proceed').prop('disabled', false);

                if (data == 1) {
                    // Navigate to next step (mobile-compatible)
                    var currentStep = 3; // Guardian form is step 3

                    // Use the new function to find the next available step
                    var nextStep = findNextAvailableStep(currentStep);
                    if(nextStep === 6){
                        goto_preview_page('guardian-form');
                    }
                    mobileGoToStep(nextStep);
                } else {
                    alert("Something went wrong, try again.");
                }
            },
            error: function() {
                $('.save-step4.mobile-save-btn').text('Save & Proceed').prop('disabled', false);
                alert("Something went wrong, try again.");
            }
        });
    } else {
        alert('Please fill in all required fields correctly.');
    }
}

function mobileSaveStep5() {
    var $form = $('#medical-form');

    var validationResult = $form.parsley().validate();

    if (validationResult) {
        console.log('Medical form is valid, proceeding...');

        var form = $('#medical-form')[0];
        var formData = new FormData(form);

        // Show loading state
        $('.save-step5.mobile-save-btn').text('Please wait...').prop('disabled', true);

        // Make AJAX call similar to the original save-step5
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_medical_form_details'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                $('.save-step5.mobile-save-btn').text('Save & Proceed').prop('disabled', false);

                if (data == 1) {
                    // Navigate to next step (mobile-compatible)
                    var currentStep = 4; // Medical form is step 4

                    // Use the new function to find the next available step
                    var nextStep = findNextAvailableStep(currentStep);

                    // Additional debugging
                    if (nextStep === 6) {
                        goto_preview_page('medical-form');
                    }
                    mobileGoToStep(nextStep);
                    
                } else {
                    alert("Something went wrong, try again.");
                }
            },
            error: function() {
                $('.save-step5.mobile-save-btn').text('Save & Proceed').prop('disabled', false);
                alert("Something went wrong, try again.");
            }
        });
    } else {
        console.log('Medical form validation failed');

        // Show validation errors more prominently on mobile
        $('.parsley-errors-list').each(function() {
            console.log('Medical form validation error found:', $(this).text());
        });

        alert('Please fill in all required fields correctly.');
    }
}

var dob_maxdate = new Date();
dob_maxdate.setFullYear(dob_maxdate.getFullYear() + 0);

var dob_mindate = new Date();
dob_mindate.setFullYear(dob_mindate.getFullYear() - 60);

var doj_maxdate = new Date();
doj_maxdate.setFullYear(dob_maxdate.getFullYear() + 1);

var doj_mindate = new Date();
doj_mindate.setFullYear(dob_mindate.getFullYear() - 40);

$(document).ready(function() {
    $('.datepick').datetimepicker({
        viewMode: 'years',
        format: 'DD-MM-YYYY',
        maxDate: dob_maxdate,
        minDate: dob_mindate
    }).on('dp.change', function(e) {
        $(this).parsley().validate();
        var d = new Date(e.date);
        var age = getAge(e.date);
        $('#age_cal').html(age);
        getAge1(d);
    });
});

// Initialize datetimepickers when document is ready
$(document).ready(function() {
    // Check if datetimepicker function exists
    if (typeof $.fn.datetimepicker !== 'undefined') {
        $('#passport_expiry_date').datetimepicker({
            viewMode: 'years',
            format: 'DD-MM-YYYY',
        });

        $('.f_datepick').datetimepicker({
            viewMode: 'years',
            format: 'DD-MM-YYYY',
            maxDate: dob_maxdate,
            minDate: dob_mindate
        }).on('dp.change', function(e) {
            var d = new Date(e.date);
            var age = getAge(e.date);
            $('#age_cal_f').html(age);
        });

        $('.m_datepick').datetimepicker({
            viewMode: 'years',
            format: 'DD-MM-YYYY',
            maxDate: dob_maxdate,
            minDate: dob_mindate
        }).on('dp.change', function(e) {
            var d = new Date(e.date);
            var age = getAge(e.date);
            $('#age_cal_m').html(age);
        });
    } else {
        console.error('Bootstrap datetimepicker plugin is not loaded');

        // Fallback to regular datepicker if datetimepicker is not available
        if (typeof $.fn.datepicker !== 'undefined') {
            console.log('Using fallback datepicker');
            $('#passport_expiry_date, .f_datepick, .m_datepick').datepicker({
                format: 'dd-mm-yyyy',
                autoclose: true,
                todayHighlight: true,
                orientation: "top"
            });
        }
    }
});

// Additional safety: Try to initialize datetimepicker after a short delay
setTimeout(function() {
    if (typeof $.fn.datetimepicker !== 'undefined') {
        // Re-initialize any elements that might not have been initialized
        $('#passport_expiry_date:not(.hasDatepicker)').datetimepicker({
            viewMode: 'years',
            format: 'DD-MM-YYYY',
        });

        $('.f_datepick:not(.hasDatepicker), .m_datepick:not(.hasDatepicker)').datetimepicker({
            viewMode: 'years',
            format: 'DD-MM-YYYY',
        });
    }
}, 1000);

// Loading overlay functions for save operations
function showSaveLoadingOverlay() {
    // Remove existing overlay if any
    hideSaveLoadingOverlay();

    // Create loading overlay
    var loadingHtml = `
        <div id="save-loading-overlay" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            font-family: Arial, sans-serif;
        ">
            <div style="
                background: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                min-width: 200px;
            ">
                <div class="loaderclass" style="
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #007bff;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                "></div>
                <div style="
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                ">Saving...</div>
                <div style="
                    color: #666;
                    font-size: 14px;
                    margin-top: 5px;
                ">Please wait</div>
            </div>
        </div>
    `;

    $('body').append(loadingHtml);
}

function hideSaveLoadingOverlay() {
    $('#save-loading-overlay').remove();
}

function getAge(dateVal) {
    var dob_as_on = '<?php echo $this->settings->getSetting('application_form_dob_criteria') ?>';
    if (dob_as_on) {
        var onSelectDate = new Date(dateVal),
            today = new Date(dob_as_on);
        // Check if the selected date is in the future compared to dob_as_on
        if (onSelectDate > today) {
            return '';
        }
        var ageInMilliseconds = new Date(today - onSelectDate),
            years = ageInMilliseconds / (24 * 60 * 60 * 1000 * 365.25),
            months = 12 * (years % 1),
            days = Math.floor(30 * (months % 1));
        // Ensure no negative values
        var calculatedYears = Math.floor(years);
        var calculatedMonths = Math.floor(months);
        var calculatedDays = days;

        if (calculatedYears < 0 || calculatedMonths < 0 || calculatedDays < 0) {
            return '';
        }
        return calculatedYears + ' years ' + calculatedMonths + ' months ' + calculatedDays + ' days as on ' +
            moment(dob_as_on).format('MMM DD, YYYY');
    } else {
        return '';
    }
}

function getAge1(dob) {
    var selectedClass = $("#class").val();
    var dob = $("input[name='student_dob']").val();
    if(selectedClass == '') {
        return;
    }
    $.ajax({
        url: '<?php echo site_url('admission_controller/get_class_dob/'); ?>',
        type: 'post',
        data: {
            'selectedClass': selectedClass,
            'dob': dob
        },
        success: function(data) {
            console.log(data);
            // return false;
            var resData = data.trim();
            if (resData != 0) {
                $('#age_cal_dob_error').html('');
            } else {
                $('#age_cal_dob_error').html(
                    'The kid does not meet the age-criteria for the applied Grade.');
                return false;
            }
        }
    });
}

function readURL(input, photo_type) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            if (photo_type == 'student') {
                $('#previewing').attr('src', e.target.result);
            } else if (photo_type == 'family') {
                $('#family_previewing').attr('src', e.target.result);
            } else if (photo_type == 'stud_sign') {
                $('#stud_sig_previewing').attr('src', e.target.result);
            }
        }

        reader.readAsDataURL(input.files[0]);
    }
}



$('#documentId').change(function() {
    var src = $(this).val();
    if (src && validate_document(this.files[0], 'documentId')) {
        $("#error1").html("");
        readURL(this);
    } else {
        $("#error1").html(
            "Allowed file size exceeded. (Max. 2 Mb) / Allowed file types are jpeg, jpg and pdf"
        );
        this.value = null;
    }
});

var other = '<?php echo $final_preview->nationality ?>';
if (other == 'Other') {
    $('#nationality_other').show();
} else {
    $('#nationality_other').hide();

}

var other =
    '<?php if (!empty($admission_prev_schools->board_other))  echo $admission_prev_schools->board_other ?>';
if (other == 'Other') {
    $('#board_other').show();
} else {
    $('#board_other').hide();

}

var other = '<?php echo $final_preview->religion ?>';
if (other == 'Other') {
    $('#religion_other').show();
} else {
    $('#religion_other').hide();
}

var other = '<?php echo $final_preview->std_mother_tongue ?>';
if (other == 'Other') {
    $('#mother_tongue_name').show();
} else {
    $('#mother_tongue_name').hide();
}


var other = '<?php echo $final_preview->father_mother_tongue ?>';
if (other == 'Other') {
    $('#f_mother_tongue_name').show();
} else {
    $('#f_mother_tongue_name').hide();
}

var other = '<?php echo $final_preview->mother_mother_tongue ?>';
if (other == 'Other') {
    $('#m_mother_tongue_name').show();
} else {
    $('#m_mother_tongue_name').hide();
}

function validate_photo_size(files, id, error_id) {
    var max_size_string = '<?php echo $image_size_in_admissions ?>';
    var file_size = parseFloat(files.size / 1024 / 1024);
    var max_file_size = parseInt(max_size_string);
    if (file_size > max_file_size) {
        $("#" + error_id).html('File size exceeded.');
        $("#" + id).val('');
        return false;
    } else {
        $("#" + error_id).html('');
        return true;
    }
}

function saveFileToStorage(file, inputId, pathInputId) {
    $('.save-step1').prop('disabled', true);
    $('.save-draft1').prop('disabled', true);
    console.log('Starting upload for:', inputId, 'file:', file.name);

    // Show progress and disable input
    $('#' + inputId).attr('disabled', 'disabled');

    // Create progress overlay with percentage display
    var previewSelector = '';
    if (inputId === 'fileupload') {
        previewSelector = '#previewing';
    } else if (inputId === 'stud_sign_fileupload') {
        previewSelector = '#stud_sig_previewing';
    } else if (inputId === 'family_fileupload') {
        previewSelector = '#family_previewing';
    }

    if (previewSelector) {
        var $preview = $(previewSelector);
        var $parent = $preview.parent();

        // Remove any existing overlay
        $parent.find('.upload-progress-overlay').remove();

        // Add new progress overlay
        $parent.append(`
            <div class="upload-progress-overlay" style="background: rgba(129, 125, 125, 0.3);position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 1000; border-radius: 8px;">
                <div style="color: white; font-size: 24px; font-weight: bold; margin-bottom: 10px;">
                    <span id="upload-percentage-${inputId}">0%</span>
                </div>
                <div style="color: white; font-size: 14px;">Uploading...</div>
                <div style="width: 80%; background: rgba(255,255,255,0.3); border-radius: 10px; margin-top: 10px; height: 6px;">
                    <div id="upload-progress-bar-${inputId}" style="width: 0%; background: #6c63ff; height: 100%; border-radius: 10px; transition: width 0.3s ease;"></div>
                </div>
            </div>
        `);
    }

    $.ajax({
        url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'profile'
        },
        success: function(response) {
            console.log('Got signed URL response:', response);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;

            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = Math.round((e.loaded / e.total) * 100);
                            console.log('Upload progress:', percentComplete + '%');

                            // Update progress display
                            $('#upload-percentage-' + inputId).text(percentComplete + '%');
                            $('#upload-progress-bar-' + inputId).css('width',
                                percentComplete + '%');
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    console.log('Upload successful, path:', path);

                    // Store the S3 path in the hidden input
                    $('#' + pathInputId).val(path);

                    // Re-enable the input
                    $('#' + inputId).removeAttr('disabled');
                    $('.save-step1').prop('disabled', false);
                    $('.save-draft1').prop('disabled', false);

                    // Remove progress overlay
                    $('.upload-progress-overlay').remove();

                    // Reset opacity
                    if (previewSelector) {
                        $(previewSelector).css('opacity', '1');
                    }

                    console.log('File uploaded successfully: ' + path);
                },
                error: function(err) {
                    console.log('Upload error:', err);
                    $('#' + inputId).removeAttr('disabled');
                    $('.upload-progress-overlay').remove();
                    if (previewSelector) {
                        $(previewSelector).css('opacity', '1');
                    }
                    alert('Upload failed. Please try again.');
                }
            });
        },
        error: function(err) {
            console.log('Signed URL error:', err);
            $('#' + inputId).removeAttr('disabled');
            $('.upload-progress-overlay').remove();
            if (previewSelector) {
                $(previewSelector).css('opacity', '1');
            }
            alert('Failed to get upload URL. Please try again.');
        }
    });
}

function single_file_progress(percentage) {
    // Update progress if there's a progress element
    if (percentage == 100) {
        completed_promises++;
        if (completed_promises >= total_promises) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }

    // Update any visible progress bars
    $('.progress-bar').css('width', current_percentage + '%');
    $('.progress-percent').text(current_percentage + '%');

    return false;
}

function addProgressOverlay(selector) {
    // Add a progress overlay to the image preview
    var $element = $(selector);
    if ($element.length && !$element.next('.progress-overlay').length) {
        $element.after(
            '<div class="progress-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.8); display: flex; align-items: center; justify-content: center; z-index: 10;"><div class="spinner-border text-primary" role="status"><span class="sr-only">Uploading...</span></div></div>'
        );
    }
}

function removeProgressOverlay(selector) {
    // Remove the progress overlay
    $(selector).next('.progress-overlay').remove();
}

function single_file_progress(percentage) {
    // Update progress if there's a progress element
    if (percentage == 100) {
        completed_promises++;
        if (completed_promises >= total_promises) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }

    // Update any visible progress bars
    $('.progress-bar').css('width', current_percentage + '%');
    $('.progress-percent').text(current_percentage + '%');

    return false;
}

function addProgressOverlay(selector) {
    // Add a progress overlay to the image preview
    var $element = $(selector);
    if ($element.length && !$element.next('.progress-overlay').length) {
        $element.after(
            '<div class="progress-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.8); display: flex; align-items: center; justify-content: center; z-index: 10;"><div class="spinner-border text-primary" role="status"><span class="sr-only">Uploading...</span></div></div>'
        );
    }
}

function removeProgressOverlay(selector) {
    // Remove the progress overlay
    $(selector).next('.progress-overlay').remove();
}


function validate_document(file, error) {
    if (file.size > 2097152 || file.fileSize > 2097152) {
        $("#" + error + "Error").html("Allowed file size exceeded. (Max. 2 Mb)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'application/pdf') {
        $("#" + error + "Error").html("Allowed file types are jpeg, jpg and pdf");
        return false;
    }
    return true;
}

function validate_documents_config_based(files) {
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>';
    var file_size = parseFloat(files.size / 1024 / 1024);
    var max_file_size = parseInt(max_size_string);
    if (file_size > max_file_size) {
        return false;
    } else {
        return true;
    }
}


function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}


$(document).ready(function() {
    $('.next').on('click', function() {
        var current = $(this).data('currentBlock'),
            next = $(this).data('nextBlock');
        // only validate going forward. If current group is invalid, do not go further
        // .parsley().validate() returns validation result AND show errors
        if (next > current)
            if (false === $('#std-form').parsley().validate('block' + current))
                return;

        // validation was ok. We can go on next step.
        $('.block' + current)
            .removeClass('show')
            .addClass('hidden');

        $('.block' + next)
            .removeClass('hidden')
            .addClass('show');
    });

    // $('.nav-tabs > li a[title]').tooltip();

    //Wizard
    $('a[data-toggle="tab"]').on('show.bs.tab', function(e) {

        var $target = $(e.target);

        if ($target.parent().hasClass('disabled')) {
            return false;
        }
    });


    // Mobile-compatible previous step handler
    $(document).on('click', '.prev-step', function(e) {

        if (window.matchMedia('(max-width: 768px)').matches) {
            // Mobile navigation - determine current step and go to previous available step
            var currentStepForm = $('.step-form:not(.d-none)').attr('id');

            if (!currentStepForm) {
                console.error('No current step form found');
                return;
            }

            var currentStepNumber = parseInt(currentStepForm.replace('step-form-', ''));

            if (isNaN(currentStepNumber)) {
                console.error('Invalid current step number:', currentStepForm);
                return;
            }

            // Use the new function to find the previous available step
            var prevStep = findPreviousAvailableStep(currentStepNumber);

            console.log('Mobile prev: current step', currentStepNumber, 'going to previous available step', prevStep);

            // Only navigate if we found a different step
            if (prevStep !== currentStepNumber && prevStep >= 1) {
                mobileGoToStep(prevStep);
            } else {
                console.log('Already at the first available step or no previous step found');
            }
        } else {
            // Desktop navigation
            var $visibleSteps = $('.step:visible');
            var $current = $visibleSteps.filter('.active');
            var idx = $visibleSteps.index($current);
            if (idx > 0) {
                var prevStep = $visibleSteps.eq(idx - 1).data('step');
                goToStep(prevStep);
            }
        }
    });

    // Special handler for medical form previous button (handles boarding logic) - Mobile compatible
    $(document).on('click', '.medical_prev_button', function(e) {
        for (var i = 1; i <= 6; i++) {
            var exists = $('#step-form-' + i).length > 0;
            console.log('  step-form-' + i + ':', exists);
        }

        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var show_gaurdian_form_based_on_boarder =
            '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';
        var medical_form =
            '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var af_id = '<?php echo $final_preview->id ?>';

        if (gaurdian_form == 1 && show_gaurdian_form_based_on_boarder == 1) {
            $.ajax({
                url: '<?php echo site_url('admission_controller/check_student_boarding'); ?>',
                type: 'post',
                data: {
                    'af_id': af_id
                },
                success: function(data) {
                    // Mobile-compatible navigation
                    if (window.matchMedia('(max-width: 768px)').matches) {
                        // On mobile, find the actual previous available step
                        var currentStep = 4; // Medical form is step 5
                        var prevStep = findPreviousAvailableStep(currentStep);
                        if (prevStep !== currentStep && prevStep >= 1) {
                            mobileGoToStep(prevStep);
                        }
                    } else {
                        // Desktop navigation - go to previous step
                        var $visibleSteps = $('.step:visible');
                        var $current = $visibleSteps.filter('.active');
                        var idx = $visibleSteps.index($current);
                        if (idx > 0) {
                            var prevStep = $visibleSteps.eq(idx - 1).data('step');
                            goToStep(prevStep);
                        }
                    }
                }
            });
        } else if ( gaurdian_form == 1) {
           if (window.matchMedia('(max-width: 768px)').matches) {
                        // On mobile, find the actual previous available step
                        var currentStep = 4; // Medical form is step 5
                        var prevStep = findPreviousAvailableStep(currentStep);
                        console.log('Medical prev (boarding): going to previous available step', prevStep);

                        if (prevStep !== currentStep && prevStep >= 1) {
                            mobileGoToStep(prevStep);
                        }
                    } else {
                        // Desktop navigation - go to previous step
                        var $visibleSteps = $('.step:visible');
                        var $current = $visibleSteps.filter('.active');
                        var idx = $visibleSteps.index($current);
                        if (idx > 0) {
                            var prevStep = $visibleSteps.eq(idx - 1).data('step');
                            goToStep(prevStep);
                        }
                    }
        } else {
            // Normal previous step - Mobile compatible
            if (window.matchMedia('(max-width: 768px)').matches) {
                // On mobile, find the actual previous available step
                var currentStep = 4; // Medical form is step 5
                var prevStep = findPreviousAvailableStep(currentStep);
                if (prevStep !== currentStep && prevStep >= 1) {
                    mobileGoToStep(prevStep);

                    setTimeout(function() {
                        var visibleForm = $('.step-form:not(.d-none)').attr('id');
                        console.log('Medical prev: After navigation, visible form is:', visibleForm);
                    }, 100);
                } else {
                    console.log('No previous step available from medical form - prevStep:', prevStep, 'currentStep:', currentStep);
                }
            } else {
                goToStep(2);
            }
        }
    });

    // Enhanced save-step1 handler with mobile support
    $(document).on('click', '.save-step1', function(e) {
        // Store original button text and show loading state
        var $btn = $(this);
        var originalText = $btn.text();

        // Set loading state for all save-step1 buttons
        $('.save-step1').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });

        // Create and show loading overlay
        showSaveLoadingOverlay();

        if (window.matchMedia('(max-width: 768px)').matches) {
            console.log('Mobile save step 1 button clicked!');
        }
        var inputDob = $("input[name='student_dob']").val();
        var inputClass = $("#class").val();
        var $form = $('#std-form');

        var validationResult = $form.parsley().validate();

        // Mobile-specific validation debugging
        if (window.matchMedia('(max-width: 768px)').matches) {
            if (!validationResult) {
                var errors = $form.parsley().validationResult;

                // Show validation errors more prominently on mobile
                $('.parsley-errors-list').each(function() {
                    console.log('Validation error found:', $(this).text());
                });
            } else {
                console.log('Form validation passed on mobile');
            }
        }

        if (validationResult) {
            var form = $('#std-form')[0];
            var formData = new FormData(form);
            check_dob_grade_wise(inputDob, inputClass, formData);
        } else {
            // Restore button state if validation fails
            restoreSaveButtonState();
        }
    });

    // Helper function to restore save button state
    function restoreSaveButtonState() {
        $('.save-step1').each(function() {
            var originalText = $(this).data('original-text') || 'Save & Continue';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    // Helper function to restore save-step2 button state
    function restoreSaveStep2ButtonState() {
        $('.save-step2').each(function() {
            var originalText = $(this).data('original-text') || 'Save & Continue';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    // Helper function to restore save-step4 button state
    function restoreSaveStep4ButtonState() {
        $('.save-step4').each(function() {
            var originalText = $(this).data('original-text') || 'Save & Continue';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    // Helper function to restore save-step5 button state
    function restoreSaveStep5ButtonState() {
        $('.save-step5').each(function() {
            var originalText = $(this).data('original-text') || 'Save & Continue';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    // Helper function to restore save-draft2 button state
    function restoreSaveDraft2ButtonState() {
        $('.save-draft2').each(function() {
            var originalText = $(this).data('original-text') || 'Save as Draft';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    // Helper function to restore save-draft4 button state
    function restoreSaveDraft4ButtonState() {
        $('.save-draft4').each(function() {
            var originalText = $(this).data('original-text') || 'Save as Draft';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    // Helper function to restore save-draft5 button state
    function restoreSaveDraft5ButtonState() {
        $('.save-draft5').each(function() {
            var originalText = $(this).data('original-text') || 'Save as Draft';
            $(this).text(originalText).prop('disabled', false);
        });

        // Hide loading overlay
        hideSaveLoadingOverlay();
    }

    $('#class').on('change', function() {
        var selectedClass = $("#class").val();
        var dob = $("input[name='student_dob']").val();
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_class_dob/'); ?>',
            type: 'post',
            data: {
                'selectedClass': selectedClass,
                'dob': dob
            },
            success: function(data) {
                console.log(data);
                // return false;
                var resData = data.trim();
                if (resData != 0) {
                    $('#age_cal_dob_error').html('');
                } else {
                    $('#age_cal_dob_error').html(
                        'The kid does not meet the age-criteria for the applied Grade.');
                    return false;
                }
            }
        });
    });

    function check_dob_grade_wise(dob, selectedClass, formData) {
        $.ajax({
            url: '<?php echo site_url('admission_controller/get_class_dob/'); ?>',
            type: 'post',
            data: {
                'selectedClass': selectedClass,
                'dob': dob,
            },
            success: function(data) {
                // console.log(data);
                // return false;
                var resData = data.trim();
                if (resData != 0) {
                    $('#dob_error').html('');
                    update_student_after_dob_check(formData, selectedClass);
                } else {
                    $('#dob_error').html('The kid does not meet the age-criteria for the applied Grade.');
                    // Restore button state on error
                    // restoreSaveButtonState();
                    $('.save-step1').text('Save & Continue').prop('disabled', false);
                    hideSaveLoadingOverlay();
                    return false;
                }
            }
            
        });
    }

    function update_student_after_dob_check(formData, selectedClass) {
        // Button state is already set in the main click handler, no need to set again
        $.ajax({
            url: '<?php echo site_url('admission_controller/updateStudentDetails'); ?>',
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            // cache : false,
            success: function(data) {
                console.log(data);

                if (data != '') {
                    // Success - restore button state and proceed
                    restoreSaveButtonState();

                    // var $active = $('.wizard1 .nav-tabs li.active');
                    // $active.next().removeClass('disabled');
                    // nextTab($active);
                    goToStep(2);
                    get_language_selection(selectedClass);
                    get_subject_details(selectedClass);
                    fill_single_parent();
                    return 1;
                } else {
                    // Error - restore button state and show alert
                    restoreSaveButtonState();
                    alert("Something went wrong in Student data, try again.");
                    return 0;
                }
            },
            error: function() {
                // AJAX error - restore button state
                restoreSaveButtonState();
                alert("Network error occurred. Please try again.");
            }
        });
    }

    $(".save-draft1").click(function(e) {
        console.log(e.target);
        var auId = $('#auId').val();
        var $form = $('#std-form');
        if ($form.parsley().validate()) {
            var form = $('#std-form')[0];
            var formData = new FormData(form);
            $('.save-draft1').text('Please wait ...').attr('disabled', 'disabled');
            $.ajax({
                url: '<?php echo site_url('admission_controller/updateStudentDetails'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    $('.save-draft1').text('Save as draft').removeAttr('disabled');
                    if (data != '') {
                        url = '<?php echo site_url('admissions/home') ?>';
                        $('#std-form').attr('action', url);
                        $('#std-form').submit();
                        return 1;
                    } else {
                        alert("Something went wrong in Student data, try again.");
                        return 0;
                    }
                }
            });
        }
    });

    function check_student_boarding_show_guardian_details() {
        var af_id = '<?php echo $final_preview->id ?>';
        $.ajax({
            url: '<?php echo site_url('admission_controller/check_student_boarding'); ?>',
            type: 'post',
            data: {
                'af_id': af_id
            },
            success: function(data) {
                if (data == 0) {
                    // Student doesn't need boarding - hide guardian details and skip guardian step
                    $('#guardian_details').hide();
                    $('.step[data-step="3"]').hide(); // Hide guardian step from navigation

                    // Skip to the next available step after guardian (usually medical or documents)
                    var currentStep = 3; // Assuming we're coming from parent details (step 3)
                    var nextAvailableStep = findNextAvailableStep(currentStep + 1); // Skip guardian step (4)

                    if (nextAvailableStep) {
                        if (window.matchMedia('(max-width: 768px)').matches) {
                            mobileGoToStep(nextAvailableStep);
                        } else {
                            goToStep(nextAvailableStep);
                        }
                    }

                    get_parent_data();
                    return 1;
                } else {
                    // Student needs boarding - show guardian details and navigate to guardian step
                    $('#guardian_details').show();
                    $('.step[data-step="3"]').show(); // Show guardian step in navigation

                    // Navigate to guardian step
                    if (window.matchMedia('(max-width: 768px)').matches) {
                        mobileGoToStep(3);
                    } else {
                        goToStep(3);
                    }

                    get_parent_data();
                    return 1;
                }
            }
        });
    }
    // Enhanced save-step2 handler with mobile support
    $(document).on('click', '.save-step2', function(e) {
        console.log('Save Step 2 clicked - Mobile check:', window.matchMedia('(max-width: 768px)')
            .matches);

        // Store original button text and show loading state
        $('.save-step2').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });

        // Show loading overlay
        showSaveLoadingOverlay();

        //var formData =$("#health-form").serialize();
        var af_id = '<?php echo $final_preview->id ?>';
        var f_mobile_no = $("#f_mobile_no").val(),
            m_mobile_no = $("#m_mobile_no").val(),
            father_email_id = $("#f_email").val(),
            mother_email_id = $("#m_email").val();
        if (f_mobile_no && m_mobile_no && f_mobile_no === m_mobile_no) {
            // Restore button state on validation error
            restoreSaveStep2ButtonState();
            // Show inline error messages
            showMobileDuplicateError();
            return false;
        } else {
            // Hide mobile duplicate error if validation passes
            hideMobileDuplicateError();
        }
        if (father_email_id && mother_email_id && father_email_id === mother_email_id) {
            restoreSaveStep2ButtonState();
            // Show inline error messages
            showEmailDuplicateError();
            return false;
        } else {
            // Hide email duplicate error if validation passes
            hideEmailDuplicateError();
        }
        var document_form =
            '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        console.log(document_form);
        var medical_form =
            '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var show_gaurdian_form_based_on_boarder =
            '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';
        var $form = $('#parent-form');
        if ($form.parsley().validate()) {
            var form = $('#parent-form')[0];
            var formData = new FormData(form);
            // Button state already set above, no need to set again
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_parent_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache: false,
                success: function(data) {
                    // console.log(data);

                    if (data == 1) {
                        // Success - restore button state
                        restoreSaveStep2ButtonState();
                        const isGuardianEnabled = gaurdian_form === '1';
                        const isGuardianBasedOnBoarder =
                            show_gaurdian_form_based_on_boarder === '1';
                        const isMedicalEnabled = medical_form === '1';
                        const isDocumentEnabled = document_form === '' || document_form !=
                            1; // 0 means enabled
                        // Mobile-compatible navigation logic

                        if (window.matchMedia('(max-width: 768px)').matches) {
                            // Mobile navigation
                            if (isGuardianEnabled && isGuardianBasedOnBoarder) {
                                console.log('Save Step 2: Checking boarding for guardian details');
                                check_student_boarding_show_guardian_details();
                                return;
                            } else if (isGuardianEnabled) {
                                console.log('Save Step 2: Going to guardian step (3)');
                                mobileGoToStep(3);
                            } else if (isMedicalEnabled) {
                                console.log('Save Step 2: Going to medical step (4)');
                                mobileGoToStep(4);
                            } else if (isDocumentEnabled) {
                                console.log('Save Step 2: Going to document step (5)');
                                mobileGoToStep(5);
                            } else {
                                console.log('Save Step 2: Going to preview');
                                goto_preview_page('parent-form');
                            }
                        } else {
                            // Desktop navigation
                            var $visibleSteps = $('.step:visible');
                            var $current = $visibleSteps.filter('.active');
                            var idx = $visibleSteps.index($current);

                            if (isGuardianEnabled && isGuardianBasedOnBoarder) {
                                console.log('isGuardianEnabled')
                                check_student_boarding_show_guardian_details();
                                return;
                            } else if (isGuardianEnabled) {
                                console.log('Save Step 2: Going to guardian step (3)');
                                goToStep(3);
                            } else if (isMedicalEnabled) {
                                console.log('Save Step 2: Going to medical step (4)');
                                goToStep(4);
                            } else if (isDocumentEnabled) {
                                console.log('Save Step 2: Going to document step (5)');
                                goToStep(5);
                            } else {
                                console.log('Save Step 2: Going to preview');
                                goto_preview_page('parent-form');
                                var nextStep = $visibleSteps.eq(idx + 1).data('step');
                                goToStep(nextStep);
                            }
                        }
                        get_parent_data();
                        return 1;
                    } else {
                        // Error - restore button state
                        restoreSaveStep2ButtonState();
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                },
                error: function() {
                    // AJAX error - restore button state
                    restoreSaveStep2ButtonState();
                    alert("Network error occurred. Please try again.");
                }
            });
        } else {
            // Form validation failed - restore button state
            restoreSaveStep2ButtonState();
        }
    });

    $('#boarding').on('change', function() {
        check_student_boarding();
    });

    $(document).ready(function() {
        check_student_boarding();
    });

    function check_student_boarding() {
        var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
        var show_gaurdian_form_based_on_boarder =
            '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';
        if (gaurdian_form == 1 && show_gaurdian_form_based_on_boarder == 1) {
            var boarding = $('#boarding option:selected').text();
            if (boarding == 'Day Scholar') {
                $('#guardian_details').hide();
                // $('#step3').hide();
            } else {
                $('#guardian_details').show();
                // $('#step3').show();
            }
        }
    }


    

    // Helper function to find next available step
    function findNextAvailableStep(startStep) {
        for (var step = startStep; step <= 6; step++) {
            var $stepElement = $('.step[data-step="' + step + '"]:visible');
            var $formElement = $('#step-form-' + step);

            if ($stepElement.length > 0 && $formElement.length > 0) {
                return step;
            }
        }
        return null;
    }

    // Enhanced save-step5 handler with mobile support
    $(document).on('click', '.save-step5', function(e) {
        console.log('Save Step 5 clicked - Mobile check:', window.matchMedia('(max-width: 768px)')
            .matches);

        // Store original button text and show loading state
        $('.save-step5').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });

        // Show loading overlay
        showSaveLoadingOverlay();

        // $(".remove_input").remove();
        // $("#vac_date_modal").val('01-01-0001');
        var document_form_disabled =
            '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var $form = $('#medical-form');

        console.log('Medical form validation result:', $form.parsley().validate());

        if ($form.parsley().validate()) {
            var form = $('#medical-form')[0];
            var formData = new FormData(form);
            // Button state already set above, no need to set again
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_medical_form_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache: false,
                success: function(data) {
                    // console.log(data);

                    if (data == 1) {
                        // Success - restore button state
                        restoreSaveStep5ButtonState();
                        if (document_form_disabled == 1) {
                            goto_preview_page('medical-form');
                        } else {
                            // var $active = $('.wizard1 .nav-tabs li.active');
                            // $active.next().removeClass('disabled');
                            // nextTab($active);
                            // return 1;
                            var $visibleSteps = $('.step:visible');
                            var $current = $visibleSteps.filter('.active');
                            var idx = $visibleSteps.index($current);
                            if (idx < $visibleSteps.length - 1) {
                                var nextStep = $visibleSteps.eq(idx + 1).data('step');
                                goToStep(nextStep);
                            }
                            return 1;
                        }
                    } else {
                        // Error - restore button state
                        restoreSaveStep5ButtonState();
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                },
                error: function() {
                    // AJAX error - restore button state
                    restoreSaveStep5ButtonState();
                    alert("Network error occurred. Please try again.");
                }
            });
        } else {
            // Form validation failed - restore button state
            restoreSaveStep5ButtonState();
            $("#vaccination_table_in_admission ul").each(function() {
                var html = $(this).children('li').html();
                $(this).hide();
                var num = $(this).prev('td').data("num");
                $(this).prev('td').children(`.span_class_${num}`).remove();
                $(this).prev('td').append(`<span class="span_class_${num}">${html}</span>`);
            });
        }
    });




    // Enhanced save-step4 handler with mobile support
    $(document).on('click', '.save-step4', function(e) {
        // Store original button text and show loading state
        $('.save-step4').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });

        // Show loading overlay
        showSaveLoadingOverlay();

        $("#vaccination_table_7 .cls_3").remove();
        //var formData =$("#health-form").serialize();
        var document_form =
            '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
        var medical_form =
            '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
        var $form = $('#guardian-form');

        if ($form.parsley().validate()) {
            var form = $('#guardian-form')[0];
            var formData = new FormData(form);
            // Button state already set above, no need to set again
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_guardian_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache: false,
                success: function(data) {
                    // console.log(data);

                    if (data == 1) {
                        // Success - restore button state
                        restoreSaveStep4ButtonState();
                        if (document_form == 1 && medical_form == 0) {
                            goto_preview_page('guardian-form');
                        } else {
                            // var $active = $('.wizard1 .nav-tabs li.active');
                            // $active.next().removeClass('disabled');
                            // nextTab($active);
                            var $visibleSteps = $('.step:visible');
                            var $current = $visibleSteps.filter('.active');
                            var idx = $visibleSteps.index($current);
                            if (idx < $visibleSteps.length - 1) {
                                var nextStep = $visibleSteps.eq(idx + 1).data('step');
                                goToStep(nextStep);
                            }
                            return 1;
                        }
                    } else {
                        // Error - restore button state
                        restoreSaveStep4ButtonState();
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                },
                error: function() {
                    // AJAX error - restore button state
                    restoreSaveStep4ButtonState();
                    alert("Network error occurred. Please try again.");
                }
            });
        } else {
            // Form validation failed - restore button state
            restoreSaveStep4ButtonState();
        }
    });

    $(".save-draft2").click(function(e) {
        // Store original button text and show loading state
        $('.save-draft2').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });

        // Show loading overlay
        showSaveLoadingOverlay();

        //var formData =$("#health-form").serialize();
        var $form = $('#parent-form');
        if ($form.parsley().validate()) {
            var form = $('#parent-form')[0];
            var formData = new FormData(form);
            // Button state already set above, no need to set again
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_parent_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache: false,
                success: function(data) {
                    // console.log(data);

                    if (data == 1) {
                        // Success - restore button state
                        restoreSaveDraft2ButtonState();
                        url = '<?php echo site_url('admissions/home') ?>';
                        $('#parent-form').attr('action', url);
                        $('#parent-form').submit();
                        // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                        return 1;
                    } else {
                        // Error - restore button state
                        restoreSaveDraft2ButtonState();
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                },
                error: function() {
                    // AJAX error - restore button state
                    restoreSaveDraft2ButtonState();
                    alert("Network error occurred. Please try again.");
                }
            });
        } else {
            // Form validation failed - restore button state
            restoreSaveDraft2ButtonState();
        }
    });

    $(".save-draft4").click(function(e) {
        // Store original button text and show loading state
        $('.save-draft4').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });

        // Show loading overlay
        showSaveLoadingOverlay();

        var $form = $('#guardian-form');
        if ($form.parsley().validate()) {
            var form = $('#guardian-form')[0];
            var formData = new FormData(form);
            // Button state already set above, no need to set again
            $.ajax({
                url: '<?php echo site_url('admission_controller/update_guardian_details'); ?>',
                type: 'post',
                data: formData,
                // async: false,
                processData: false,
                contentType: false,
                cache: false,
                success: function(data) {
                    // console.log(data);

                    if (data == 1) {
                        // Success - restore button state
                        restoreSaveDraft4ButtonState();
                        url = '<?php echo site_url('admissions/home') ?>';
                        $('#guardian-form').attr('action', url);
                        $('#guardian-form').submit();
                        // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                        return 1;
                    } else {
                        // Error - restore button state
                        restoreSaveDraft4ButtonState();
                        alert("Something went wrong, try again.");
                        return 0;
                    }
                },
                error: function() {
                    // AJAX error - restore button state
                    restoreSaveDraft4ButtonState();
                    alert("Network error occurred. Please try again.");
                }
            });
        } else {
            // Form validation failed - restore button state
            restoreSaveDraft4ButtonState();
        }
    });

});

// draft medical
// Function to restore save-draft5 button state
function restoreSaveDraft5ButtonState() {
    $('.save-draft5').each(function() {
        var originalText = $(this).data('original-text');
        if (originalText) {
            $(this).text(originalText).prop('disabled', false);
        } else {
            // Fallback text if original text wasn't stored
            $(this).text('Save & Continue').prop('disabled', false);
        }
    });
    hideSaveLoadingOverlay();
}

$(".save-draft5").click(function(e) {
    // Store original button text and show loading state
    var $form = $('#medical-form');
    if ($form.parsley().validate()) {
        $('.save-draft5').each(function() {
            $(this).data('original-text', $(this).text());
            $(this).text('Please wait...').prop('disabled', true);
        });


        showSaveLoadingOverlay();
        var form = $('#medical-form')[0];
        var formData = new FormData(form);
        // Button state already set above, no need to set again
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_medical_form_details'); ?>',
            type: 'post',
            data: formData,
            // async: false,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                // console.log(data);

                if (data == 1) {
                    // Success - restore button state
                    restoreSaveDraft5ButtonState();
                    url = '<?php echo site_url('admissions/home') ?>';
                    $('#medical-form').attr('action', url);
                    $('#medical-form').submit();
                    // window.location.href ='<?php // echo site_url('admission_controller/saveAction/'.$au_id) ?>';
                    return 1;
                } else {
                    // Error - restore button state
                    restoreSaveDraft5ButtonState();
                    alert("Something went wrong, try again.");
                    return 0;
                }
            },
            error: function() {
                // AJAX error - restore button state
                restoreSaveDraft5ButtonState();
                alert("Network error occurred. Please try again.");
            }
        });
    }
});
// draft medcal end

function nextTab(elem) {
    $(elem).next().find('a[data-toggle="tab"]').click();
}

function prevTab(elem) {
    $(elem).prev().find('a[data-toggle="tab"]').click();
}



function check_sibling_ad_no() {
    var sb_ad = $('#sb_admission_number').val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/check_sibling_ad_nobyinput'); ?>',
        type: 'post',
        data: {
            'sb_ad': sb_ad
        },
        success: function(data) {
            console.log(data);
            if (data) {
                $('#exit_error').html(data);
            } else {
                $('#exit_error').html("");
            }
        }
    });
}


$('#document_add').on('click', function() {
    var file_doc = $('#documentId').val();
    if (file_doc == '') {
        bootbox.alert({
            title: 'Error',
            message: "Upload document and then click Add",
            size: 'small',
            className: 'class_document'
        });
        return false;
    }
    var $form = $('#document-form');
    var afid = $('#afid').val();
    var form = $('#document-form')[0];
    var formData = new FormData(form);
    $('#document_add').val('Please wait ...').attr('disabled', 'disabled');
    $("#loader1").show();
    $.ajax({
        url: '<?php echo site_url('admission_controller/update_documents/'); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        cache: false,
        success: function(data) {
            console.log(data);
            $("#loader1").hide();
            $('#document_add').val('Add').removeAttr('disabled');
            if (data != '') {
                $('#documentId').val('');
                $.post("<?php echo site_url('admission_controller/get_admission_form_document');?>", {
                    afid: afid
                }, function(data) {
                    // console.log(data);
                    var details = $.parseJSON(data);
                    if (details != '') {
                        $('#document_submit').removeAttr('disabled');
                        $('#draft_submit').removeAttr('disabled');
                        var m = 1;
                        var html = '';
                        html += '<table class="table">';
                        html += '<thead>';
                        html += '<tr>';
                        html += '<th>#</th>';
                        html += '<th>Name</th>';
                        html += '<th>Action</th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';
                        for (var i = 0; i < details.length; i++) {
                            html += '<tr>';
                            html += '<td>' + m + '</td>'
                            html += '<td>' + details[i].document_type + '</td>'
                            html +=
                                '<td><a href="javascript:void(0)" onclick="deletedocument_row(' +
                                details[i].id +
                                ')" class="remove btn btn-danger "><i class="fa fa-trash-o"></i></a></td>'
                            html += '</tr>';
                            m++;
                        }
                        html += '</tbody>';
                        html += '</table>';
                        $('#display_document').html(html);
                    }
                });
            } else {
                alert('Something went wrong');
            }
        }
    });
});

function deletedocument_row(d_id) {
    var afid = $('#afid').val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/delete_documentbyId'); ?>',
        type: 'post',
        data: {
            'd_id': d_id
        },
        success: function(data) {
            if (data != '') {
                $.post("<?php echo site_url('admission_controller/get_admission_form_document');?>", {
                    afid: afid
                }, function(data) {
                    var details = $.parseJSON(data);
                    var m = 1;
                    var html = '';
                    html += '<table class="table">';
                    html += '<thead>';
                    html += '<tr>';
                    html += '<th>#</th>';
                    html += '<th>Name</th>';
                    html += '<th>Action</th>';
                    html += '</tr>';
                    html += '</thead>';
                    html += '<tbody>';
                    for (var i = 0; i < details.length; i++) {
                        html += '<tr>';
                        html += '<td>' + m + '</td>'
                        html += '<td>' + details[i].document_type + '</td>'
                        html += '<td><a href="javascript:void(0)" onclick="deletedocument_row(' +
                            details[i].id +
                            ')" class="remove btn btn-danger "><i class="fa fa-trash-o"></i></a></td>'
                        html += '</tr>';
                        m++;
                    }
                    html += '</tbody>';
                    html += '</table>';
                    $('#display_document').html(html);
                });
            } else {
                alert("Something went wrong in Student data, try again.");
                return 0;
            }
        }
    });
}

$('#nationality').on('change', function() {
    var others = $('#nationality').val();
    if (others == 'Other') {
        $('#nationality_other').show();
    } else {
        $('#nationality_other').hide();
    }
});

$('#schooling_board').on('change', function() {
    var others = $('#schooling_board').val();
    if (others == 'Other') {
        $('#board_other').show();
    } else {
        $('#board_other').hide();
    }
});

$('#religion').on('change', function() {
    var others = $('#religion').val();
    if (others == 'Other') {
        $('#religion_other').show();
    } else {
        $('#religion_other').hide();
    }
});


$('#document_for').on('change', function() {
    var others = $('#document_for').val();
    if (others == 'Others') {
        $('#documentName').show();
    } else {
        $('#documentName').hide();
    }
});

$('#mother_tongue').on('change', function() {
    var others = $('#mother_tongue').val();
    if (others == 'Other') {
        $('#mother_tongue_name').show();
    } else {
        $('#mother_tongue_name').hide();
    }
});


$('#father_mother_tongue').on('change', function() {
    var others = $('#father_mother_tongue').val();
    if (others == 'Other') {
        $('#f_mother_tongue_name').show();
    } else {
        $('#f_mother_tongue_name').hide();
    }
});

$('#mother_mother_tongue').on('change', function() {
    var others = $('#mother_mother_tongue').val();
    if (others == 'Other') {
        $('#m_mother_tongue_name').show();
    } else {
        $('#m_mother_tongue_name').hide();
    }
});

function previous_click() {
    Swal.fire({
        title: 'Are you sure you want to exit?',
        html: `
                    <p style="font-size:15px; color:#6B7280; margin-top:8px;">
                        On exit, application will be saved as draft. You can continue later.
                    </p>`,
        showConfirmButton: false,
        showCancelButton: false,
        width: '400px',
        customClass: {
            popup: 'rounded-xl shadow-lg',
            title: 'text-lg font-semibold text-gray-800',
            htmlContainer: 'text-sm text-gray-500'
        },
        didRender: () => {
            const popup = Swal.getPopup();

            // 🔼 Add padding above the title
            const topPadding = document.createElement('div');
            topPadding.style.height = '20px';
            popup.insertBefore(topPadding, popup.firstChild);

            // ✅ Footer with buttons
            const container = Swal.getHtmlContainer();
            const footer = document.createElement('div');
            footer.className = 'custom-button-container mt-3 d-flex justify-content-center gap-3';
            footer.innerHTML = `
                        <button class="swal2-cancel btn-outline" id="cancelBtn">No</button>
                        <button class="swal2-confirm btn-primary" id="createBtn">Yes</button>
                    `;
            container.parentNode.appendChild(footer);

            // 🔽 Add padding below the buttons
            const bottomPadding = document.createElement('div');
            bottomPadding.style.height = '20px';
            footer.parentNode.appendChild(bottomPadding);

            // Button actions
            document.getElementById('cancelBtn').addEventListener('click', () =>
                Swal.close());

            document.getElementById('createBtn').addEventListener('click', () => {
                let url = '<?php echo site_url('admissions/home') ?>';
                $('#breadcrumb-form').attr('action', url);
                $('#breadcrumb-form').submit();
                return false;
            });
        }
    });
}


var show_previous_schooling_subjects = '<?php echo $show_previous_schooling_subjects ?>';
var show_previous_schooling_overall_total_marks = '<?php echo $show_previous_schooling_overall_total_marks ?>';
var show_previous_schooling_overall_percentage = '<?php echo $show_previous_schooling_overall_percentage ?>';
var show_previous_schooling_subject_total_marks = '<?php echo $show_previous_schooling_subject_total_marks ?>';
var show_previous_schooling_subject_percentage = '<?php echo $show_previous_schooling_subject_percentage ?>';
$('#prev_add').on('click', function() {
    var file_doc = $('#schooling_school').val();
    if (file_doc == '') {
        bootbox.alert({
            title: 'Error',
            message: "Enter school name and then click add button",
            size: 'small'
        });
        return false;
    }
    var $form = $('#document-form');
    $('#combinationId').removeAttr('required');
    var afid = $('#afid').val();
    if ($form.parsley().validate()) {
        var form = $('#document-form')[0];
        var formData = new FormData(form);
        $('#prev_add').val('Please wait ...').attr('disabled', 'disabled');
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_previous_school_details/'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                console.log(data);
                $('#prev_add').val('Add >>').removeAttr('disabled');
                if (data != '') {
                    $('#combinationId').attr('required', 'required');

                    // $('#schooling_year').val('');
                    $.post("<?php echo site_url('admission_controller/get_previous_school_details');?>", {
                        afid: afid
                    }, function(data) {
                        var details = $.parseJSON(data);
                        // console.log(details);
                        if (details != '') {
                            var html = '';
                            html += '<table class="table table-bordered">';
                            html += '<thead>';
                            html += '<tr>';
                            html += '<th>Year</th>';
                            html += '<th>Grades and marks obtained in Final Exam</th>';
                            html += '</tr>';
                            html += '</thead>';
                            html += '<tbody>';
                            for (var i = 0; i < details.length; i++) {
                                html += '<tr>';
                                html += '<td>' + details[i].year + '</td>'
                                html += '<td>';
                                html += '<lable>School Name : ' + details[i].school_name +
                                    '</label><br>';
                                html += '<lable>Class : ' + details[i].class +
                                    '</label><br>';
                                if (details[i].board == 'Other') {
                                    html += '<lable>Board : ' + details[i].board + ', ' +
                                        details[i].board_other + '</label><br>';
                                } else {
                                    html += '<lable>Board : ' + details[i].board +
                                        '</label><br>';
                                }
                                // html +='<lable>Class : '+details[i].class+'</label><br>';

                                if (show_previous_schooling_overall_total_marks == 1) {
                                    html += '<lable>Total Marks Scored : ' + details[i]
                                        .total_marks_scored + '</label><br>';
                                    html += '<lable>Total Marks : ' + details[i]
                                        .total_marks + '</label><br>';
                                }
                                if (show_previous_schooling_overall_percentage == 1) {
                                    html += '<lable>Total Percentage : ' + details[i]
                                        .total_percentage + '</label><br>';
                                }
                                // var json = $.parseJSON(details[i].marks);
                                // console.log(json);
                                if (show_previous_schooling_subjects) {
                                    console.log((details[i].marks));
                                    if (details[i].marks != undefined) {
                                        html += '<lable>Subject : <br> '
                                        for (var j = 0; j < details[i].marks.length; j++) {
                                            var json = $.parseJSON(details[i].marks[j]
                                                .sub_name);

                                            if (show_previous_schooling_subject_total_marks ==
                                                1) {
                                                html += '<strong>' + json.name +
                                                    '</strong> - ' + details[i].marks[j]
                                                    .marks_scored + '/' + details[i].marks[
                                                        j].marks + '<br>';
                                            }
                                            if (show_previous_schooling_subject_percentage ==
                                                1) {
                                                html += '<strong>' + json.name +
                                                    '</strong> - ' + details[i].marks[j]
                                                    .percentage + '%' + '( ' + details[i]
                                                    .marks[j].grade + ') <br>'
                                            }

                                            // var percentage = ' %';
                                            // if (show_previous_schooling_subject_total_marks) {
                                            //     percentage = ' ';
                                            // }
                                            // html += '<strong>'+json.name+'</strong> - ' +details[i].marks[j].percentage+ percentage+'( '+details[i].marks[j].grade+') '
                                        }
                                    }

                                }

                                html +=
                                    '<a href="javascript:void(0)" onclick="deletepreviou_school_row(' +
                                    details[i].apsid +
                                    ')" class="remove btn btn-danger pull-right "><i class="fa fa-trash-o"></i></a>'
                                html += '</lable>';
                                html += '</tr>';
                            }
                            html += '</tbody>';
                            html += '</table>';
                            // console.log(html);
                            $('#display_prev').html(html);
                            $('#schooling_class').val('');
                            $('.per').val('');
                            $('.grd').val('');
                            yearLength--;
                            $.ajax({
                                url: '<?php echo site_url('admission_controller/get_remaing_years_selecting/'); ?>',
                                type: 'post',
                                data: formData,
                                processData: false,
                                contentType: false,
                                cache: false,
                                success: function(data) {
                                    var years = $.parseJSON(data);
                                    if (years.length == 0) {

                                    }
                                    // console.log(years);
                                    // alert(years.length);
                                    var yearinfo =
                                        '<option value="">Select Year</option>';
                                    for (var i = 0; i < years.length; i++) {
                                        yearinfo += '<option value="' + years[
                                                i] + '">' + years[i] +
                                            '</option>';
                                    }
                                    // console.log(yearinfo);
                                    $('#schooling_year').html(yearinfo);
                                }
                            });

                            $('#schooling_year').val('');
                            $('.displayshow').hide();
                            $('#prev_add').attr('disabled', 'disabled');
                        }
                    });
                } else {
                    alert('Something went wrong');
                }
            }
        });
    }
});

function deletepreviou_school_row(apsid) {
    var $form = $('#document-form');
    var form = $('#document-form')[0];
    var formData = new FormData(form);
    var afid = $('#afid').val();
    $.ajax({
        url: '<?php echo site_url('admission_controller/delete_details_schooling_byId'); ?>',
        type: 'post',
        data: {
            'apsid': apsid
        },
        success: function(data) {
            // console.log(data);
            if (data != '') {
                $.post("<?php echo site_url('admission_controller/get_previous_school_details');?>", {
                    afid: afid
                }, function(data) {
                    var details = $.parseJSON(data);
                    // console.log(data);
                    if (details != '') {
                        var html = '';
                        html += '<table class="table table-bordered">';
                        html += '<thead>';
                        html += '<tr>';
                        html += '<th>Year</th>';
                        html += '<th>Grades and marks obtained in Final Exam</th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';
                        for (var i = 0; i < details.length; i++) {
                            html += '<tr>';
                            html += '<td>' + details[i].year + '</td>'
                            html += '<td>';
                            html += '<lable>School Name : ' + details[i].school_name +
                                '</label><br>';
                            html += '<lable>Class : ' + details[i].class + '</label><br>';
                            if (details[i].board == 'Other') {
                                html += '<lable>Board : ' + details[i].board + ', ' + details[i]
                                    .board_other + '</label><br>';
                            } else {
                                html += '<lable>Board : ' + details[i].board + '</label><br>';
                            }

                            if (show_previous_schooling_overall_total_marks == 1) {
                                html += '<lable>Total Marks Scored : ' + details[i]
                                    .total_marks_scored + '</label><br>';
                                html += '<lable>Total Marks : ' + details[i].total_marks +
                                    '</label><br>';
                            }
                            if (show_previous_schooling_overall_percentage == 1) {
                                html += '<lable>Total Percentage : ' + details[i].total_percentage +
                                    '</label><br>';
                            }
                            html += '<lable>Subject : '

                            if (show_previous_schooling_subjects) {
                                if (details[i].marks != undefined) {
                                    html += '<lable>Subject : '
                                    for (var j = 0; j < details[i].marks.length; j++) {
                                        var json = $.parseJSON(details[i].marks[j].sub_name);
                                        var percentage = ' %';
                                        if (show_previous_schooling_subject_total_marks) {
                                            percentage = ' ';
                                        }
                                        html += '<strong>' + json.name + '</strong> - ' + details[i]
                                            .marks[j].percentage + percentage + '( ' + details[i]
                                            .marks[j].grade + ') '
                                    }
                                }

                            }

                            html +=
                                '<a href="javascript:void(0)" onclick="deletepreviou_school_row(' +
                                details[i].apsid +
                                ')" class="remove btn btn-danger pull-right "><i class="fa fa-trash-o"></i></a>'
                            html += '</lable>';
                            html += '</tr>';

                        }
                        html += '</tbody>';
                        html += '</table>';
                        $('#display_prev').html(html);
                        yearLength++;

                        $.ajax({
                            url: '<?php echo site_url('admission_controller/get_remaing_years_selecting/'); ?>',
                            type: 'post',
                            data: formData,
                            processData: false,
                            contentType: false,
                            cache: false,
                            success: function(data) {
                                // console.log(data);
                                var years = $.parseJSON(data);
                                // console.log(years);
                                // alert(years.length);
                                var yearinfo = '<option value="">Select Year</option>';
                                for (var i = 0; i < years.length; i++) {
                                    yearinfo += '<option value="' + years[i] + '">' +
                                        years[i] + '</option>';
                                }
                                // console.log(yearinfo);
                                $('#schooling_year').html(yearinfo);
                            }
                        });

                        return true;
                    }
                    $.ajax({
                        url: '<?php echo site_url('admission_controller/get_remaing_years_selecting/'); ?>',
                        type: 'post',
                        data: formData,
                        processData: false,
                        contentType: false,
                        cache: false,
                        success: function(data) {
                            // console.log(data);
                            var years = $.parseJSON(data);
                            // console.log(years);
                            if (years.length == 0) {
                                documentSubmit(0);
                            }
                            var yearinfo = '<option value="">Select Year</option>';
                            for (var i = 0; i < years.length; i++) {
                                yearinfo += '<option value="' + years[i] + '">' + years[
                                    i] + '</option>';
                            }
                            // console.log(yearinfo);
                            $('#schooling_year').html(yearinfo);
                        }
                    });
                    $('#display_prev').html('');
                    $('#dispalyempty').show();
                    $('#empty').html('Details of Schooling not added');
                    // alert('Something went wrong');
                });
            } else {

                alert('Something went wrong');
            }
        }
    });
}

function documentSubmit(val) {

    var required = '<?php echo $required_fields['year']['required'] ?>';
    if (required == 'required') {
        if (yearLength == 0) {
            $('#schooling_year').removeAttr('required');
            $('#schooling_school').removeAttr('required');
            $('#schooling_class').removeAttr('required');
            $('#schooling_board').removeAttr('required');
            $('#school_address').removeAttr('required');
            $('#registration_no').removeAttr('required');
            $('#medium_of_instruction').removeAttr('required');
            goto_preview_page('document-form');
            return true;
        } else {
            alert("Enter previous details, then you can 'Save and Preview'.");
        }
    } else {
        $('#schooling_year').removeAttr('required');
        $('#schooling_school').removeAttr('required');
        $('#schooling_class').removeAttr('required');
        $('#schooling_board').removeAttr('required');
        $('#school_address').removeAttr('required');
        goto_preview_page('document-form');
        $('#document-form').attr('action', url);
        $('#document-form').submit();
        return true;
    }

}

function max_marks_total(m) {
    var maxmarks = $('#maxMarks_' + m).val();
    if (maxmarks == '') {
        $('#maxMarks_' + m).val('');
    }
    var tmaxMarks = 0;
    $('.maxMarks').each(function() {
        if (parseFloat($(this).val())) {
            tmaxMarks += parseFloat($(this).val());
        }
    });

    $('#total_max_marks_entry').val(tmaxMarks);
    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored) / parseFloat(total_max_marks_entry) * 100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function max_marks_scored_total(m) {
    var maxMarkScored = $('#maxMarkScored_' + m).val();
    if (maxMarkScored == '') {
        $('#maxMarkScored_' + m).val('');
    }
    var tmaxMakrsScored = 0;
    $('.maxMakrsScored').each(function() {
        if (parseFloat($(this).val())) {
            tmaxMakrsScored += parseFloat($(this).val());
        }
    });
    $('#total_marks_scored').val(tmaxMakrsScored);

    var total_max_marks_entry = $('#total_max_marks_entry').val();
    var total_marks_scored = $('#total_marks_scored').val();
    var percentage = parseFloat(total_marks_scored) / parseFloat(total_max_marks_entry) * 100;
    $('#total_percentage').val(percentage.toFixed(2));
}

function check_is_value_not_empty(e, sl) {
    if (e.value != '') {
        $('#maxMarks_' + sl).attr({
            "max": 125,
            "min": 100
        });

        $('#maxMarkScored_' + sl).attr({
            "max": 125,
            "min": 0
        });
    } else {
        $('#maxMarks_' + sl).attr({
            "max": 0,
            "min": 0
        });

        $('#maxMarkScored_' + sl).attr({
            "max": 0,
            "min": 0
        });
    }
}

// Helper function to show field-specific error messages
function showFieldError(fieldName, message) {
    const field = $(`[name="${fieldName}"], #${fieldName}`);

    // Remove any existing error message for this field
    field.closest('.form-group, .row, div').find('.field-error').remove();

    // Add error message after the field
    const errorElement = $(`<small class="field-error text-danger d-block mt-1">${message}</small>`);

    if (field.is('select')) {
        // For select elements, add after the parent div
        field.closest('.position-relative, div').after(errorElement);
    } else {
        // For input elements, add after the field
        field.after(errorElement);
    }

    // Also scroll to the first error if this is the first one
    if ($('.field-error').length === 1) {
        field[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

function show_upload_document_modal(view_type, document_name, document_sl_no,required) {
    const adm_setting_id = '<?= $final_preview->admission_setting_id ?>';
    var insert_id = '<?= $insert_id ?>';
    let dynamicFieldsHTML = '';

    if (view_type === 'aadhar') {
        dynamicFieldsHTML = construct_aadhar_details(adm_setting_id, document_sl_no);
    } else if (view_type === 'pan') {
        dynamicFieldsHTML = construct_pan_details(adm_setting_id, document_sl_no);
    }

    Swal.fire({
        title: `<div class="swal2-header-custom" style="text-align: center;margin: 0;">
                    <span>Upload <span id="upload_document_header">${document_name}</span> Details</span>
                </div>`,
        html: `
            <form id="upload_document_form" enctype="multipart/form-data" method="post" class="form-horizontal">
                <input type="hidden" name="document_name" id="admission_document_name" value="${document_name}">
                <input type="hidden" name="af_id" value="${insert_id}">
                <input type="hidden" id="document_sl_no" value="${document_sl_no}">
                <div id="upload_aadhar_details">${dynamicFieldsHTML}</div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'Upload',
        cancelButtonText: 'Close',
        reverseButtons: true,
        width: '40%',
        customClass: {
            popup: 'terms-confirm-popup mobile-popup-spacing',
            confirmButton: 'swal2-submit-btn',
            cancelButton: 'swal2-cancel-btn'
        },
        buttonsStyling: false,
        didOpen: () => {
            makeSwalDraggable();
            const style = document.createElement('style');
            style.textContent = `
                @media (max-width: 768px) {
                    .mobile-popup-spacing .swal2-actions {
                        margin-bottom: 20px !important;
                    }
                    .mobile-popup-spacing .swal2-confirm,
                    .mobile-popup-spacing .swal2-cancel {
                        margin-bottom: 10px !important;
                    }
                    .mobile-popup-spacing .swal2-popup {
                        margin-bottom: 20px !important;
                    }
                }
            `;
            document.head.appendChild(style);




            // Add click event to the Upload button
            const uploadBtn = Swal.getConfirmButton();
            uploadBtn.onclick = function(e) {
                e.preventDefault();

                // Validate the form before uploading
                const form = document.getElementById('upload_document_form');

                console.log('Upload button clicked for view_type:', view_type);

                // Basic validation for required fields
                let isValid = true;

                if (view_type === 'aadhar') {
                    const hasAadhar = $('select[name="has_aadhar_card"]').val();
                    const appliedForAadhar = $('select[name="applied_for_aadhar"]').val();

                    console.log('Aadhar validation - hasAadhar:', hasAadhar, 'appliedForAadhar:', appliedForAadhar);

                    // Clear any existing error messages
                    $('.text-danger').text('');

                    if (!hasAadhar) {
                        showFieldError('has_aadhar_card', 'Please select whether you have an Aadhar card or not.');
                        isValid = false;
                    } else if (hasAadhar === '1') {
                        // User has Aadhar card - validate required fields
                        const nameInAadhar = $('#name_in_aadhar').val().trim();
                        const aadharNumber = $('#aadhar_number').val().trim();
                        const aadharDocPath = $('#aadhar_doc_path').val();

                        if (!nameInAadhar) {
                            showFieldError('name_in_aadhar', 'Please enter the name as per Aadhar.');
                            isValid = false;
                        }
                        if (!aadharNumber) {
                            showFieldError('aadhar_number', 'Please enter the Aadhar number.');
                            isValid = false;
                        } else if (aadharNumber.length !== 12 || !/^\d{12}$/.test(aadharNumber)) {
                            showFieldError('aadhar_number', 'Aadhar number must be exactly 12 digits.');
                            isValid = false;
                        }
                        if (!aadharDocPath) {
                            $('.aadhar_doc_card_error').text('Please upload the Aadhar document.');
                            isValid = false;
                        }
                    } else if (hasAadhar === '0') {
                        if (!appliedForAadhar) {
                            showFieldError('applied_for_aadhar', 'Please select whether you have applied for an Aadhar card or not.');
                            isValid = false;
                        } else if (appliedForAadhar === '1') {
                            // User has applied for Aadhar - check acknowledgement
                            const acknowledgementPath = $('#acknowledgement_path').val();
                            if (!acknowledgementPath) {
                                $('.acknowledgement_card_error').text('Please upload the acknowledgement document.');
                                isValid = false;
                            }
                        } else {
                            // User hasn't applied - check declaration
                            const declarationPath = $('#aadhar_declaration_path').val();
                            if (!declarationPath) {
                                $('.declaration_card_error').text('Please upload the declaration document.');
                                isValid = false;
                            }
                        }
                    }
                } else if (view_type === 'pan') {
                    const hasPan = $('select[name="has_document"]').val();

                    console.log('PAN validation - hasPan:', hasPan);

                    // Clear any existing error messages
                    $('.text-danger').text('');

                    if (!hasPan) {
                        showFieldError('has_document', 'Please select whether you have a PAN card or not.');
                        isValid = false;
                    } else if (hasPan === '1') {
                        // User has PAN card - validate required fields
                        const panCardNumber = $('#pan_card_number').val().trim();
                        const panDocPath = $('#pan_doc_path').val();

                        if (!panCardNumber) {
                            showFieldError('pan_card_number', 'Please enter the PAN card number.');
                            isValid = false;
                        } else if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(panCardNumber)) {
                            showFieldError('pan_card_number', 'Please enter a valid PAN card number (e.g., **********).');
                            isValid = false;
                        }
                        if (!panDocPath) {
                            $('.pancard_doc_card_error').text('Please upload the PAN card document.');
                            isValid = false;
                        }
                    } else {
                        // User doesn't have PAN - check declaration
                        const declarationPath = $('#pan_declaration_path').val();
                        if (!declarationPath) {
                            $('.pan_declaration_card_error').text('Please upload the declaration document.');
                            isValid = false;
                        }
                    }
                }

                // If validation passes, proceed with upload
                if (isValid) {
                    if (view_type === 'aadhar') {
                        upload_aadhar_documents(required,document_sl_no);
                    } else if (view_type === 'pan') {
                        upload_pan_documents(required,document_sl_no);
                    }
                }
            };
        },
        showConfirmButton: true,
        allowOutsideClick: false,
        allowEscapeKey: false
    });
}

function upload_aadhar_documents(required,doc_sl_no) {

    var hidden_input_value = $('#admission_document_name').val();

    var document_name = hidden_input_value;
    // var doc_sl_no = $('#document_sl_no').val();
    var $form = $('#upload_document_form');

    // Get form values for Aadhar (using correct field names)
    var has_document = $('select[name="has_aadhar_card"]').val();
    var applied_for_aadhar = $('select[name="applied_for_aadhar"]').val();


    // Validate Aadhar specific fields
    var aadhar_doc_path = $('#aadhar_doc_path').val();
    var name_in_aadhar = $('#name_in_aadhar').val();
    var aadhar_number = $('#aadhar_number').val();
    var acknowledgement_path = $('#acknowledgement_path').val();
    var declaration_path = $('#aadhar_declaration_path').val();


    // Collect form data
    var form = $('#upload_document_form')[0];
    var formData = new FormData(form);

    formData.set('document_name', document_name);
    formData.set('has_document', has_document || '0');
    formData.set('applied_form_document', applied_for_aadhar || '0');
    formData.set('has_aadhar_card', has_document || '0');
    formData.set('applied_for_aadhar', applied_for_aadhar || '0');

    // Add fields based on user selection
    if (has_document === '1') {
        formData.set('document_file_path', aadhar_doc_path);
        formData.set('name_as_per__aadhar', name_in_aadhar);
        formData.set('aadhar_number', aadhar_number);
    } else if (applied_for_aadhar === '1') {
        // User has applied for Aadhar
        formData.set('acknowledgement_path', acknowledgement_path);
    } else {
        // User doesn't have Aadhar and hasn't applied
        formData.set('declaration_file_path', declaration_path);
    }

    $.ajax({
        url: '<?php echo site_url('admission_controller/submit_admission_documents_s3'); ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            var parsed_data = JSON.parse(response);
            if (parsed_data) {
                Swal.close();
                var cardHtml = `
                            <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style="background:#fff; border-radius:6px; border: 1px dashed #dcdcdc;">
                                <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:#FF2D2D; border-radius:10px;">
                                    <span style="color:#fff; font-weight:700; font-size:1.1em;">PDF</span>
                                </div>
                                <div class="flex-grow-1" style="min-width:0;">
                                    <div class="d-flex align-items-center justify-content-between" style="gap: 16px;">
                                        <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                                            ${document_name}
                                        </div>
                                       
                                        <div id="delete_btn_doc${doc_sl_no}">
                                             <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="deletedocument_row_new(${parsed_data},${doc_sl_no},'${required}')">
                                                <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                                    <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            `;
                const target = $('#document_row_' + doc_sl_no);
                if (target.length) {
                    target.html(cardHtml);
                     target.removeAttr('onclick');
                } else {
                    console.error("Target div not found: #document_row_" + doc_sl_no);
                }
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Upload Failed',
                text: 'There was an error uploading the Aadhar document. Please try again.',
                confirmButtonText: 'OK'
            });
        }
    });
}

function upload_pan_documents(required,doc_sl_no) {
    var hidden_input_value = $('#admission_document_name').val();
    var document_name = hidden_input_value;
    // var doc_sl_no = $('#document_sl_no').val();
    var $form = $('#upload_document_form');

    // Validate PAN specific fields
    var has_document = $('select[name="has_document"]').val();
    var applied_for_pan = $('select[name="applied_form_document"]').val();
    var pan_doc_path = $('#pan_doc_path').val();
    var pan_card_number = $('#pan_card_number').val();
    var acknowledgement_path = $('#pan_acknowledgement_path').val();
    var declaration_path = $('#pan_declaration_path').val();

    var form = $('#upload_document_form')[0];
    var formData = new FormData(form);

    formData.set('document_name', document_name);
    formData.set('has_document', has_document || '0');
    formData.set('applied_form_document', applied_for_pan || '0');

    // Add fields based on user selection
    if (has_document === '1') {
        formData.set('document_file_path', pan_doc_path);
        formData.set('pan_card_number', pan_card_number);
    } else if (applied_for_pan === '1') {
        // User has applied for Aadhar
        formData.set('acknowledgement_path', acknowledgement_path);
    } else {
        // User doesn't have Aadhar and hasn't applied
        formData.set('declaration_file_path', declaration_path);
    }
    var form = $('#upload_document_form')[0];
    var formData = new FormData(form);

    var document_name = $('#admission_document_name').val() || $('#document_name').val() || 'PAN Card';

    // Explicitly add/override PAN specific data to ensure they're included
    formData.set('document_name', document_name || 'PAN Card');
    formData.set('document_file_path', pan_doc_path);
    formData.set('pan_card_number', pan_card_number);

    // Add declaration path if exists
    var pan_declaration_path = $('#pan_declaration_path').val();
    if (pan_declaration_path) {
        formData.set('declaration_file_path', pan_declaration_path);
    }

    $.ajax({
        url: '<?php echo site_url('admission_controller/submit_admission_documents_s3'); ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            var parsed_data = JSON.parse(response);
            if (parsed_data) {
                Swal.close();
                var cardHtml = `
                            <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style="background:#fff; border-radius:6px; border: 1px dashed #dcdcdc;">
                                <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:#FF2D2D; border-radius:10px;">
                                    <span style="color:#fff; font-weight:700; font-size:1.1em;">PDF</span>
                                </div>
                                <div class="flex-grow-1" style="min-width:0;">
                                    <div class="d-flex align-items-center justify-content-between" style="gap: 16px;">
                                        <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                                            ${document_name}
                                        </div>
                                       
                                        <div id="delete_btn_doc${doc_sl_no}">
                                             <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="deletedocument_row_new(${parsed_data},${doc_sl_no},'${required}')">
                                                <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                                    <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            `;
                $('#document_row_' + doc_sl_no).html(cardHtml);
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Upload Failed',
                text: 'There was an error uploading the PAN document. Please try again.',
                confirmButtonText: 'OK'
            });
        }
    });
}

function construct_aadhar_details(adm_setting_id, document_sl_no) {
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>' ;
    const template = '<?php echo site_url('admission_controller/download_declaration/') ?>' + adm_setting_id +
        '/Aadhar Card Declaration/aadhar';

    return `
        <!-- Hidden fields for form identification -->
        <input type="hidden" name="document_type" value="aadhar" />
        <input type="hidden" name="document_sl_no" value="${document_sl_no}" />
        <input type="hidden" name="adm_setting_id" value="${adm_setting_id}" />

        <div class="row mb-4">
            <div class="col-md-12 mt-3 mb-3">
                <label class="form-label">Do you have Aadhar Card? <span class="text-danger">*</span></label>
                <div class="position-relative">
                    <select name="has_aadhar_card" id="has_aadhar_card" class="form-control" onchange="show_aadhar_details(this.value)" required>
                        <option value="">Select an option</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                    <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                        <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div id="show_aadhar_data" style="display: none;">
            <div class="row mb-4">
                <div class="col-md-12 mb-4">
                    <label for="name_in_aadhar" class="form-label">Name as per Aadhar <span class="text-danger">*</span></label>
                    <input type="text" id="name_in_aadhar" name="name_as_per_aadhar" class="form-control remove-required" placeholder="Enter the name as per Aadhar" data-parsley-pattern="^[a-zA-Z. ]+$"/>
                </div>
                <div class="col-md-12">
                    <label for="aadhar_number" class="form-label">Aadhar Number <span class="text-danger">*</span></label>
                    <input
                        type="text"
                        id="aadhar_number"
                        name="aadhar_number"
                        class="form-control remove-required"
                        placeholder="Enter Aadhar Number"
                        maxlength="12"
                        data-parsley-pattern="^[2-9]{1}[0-9]{11}$"
                        data-parsley-pattern-message="Enter a valid 12-digit Aadhar number starting with 2-9"
                        data-parsley-length="[12,12]"
                        data-parsley-trigger="blur"
                        required    
                    />
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="upload-card">
                        <label class="upload-label">Upload Aadhar Document <span class="text-danger">*</span></label>
                        <div class="border border-dashed rounded p-4 text-center position-relative"
                            style="cursor: pointer; background-color: #fafafa;" id="aadhar_doc_card"
                            onclick="document.getElementById('aadhar_doc_file').click();">
                            <div style="max-height: 25px; max-width: 25px; margin: auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                            <div class="d-flex flex-column align-items-center mt-4">
                                <i class="bi bi-upload" style="font-size: 24px;"></i>
                                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                                <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
                                <small class="aadhar_doc_card_error text-danger"></small>
                            </div>
                            <input type="file" id="aadhar_doc_file" name="document_file" class="remove-required" style="display: none;" accept="image/jpeg, application/pdf" onchange="handleAadharDocumentUpload(this,${document_sl_no})" />
                        </div>
                        <input type="hidden" id="aadhar_doc_path" name="aadhar_doc_path" value="" />
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="no_aadhar_document" style="display: none;">
            <div class="row mb-4">
                <div class="col-md-12">
                    <label class="form-label">Have you applied for Aadhar Card? <span class="text-danger">*</span></label>
                    <div class="position-relative">
                        <select name="applied_for_aadhar" id="applied_for_aadhar" class="form-control remove-required" onchange="upload_acknowledgement_details(this.value)">
                            <option value="">Select an option</option>
                            <option value="1">Yes</option>
                            <option value="0">No</option>
                        </select>
                        <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="show_acknowledgement_data" style="display: none;">
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="upload-card">
                        <label class="upload-label">Upload Acknowledgement <span class="text-danger">*</span></label>
                        <div class="border border-dashed rounded p-4 text-center position-relative"
                            style="cursor: pointer; background-color: #fafafa;"
                            onclick="document.getElementById('acknowledgement_file').click();" id="acknowledgement_card">
                            <div style="max-height: 25px; max-width: 25px; margin: auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                            <div class="d-flex flex-column align-items-center mt-4">
                                <i class="bi bi-upload" style="font-size: 24px;"></i>
                                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                                <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
                                <small class="acknowledgement_card_error text-danger"></small>
                            </div>
                            <input type="file" id="acknowledgement_file" name="acknowledgement_file" class="remove-required" style="display: none;" accept="image/jpeg, application/pdf" onchange="handleAcknowledgementUpload(this)" />
                        </div>
                        <input type="hidden" id="acknowledgement_path" name="acknowledgement_path" value="" />
                    </div>
                </div>
            </div>
        </div>

        <div id="download_aadhar_acknowledgement" style="display: none;">
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="border rounded p-4 d-flex align-items-center justify-content-between" style="background-color: #f8f9fa;">
                        <div class="d-flex align-items-center">
                            <div class="me-3" style="background-color: #6c757d; color: white; padding: 12px 16px; border-radius: 8px; font-weight: bold; font-size: 14px;">
                                PDF
                            </div>
                            <div>
                                <h6 class="mb-1 fw-semibold">Declaration form</h6>
                                <small class="text-muted">Download the declaration, sign and upload it below</small>
                            </div>
                        </div>
                        <div>
                            <a href="${template}" class="btn btn-outline-secondary btn-sm d-flex align-items-center">
                               <span><?php $this->load->view('svg_icons/download_icon.svg'); ?></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3 mb-4">
                <div class="col-md-12">
                    <div class="upload-card">
                        <label class="upload-label">Upload Declaration <span class="text-danger">*</span></label>
                        <div class="border border-dashed rounded p-4 text-center position-relative"
                            style="cursor: pointer; background-color: #fafafa;"
                            onclick="document.getElementById('aadhar_declaration').click();" id="declaration_card">
                            <div style="max-height: 25px; max-width: 25px; margin: auto; display: flex; align-items: center; justify-content: center;">
                                <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                            </div>
                            <div class="d-flex flex-column align-items-center mt-4">
                                <i class="bi bi-upload" style="font-size: 24px;"></i>
                                <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                                <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
                                <small class="declaration_card_error text-danger"></small>
                            </div>
                            <input type="file" id="aadhar_declaration" name="declaration_file" class="remove-required" style="display: none;" accept="image/jpeg, application/pdf" onchange="handledeclarationUpload(this)" />
                        </div>
                        <input type="hidden" id="aadhar_declaration_path" name="aadhar_declaration_path" value="" />
                    </div>
                </div>
            </div>
        </div>
    `;
}

function construct_pan_details(adm_setting_id) {
    const template = '<?php echo site_url('admission_controller/download_declaration/') ?>' + adm_setting_id +
        '/PAN Card Declaration/pan';
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>' ;

    return `
        <!-- Do you have PAN -->
        <div class="row mt-3 mb-4">
            <div class="col-md-12">
                <label class="form-label">Do you have PAN Card? <span class="text-danger">*</span></label>
                <div class="position-relative">
                    <select name="has_document" id="has_document" class="form-control" onchange="show_pan_details(this.value)" required>
                        <option value="">Select an option</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                    <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                        <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- PAN Details -->
        <div id="show_pancard_data" style="display:none">
                <label class="me-3" style="min-width: 200px;">PAN Card Number <span class="text-danger">*</span></label>
                <input class="form-control remove-required" style="flex: 1;" id="pan_card_number" name="pan_card_number" placeholder="Enter the PAN Card Number"
                       data-parsley-minlength="10" data-parsley-maxlength="10" pattern="[A-Z0-9]{10}" data-parsley-error-message="Should contain 10 alphanumeric characters">

            <div class="row mb-5 mt-4">
                <div class="upload-card">
                    <label class="upload-label">Upload PAN Card Document <span class="text-danger">*</span></label>
                    <div class="border border-dashed rounded p-4 text-center position-relative"
                         style="cursor: pointer; background-color: #fafafa;"
                         onclick="document.getElementById('pancard_doc_file').click();" id="pancard_doc_card">

                        <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                            <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                        </div>

                        <div class="d-flex flex-column align-items-center mt-4">
                            <i class="bi bi-upload" style="font-size: 24px;"></i>
                            <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                            <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
                            <small class="pancard_doc_card_error text-danger"></small>
                        </div>

                        <input type="file" id="pancard_doc_file" name="document_file"
                               class="remove-required" style="display: none;"
                               accept="application/pdf, image/png, image/jpeg" onchange="handlePanDocumentUpload(this)" />
                    </div>
                    <input type="hidden" id="pan_doc_path" name="pan_doc_path" value="" />
                </div>
            </div>
        </div>

        <!-- Applied for PAN? -->
        <div id="no_pan_document" style="display:none;">
            <div class="row mb-4">
                <div class="col-md-12">
                    <label class="form-label">Have you applied for PAN Card ? <span class="text-danger">&nbsp;*</span></label>
                    <div class="position-relative">
                        <select name="applied_form_document" id="applied_form_document" class="form-control" onchange="upload_pan_acknowledgement_details(this.value)">
                            <option value="">Select an option</option>
                            <option value="1">Yes</option>
                            <option value="0">No</option>
                        </select>
                        <div style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); pointer-events: none;">
                            <?php $this->load->view('svg_icons/down_arrow.svg'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Acknowledgement -->
        <div id="show_pan_acknowledgement_data" class="row mb-5" style="display:none">
            <div class="upload-card">
                <label class="upload-label">Upload Acknowledgement <span class="text-danger">*</span></label>
                <div class="border border-dashed rounded p-4 text-center position-relative"
                     style="cursor: pointer; background-color: #fafafa;"
                     onclick="document.getElementById('pan_acknowledgement').click();" id="pan_acknowledgement_card">

                    <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                        <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                    </div>

                    <div class="d-flex flex-column align-items-center mt-4">
                        <i class="bi bi-upload" style="font-size: 24px;"></i>
                        <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                        <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
                        <small class="pan_acknowledgement_card_error text-danger"></small>
                    </div>

                    <input type="file" id="pan_acknowledgement" name="acknowledgement_file"
                           class="remove-required" style="display: none;"
                           accept="application/pdf, image/png, image/jpeg" onchange="handlepanAcknowledgementUpload(this)" />
                </div>
                <input type="hidden" id="pan_acknowledgement_path" name="acknowledgement_path" value="" />
            </div>
        </div>

        <!-- Declaration Section -->
        <div id="download_pancard_acknowledgement" style="display:none">
            <div class="border rounded p-4 d-flex align-items-center justify-content-between mb-3" style="background-color: #f8f9fa;">
                <div class="d-flex align-items-center">
                    <div class="me-3" style="background-color: #6c757d; color: white; padding: 12px 16px; border-radius: 8px; font-weight: bold; font-size: 14px;">
                        PDF
                    </div>
                    <div>
                        <h6 class="mb-1 fw-semibold">Declaration form</h6>
                        <small class="text-muted">Download the declaration, sign and upload it below</small>
                    </div>
                </div>
                <div>
                    <a href="${template}" class="btn btn-outline-secondary btn-sm d-flex align-items-center">
                       <span><?php $this->load->view('svg_icons/download_icon.svg'); ?></span>
                    </a>
                </div>
            </div>

            <div class="row mt-3 mb-5">
                <div class="upload-card">
                    <label class="upload-label">Upload Declaration <span class="text-danger">*</span></label>
                    <div class="border border-dashed rounded p-4 text-center position-relative"
                         style="cursor: pointer; background-color: #fafafa;"
                         onclick="document.getElementById('pan_declaration').click();" id="pan_declaration_card">

                        <div style="max-height: 25px; max-width: 25px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                            <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
                        </div>

                        <div class="d-flex flex-column align-items-center mt-4">
                            <i class="bi bi-upload" style="font-size: 24px;"></i>
                            <span><strong style="color: #6c63ff;">Click to upload</strong></span>
                            <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
                            <small class="pan_declaration_card_error text-danger"></small>
                        </div>

                        <input type="file" id="pan_declaration" name="declaration_file"
                               class="remove-required" style="display: none;"
                               accept="application/pdf, image/png, image/jpeg"
                               onchange="handleDeclarationUpload(this)">
                    </div>

                    <!-- Hidden input to store file path -->
                    <input type="hidden" id="pan_declaration_path" name="pan_declaration_path" value="" />
                </div>
            </div>
        </div>
    `;
}

function show_aadhar_details(e) {
    if (e == 1) {
        // User has Aadhar Card
        $('#show_aadhar_data').show();
        $('#no_aadhar_document').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();

        // Set required fields for Aadhar details
        $('#name_in_aadhar').attr('required', 'required');
        $('#aadhar_number').attr('required', 'required');
        $('#aadhar_doc_file').attr('required', 'required');

        // Remove required from other sections
        $('#applied_for_aadhar').removeAttr('required');
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    } else if (e == 0) {
        // User doesn't have Aadhar Card
        $('#no_aadhar_document').show();
        $('#show_aadhar_data').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();

        // Set required for applied_for_aadhar field
        $('#applied_for_aadhar').attr('required', 'required');

        // Remove required from Aadhar details
        $('#name_in_aadhar').removeAttr('required');
        $('#aadhar_number').removeAttr('required');
        $('#aadhar_doc_file').removeAttr('required');
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    } else {
        // Reset when no option selected
        $('#show_aadhar_data').hide();
        $('#no_aadhar_document').hide();
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();

        // Remove all required attributes
        $('#name_in_aadhar').removeAttr('required');
        $('#aadhar_number').removeAttr('required');
        $('#aadhar_doc_file').removeAttr('required');
        $('#applied_for_aadhar').removeAttr('required');
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    }
}

function upload_acknowledgement_details(e) {
    if (e == 1) {
        // User has applied for Aadhar - show acknowledgement upload
        $('#show_acknowledgement_data').show();
        $('#download_aadhar_acknowledgement').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').attr('required', 'required');
        $('#aadhar_declaration').removeAttr('required');
    } else if (e == 0) {
        // User has not applied for Aadhar - show declaration download/upload
        $('#download_aadhar_acknowledgement').show();
        $('#show_acknowledgement_data').hide();
        $('#show_aadhar_data').hide();
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').attr('required', 'required');
    } else {
        // Reset when no option selected
        $('#show_acknowledgement_data').hide();
        $('#download_aadhar_acknowledgement').hide();
        $('#acknowledgement_file').removeAttr('required');
        $('#aadhar_declaration').removeAttr('required');
    }
}

function show_pan_details(e) {
    if (e == 1) {
        $('#show_pancard_data').show();
        $('#no_pan_document').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').attr('required', 'required');
        $('#pancard_doc_file').attr('required', 'required');
        $('#pan_acknowledgement').removeAttr('required');
        $('#pan_declaration').removeAttr('required');
    } else if (e == 0) {
        $('#no_pan_document').show();
        $('#show_pancard_data').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').removeAttr('required');
        $('#pancard_doc_file').removeAttr('required');
    } else {
        // Reset when no option selected
        $('#show_pancard_data').hide();
        $('#no_pan_document').hide();
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#pan_card_number').removeAttr('required');
        $('#pancard_doc_file').removeAttr('required');
        $('#pan_acknowledgement').removeAttr('required');
        $('#pan_declaration').removeAttr('required');
    }
}

function upload_pan_acknowledgement_details(e) {
    if (e == 1) {
        $('#show_pan_acknowledgement_data').show();
        $('#download_pancard_acknowledgement').hide();
        $('#show_pancard_data').hide();
        $('#pan_acknowledgement').attr('required', 'required');
        $('#pan_declaration').removeAttr('required');
    } else if (e == 0) {
        $('#download_pancard_acknowledgement').show();
        $('#show_pan_acknowledgement_data').hide();
        $('#show_pancard_data').hide();
        $('#pan_declaration').attr('required', 'required');
        $('#pan_acknowledgement').removeAttr('required');
    } else {
        // Reset when no option selected
        $('#show_pan_acknowledgement_data').hide();
        $('#download_pancard_acknowledgement').hide();
        $('#show_pancard_data').hide();
        $('#pan_acknowledgement').removeAttr('required');
        $('#pan_declaration').removeAttr('required');
    }
}

$(document).ready(function() {
    // Student Photo Upload
    $('#fileupload').change(function() {
        var src = $(this).val();

        if (src && validate_photo_size(this.files[0], 'fileupload', 'fileuploadError')) {
            $('#photo_placeholder').hide();
            $('#previewing').css('display', 'block');

            $("#fileuploadError").html("");
            readURL(this, 'previewing');

            // Start upload
            saveFileToStorage(this.files[0], 'fileupload', 'student_high_quality_url');
        } else {
            this.value = null;
        }
    });

    // Student Signature Upload
    $('#stud_sign_fileupload').change(function() {
        var src = $(this).val();

        if (src && validate_photo_size(this.files[0], 'stud_sign_fileupload',
                'stud_sign_fileuploadError')) {
            $('#stud_sig_placeholder').hide();
            $('#stud_sig_previewing').css('display', 'block');

            $("#stud_sign_fileuploadError").html("");
            readURL(this, 'stud_sig_previewing');

            // Start upload
            saveFileToStorage(this.files[0], 'stud_sign_fileupload', 'signature_img_path');
        } else {
            this.value = null;
        }
    });

    // Family Photo Upload
    $('#family_fileupload').change(function() {
        var src = $(this).val();

        if (src && validate_photo_size(this.files[0], 'family_fileupload', 'family_fileuploadError')) {
            $('#family_placeholder').hide();
            $('#family_previewing').css('display', 'block');

            $("#family_fileuploadError").html("");
            readURL(this, 'family_previewing');

            // Start upload
            saveFileToStorage(this.files[0], 'family_fileupload', 'family_photo_path');
        } else {
            this.value = null;
        }
    });
});

function addProgressOverlay(imageSelector) {
    const $image = $(imageSelector);
    const $parent = $image.parent();

    // Remove existing overlay if any
    $parent.find('.upload-progress-overlay').remove();

    // Create progress overlay
    const progressHtml = `
        <div class="upload-progress-overlay" style="
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 8px;
        ">
            <div class="progress-content" style="
                text-align: center;
                color: white;
            ">
                <div class="progress-circle" style="
                    width: 60px;
                    height: 60px;
                    border: 4px solid rgba(255, 255, 255, 0.3);
                    border-top: 4px solid #6c63ff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 10px;
                "></div>
                <div class="progress-text" style="
                    font-size: 16px;
                    font-weight: bold;
                ">0%</div>
                <div style="font-size: 12px; margin-top: 5px;">Uploading...</div>
            </div>
        </div>
    `;

    // Make parent position relative if not already
    if ($parent.css('position') === 'static') {
        $parent.css('position', 'relative');
    }

    $parent.append(progressHtml);

    // Add CSS animation
    if (!$('#progress-spinner-style').length) {
        $('head').append(`
            <style id="progress-spinner-style">
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `);
    }
}

function updateProgressOverlay(percentage) {
    $('.upload-progress-overlay .progress-text').text(percentage + '%');

    if (percentage >= 100) {
        setTimeout(() => {
            $('.upload-progress-overlay').fadeOut(300, function() {
                $(this).remove();
                $('#previewing').css('opacity', '1');
            });
        }, 500);
    }
}

function readURL(input, targetId = 'previewing') {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
            console.log(e.target.result);
            $('#' + targetId).attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

function toTitleCase(str) {
    if (!str) return str;
    return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}

function goto_preview_page(form_id) {
    var $form = $('#'+form_id);
    var form = $('#'+form_id)[0];
    var formData = new FormData(form);

    // Extract insertId from the form and add it as lastId
    var insertIdInput = $form.find('input[name="insertId"]');
    if (insertIdInput.length > 0) {
        var insertIdValue = insertIdInput.val();
        if (insertIdValue && insertIdValue.trim() !== '') {
            formData.append('lastId', insertIdValue);
        } 
    } 

    var $visibleSteps = $('.step:visible');
    var $current = $visibleSteps.filter('.active');
    var idx = $visibleSteps.index($current);
    if (idx < $visibleSteps.length - 1) {
        var nextStep = $visibleSteps.eq(idx + 1).data('step');
        goToStep(nextStep);
    }
    $.ajax({
        url: '<?php echo site_url("admission_controller/get_preview_data"); ?>',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        success: function(data) {
            let response = JSON.parse(data);
            console.log(response);
            const firstName = toTitleCase(response.previewData.std_name || '');
            const middleName = toTitleCase(response.previewData.student_middle_name || '');
            const lastName = toTitleCase(response.previewData.student_last_name || '');

            // Handle profile photo with default SVG for empty/null values
            if (response.previewData.std_photo_uri && response.previewData.std_photo_uri.trim() !== '') {
                $('.profile-photo').attr('src', response.previewData.std_photo_uri);
            } else {
                // Set default empty image SVG
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                var svgDataUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
                $('.profile-photo').attr('src', svgDataUrl);
            }
            $('.profile-name').text(`${firstName} ${middleName} ${lastName}`.trim());
            $('.profile-details').html('Grade ' + response.previewData.grade_applied_for + '<br>DOB: ' +
                response.previewData.dob);
            construct_personal_info(response.fields, response.previewData, response.form_year);
            construct_parent_info('father', response.fields, response.previewData);
            construct_parent_info('mother', response.fields, response.previewData);
            construct_guardian_info(response.fields, response.previewData);
            construct_medical_info(response.health_display_field, response.admission_medical);
            renderHospitalizationTable(response.hospitalization_details);
            renderVaccinationDetails(response.admission_vaccination);
            if(response.admission_prev_schools){
            $('#previous_schooling_block').show();
            construct_previous_school_table(response.disabled_fields, response.admission_prev_schools);

            // Check if subjectArray exists before calling the function
            const subjectArray = response.subjectArray || [];
            constructSubjectDetailsTable(subjectArray, response.admission_prev_schools, response
                .show_previous_schooling_subject_total_marks, response
                .show_previous_schooling_subject_percentage, response
                .show_previous_schooling_overall_total_marks, response
                .show_previous_schooling_overall_percentage, response.preSchoolingmarks, response
                .combinations, response.comb, response.previewData, response.subject_master, response
                .lang_selection);
            }else{
            $('#previous_schooling_block').hide();
            }
           if(response.admission_form){
            renderPhotosAndDocuments(response.previewData, response.admission_form);
           }
        }
    });
}

function construct_personal_info(fields, previewData, form_year) {
    var label = '<?php echo $this->settings->getSetting('your_word_for_class') ?>';
    var html = '';

    const search = ["birth_taluk", "birth_district", "dob", "std_name", "sats_number", "esl_english_as_second_language",
        "physically_challenged_discription", "reason_for_joining_this_institute", "student_area_of_strength",
        "student_area_of_improvement", "did_they_enrolled_in_different_institute_earlier", "has_medical_concerns",
        "s_present_addr","addr",
        "_", "std", "s "
    ];
    const replace = ["Birth Place", "Birth State", "DOB", "Student First Name", "SATS Number",
        "ESL (English As Second Language)", "Physically Challenged Description", "Reason for joining",
        "Student Areas of Strength", "Student Areas of Improvement", "Enrolled in different institute earlier",
        "Has Medical Concerns", "Present Address","Address", " ", "student", ""
    ];

    const studentFields = fields.student ?
        (Array.isArray(fields.student) ? fields.student : Object.values(fields.student)) :
        [];

    studentFields.forEach((field, index) => {

        let head = field;
        for (let j = 0; j < search.length; j++) {
            head = head.replaceAll(search[j], replace[j]);
        }
        head = head.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
        if (head === 'Grade Applied For' && label) {
            head = head.replace('Grade', label);
        }

        let value = '-';
        if (field === 'academic_year_applied_for') {
            value = form_year;
        } else if (field === 'student_mobile_no') {
            const code = previewData['s_country_code'] || '';
            const number = previewData['student_mobile_no'] || '';
            value = number ? `${code} ${number}` : '-';
        } else if (previewData[field]) {
            value = previewData[field];
        }

        if (head == 'Student signature') {
            // Handle student signature with default image
            var studentSignatureUrl = previewData[field];
            if (!studentSignatureUrl || studentSignatureUrl.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                studentSignatureUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${studentSignatureUrl}" class="img-thumbnail"/>
                    </div>`;

        }

        html += `
                    <div class="col-md-6">
                        <span class="label" style="display: inline-block; margin-bottom: 12px;">${toTitleCase(head)}</span><br>
                        <span class="value">${value}</span>
                    </div>
                `;
    });

    // Add custom fields (if any) - only if family_photo is included in studentFields
    if (previewData.custom_field && typeof previewData.custom_field === 'object') {
        for (const [key, val] of Object.entries(previewData.custom_field)) {
            const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            html += `
                <div class="col-md-6">
                <span class="label" style="display: inline-block; margin-bottom: 12px;">${label}</span><br>
                <span class="value" style="display: inline-block; margin-bottom: 15px;">${val}</span>
            </div>
            `;
        }
    }

    // Handle family photo with default image
    if(studentFields.includes('family_photo')) {
    var familyPhotoUrl = previewData['family_photo'];
    if (!familyPhotoUrl || familyPhotoUrl.trim() === '') {
        var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
        familyPhotoUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
    }
    html += `<div class="col-md-6">
                <span class="label" style="display: inline-block; margin-bottom: 12px;">Family Photo</span><br>
                <span class="value" style="display: inline-block; margin-bottom: 15px;">
                <div>
                    <img width="80" height="80" src="${familyPhotoUrl}" class="img-thumbnail"/>
                </div>
                </span>
            </div>
            `;
    }
    $('#personal-info-grid').html(html);
}

function construct_parent_info(type, fields, previewData) {
    let html = '';

    const parentFields = fields[type] ?
        (Array.isArray(fields[type]) ? fields[type] : Object.values(fields[type])) :
        [];
    const prefix = type === 'father' ? 'f' : 'm';
    const containerId = type === 'father' ? '#father-info-grid' : '#mother-info-grid';

    // Define a standardized field order for both parents to ensure synchronized display
    const fieldOrder = [
        `${prefix}_photo`,
        `${prefix}_name`,
        `${prefix}_middle_name`,
        `${prefix}_last_name`,
        `${prefix}_dob`,
        `${prefix}_mobile_no`,
        `${prefix}_email`,
        `${prefix}_qualification`,
        `${prefix}_occupation`,
        `${prefix}_designation`,
        `${prefix}_organization`,
        `${prefix}_type_of_organization`,
        `${prefix}_office_addr`,
        `${prefix}_annual_income`,
        `${prefix}_nationality`,
        `${prefix}_aadhar`,
        `${prefix}_pan`,
        `${prefix}_religion`,
        `${prefix}_caste`,
        `${prefix}_mother_tongue`,
        `${prefix}_addr`,
        `${prefix}_signature`
    ];

    // Create a set of available fields for quick lookup
    const availableFields = new Set(parentFields);

    // Process fields in the predefined order
    fieldOrder.forEach(field => {
        // Only process if the field exists in the available fields
        if (!availableFields.has(field)) return;
        if (field === `${prefix}_country_code`) return;
        if (field === `family_photo` || field == 'modified_on') return;

        let head = '';
        if (field === `${prefix}_name`) {
            head = `${type.charAt(0).toUpperCase() + type.slice(1)} First Name`;
        } else if (field === 'f_type_of_organization' && type === 'father') {
            head = 'Type of Organization';
        } else {
            head = field
                .replace(/_/g, ' ')
                .replace(new RegExp(prefix + ' ', 'g'), '')
                .replace(/\b\w/g, l => l.toUpperCase());
        }

        let value = '-';
        if (!previewData[field] || previewData[field] === '') {
            value = '-';
        } else if (field === `${prefix}_dob`) {
            const dob = previewData[field];
            const age = calculateAge(dob);
            value = `${dob} (Age - ${age})`;
        } else if (field === `${prefix}_mobile_no`) {
            const code = previewData[`${prefix}_country_code`] || '';
            const number = previewData[`${prefix}_mobile_no`] || '';
            value = number ? `${code} ${number}` : '-';
        } else {
            value = previewData[field];
        }

        if (head == 'Father Photo' || head == 'Photo') {
            // Handle father photo with default image
            var fatherPhotoUrl = previewData[field];
            if (!fatherPhotoUrl || fatherPhotoUrl.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                fatherPhotoUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${fatherPhotoUrl}" class="img-thumbnail"/>
                    </div>`;
        }
        if (head == 'Mother Photo' || head == 'Photo') {
            // Handle mother photo with default image
            var motherPhotoUrl = previewData[field];
            if (!motherPhotoUrl || motherPhotoUrl.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                motherPhotoUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${motherPhotoUrl}" class="img-thumbnail"/>
                    </div>`;

        }
        if (head == 'Signature') {
            // Handle signature with default image
            var signatureUrl = previewData[field];
            if (!signatureUrl || signatureUrl.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                signatureUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${signatureUrl}" class="img-thumbnail"/>
                    </div>`;

        }
        var search = ['Addr','County']
        var replace = ["Address","Country"]
        for (let j = 0; j < search.length; j++) {
            head = head.replaceAll(search[j], replace[j]);
        }
        html += `
            <div class="col-md-12 mb-4">
                <span class="label" style="display: inline-block; margin-bottom: 12px;">${toTitleCase(head)}</span><br>
                <span class="value" style="display: inline-block; margin-bottom: 15px;">${value}</span>
            </div>
        `;
    });

    // Process any remaining fields that weren't in the predefined order
    parentFields.forEach(field => {
        // Skip if already processed or should be excluded
        if (fieldOrder.includes(field)) return;
        if (field === `${prefix}_country_code`) return;
        if (field === `family_photo` || field == 'modified_on') return;

        let head = '';
        if (field === `${prefix}_name`) {
            head = `${type.charAt(0).toUpperCase() + type.slice(1)} First Name`;
        } else if (field === 'f_type_of_organization' && type === 'father') {
            head = 'Type of Organization';
        } else {
            head = field
                .replace(/_/g, ' ')
                .replace(new RegExp(prefix + ' ', 'g'), '')
                .replace(/\b\w/g, l => l.toUpperCase());
        }

        let value = '-';
        if (!previewData[field] || previewData[field] === '') {
            value = '-';
        } else if (field === `${prefix}_dob`) {
            const dob = previewData[field];
            const age = calculateAge(dob);
            value = `${dob} (Age - ${age})`;
        } else if (field === `${prefix}_mobile_no`) {
            const code = previewData[`${prefix}_country_code`] || '';
            const number = previewData[`${prefix}_mobile_no`] || '';
            value = number ? `${code} ${number}` : '-';
        } else {
            value = previewData[field];
        }

        if (head == 'Father Photo') {
            var f_photo_url = previewData[field];
            if (!f_photo_url || f_photo_url.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                f_photo_url = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${f_photo_url}" class="img-thumbnail"/>
                    </div>`;
        }
        if (head == 'Mother Photo') {
            var m_photo_url = previewData[field];
            if (!m_photo_url || m_photo_url.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                m_photo_url = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${m_photo_url}" class="img-thumbnail"/>
                    </div>`;

        }
        if (head == 'Signature') {
            // Handle signature with default image
            var signatureUrl = previewData[field];
            if (!signatureUrl || signatureUrl.trim() === '') {
                var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
                signatureUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
            }
            value = `<div>
                        <img width="80" height="80" src="${signatureUrl}" class="img-thumbnail"/>
                    </div>`;

        }
        var search = ['Addr','County','Res Ph','Office Ph']
        var replace = ["Address","Country","Residential Number","Office Number"]
        for (let j = 0; j < search.length; j++) {
            head = head.replaceAll(search[j], replace[j]);
        }
        html += `
            <div class="col-md-12 mb-4">
                <span class="label" style="display: inline-block; margin-bottom: 12px;">${toTitleCase(head)}</span><br>
                <span class="value" style="display: inline-block; margin-bottom: 15px;">${value}</span>
            </div>
        `;
    });

    $(containerId).html(html);
}

function calculateAge(dob) {
    // dob should be in 'YYYY-MM-DD' format
    var dobDate = new Date(dob);
    var now = new Date();
    var age = now.getFullYear() - dobDate.getFullYear();
    var m = now.getMonth() - dobDate.getMonth();
    if (m < 0 || (m === 0 && now.getDate() < dobDate.getDate())) {
        age--;
    }
    return age;
}

function construct_guardian_info(fields, previewData) {
    let html = '';

    const search = ["_", "g ",'addr','county','g_res_ph','g_office_ph'];
    const replace = [" ", "","address","country","g_residential_number","g_office_number"];

    const guardianFields = fields.gurdian ?
        (Array.isArray(fields.gurdian) ? fields.gurdian : Object.values(fields.gurdian)) :
        [];

    guardianFields.forEach((field) => {
        if (field === 'g_country_code') return;

        // Build heading
        let head = field;
        for (let j = 0; j < search.length; j++) {
            head = head.replaceAll(search[j], replace[j]);
        }
        head = head.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

        // Build value
        let value = '-';
        if (field === 'g_mobile_no') {
            const code = previewData['g_country_code'] || '';
            const number = previewData['g_mobile_no'] || '';
            value = number ? `${code} ${number}` : '-';
        } else if (previewData[field]) {
            value = previewData[field];
        }

        var guardianPhotoUrl = previewData['uri'];
        if (!guardianPhotoUrl || guardianPhotoUrl.trim() === '') {
            var defaultImageSvg = `<?php $this->load->view('svg_icons/empty_image.svg'); ?>`;
            guardianPhotoUrl = 'data:image/svg+xml;base64,' + btoa(defaultImageSvg);
        }
        if (head == 'Photo uri') {
            head = 'Photo';
            value = `<div>
                        <img width="80" height="80" src="${guardianPhotoUrl}" class="img-thumbnail"/>
                    </div>`;

        }

        // Add to html
        html += `
            <div class="col-md-6">
                <span class="label" style="display: inline-block; margin-bottom: 12px;">${toTitleCase(head)}</span><br>
                <span class="value" style="display: inline-block; margin-bottom: 15px;">${value}</span>
            </div>
        `;
    });

    $('#guardian-info-grid').html(html);
}

function construct_medical_info(health_display_field, admission_medical) {
    let html = '';

    const search = ["foodytpe", "_", "height", "weight", "family_history"];
    const replace = ["Food type", " ", "Student Height", "Student Weight", "Family Medical History"];

    const medicalFields = Array.isArray(health_display_field) ?
        health_display_field :
        Object.values(health_display_field);

    medicalFields.forEach(field => {
        let head = field;
        for (let i = 0; i < search.length; i++) {
            head = head.replaceAll(search[i], replace[i]);
        }
        head = head.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

        let value = '-';
        if (admission_medical[field]) {
            value = admission_medical[field];
        }

        html += `
            <div class="col-md-6">
                <span class="label" style="display: inline-block; margin-bottom: 12px;">${toTitleCase(head)}</span><br>
                <span class="value" style="display: inline-block; margin-bottom: 15px;">${value}</span>
            </div>
        `;
    });

    $('#medical-info-grid').html(html);
}

function renderHospitalizationTable(hospitalization_details) {
    // Check if we're on mobile
    const isMobile = window.matchMedia('(max-width: 768px)').matches;

    let html = '';

    if (isMobile) {
        // Mobile-friendly card layout
        html = `<div class="hospitalization-mobile-container">`;

        if (Array.isArray(hospitalization_details) && hospitalization_details.length > 0) {
            hospitalization_details.forEach((val, index) => {
                html += `
                    <div class="hospitalization-card mb-3 p-3" style="border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;">
                        <h6 class="mb-2" style="color: #623CE7; font-weight: bold;">Record #${index + 1}</h6>
                        <div class="row">
                            <div class="col-6 mb-2">
                                <small class="text-muted">Hospital:</small>
                                <div style="font-weight: 500;">${val.hospital_admitted || '-'}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Admission Date:</small>
                                <div style="font-weight: 500;">${val.hospitilization_date || '-'}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Discharge Date:</small>
                                <div style="font-weight: 500;">${val.discharge_date || '-'}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Reason:</small>
                                <div style="font-weight: 500;">${val.hospitilization_reason || '-'}</div>
                            </div>
                            <div class="col-12 mb-2">
                                <small class="text-muted">Treatment:</small>
                                <div style="font-weight: 500;">${val.treatment_provided || '-'}</div>
                            </div>
                            <div class="col-12">
                                <small class="text-muted">Remarks:</small>
                                <div style="font-weight: 500;">${val.remarks || '-'}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
        } else {
            html += `
                <div class="text-center p-4" style="border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;">
                    <p class="mb-0 text-muted">No hospitalization records available</p>
                </div>
            `;
        }

        html += `</div>`;
    } else {
        // Desktop table layout
        html = `
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Hospital Admitted To</th>
                            <th>Hospitalization Date</th>
                            <th>Hospital Reason</th>
                            <th>Discharge Date</th>
                            <th>Treatment Provided</th>
                            <th>Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        if (Array.isArray(hospitalization_details) && hospitalization_details.length > 0) {
            hospitalization_details.forEach((val, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${val.hospital_admitted || '-'}</td>
                        <td>${val.hospitilization_date || '-'}</td>
                        <td>${val.hospitilization_reason || '-'}</td>
                        <td>${val.discharge_date || '-'}</td>
                        <td>${val.treatment_provided || '-'}</td>
                        <td>${val.remarks || '-'}</td>
                    </tr>
                `;
            });
        } else {
            html += `
                <tr>
                    <td colspan="7" class="text-center">No records available</td>
                </tr>
            `;
        }

        html += `</tbody></table></div>`;
    }

    // Inject into the container
    $('#hospitalization-info-grid').html(html);
}

function formatDate(dateStr) {
    if (!dateStr || dateStr === '0000-00-00') return '-';
    const parts = dateStr.split('-');
    if (parts.length === 3) {
        return `${parts[2]}-${parts[1]}-${parts[0]}`;
    }
    return dateStr;
}


function renderVaccinationDetails(admission_vaccination) {
    const vaccine_master = <?= json_encode($this->config->item('vaccine_master')) ?>;
    const isMobile = window.matchMedia('(max-width: 768px)').matches;

    let html = '';

    if (isMobile) {
        // Mobile-friendly card layout
        html = `<div class="vaccination-mobile-container">`;

        vaccine_master.forEach((vaccineName, index) => {
            const vaccKey = vaccineName.toLowerCase().replace(/\s+/g, '_');
            const vaccData = admission_vaccination[vaccKey] || {};

            let status = vaccData.status;
            let date = vaccData.vacc_date;
            let remarks = vaccData.description || '-';

            let statusText = '';
            let statusColor = '';
            let dateInfo = '';

            if (Object.hasOwn(vaccData, 'status')) {
                if (status == 0) {
                    statusText = 'Not Vaccinated';
                    statusColor = '#dc3545';
                } else {
                    statusText = 'Vaccinated';
                    statusColor = '#28a745';
                    const vaccDate = (date && date !== '0000-00-00') ? formatDate(date) : '-';
                    dateInfo = `<div class="mt-1"><small class="text-muted">Date:</small> <span style="font-weight: 500;">${vaccDate}</span></div>`;
                }
            } else {
                statusText = 'Not Submitted';
                statusColor = '#6c757d';
            }

            html += `
                <div class="vaccination-card mb-3 p-3" style="border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0" style="color: #623CE7; font-weight: bold;">${vaccineName}</h6>
                        <span class="badge" style="background-color: ${statusColor}; color: white; font-size: 11px; padding: 4px 8px;">${statusText}</span>
                    </div>
                    ${dateInfo}
                    <div class="mt-2">
                        <small class="text-muted">Remarks:</small>
                        <div style="font-weight: 500; font-size: 14px;">${remarks}</div>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
    } else {
        // Desktop table layout
        html = `
            <div class="table-responsive">
                <table class="table table-bordered" id="vaccination_preview">
                    <thead>
                        <tr>
                            <th>Vaccine Name</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        vaccine_master.forEach((vaccineName) => {
            const vaccKey = vaccineName.toLowerCase().replace(/\s+/g, '_');
            const vaccData = admission_vaccination[vaccKey] || {};

            let status = vaccData.status;
            let date = vaccData.vacc_date;
            let remarks = vaccData.description || '-';

            let statusText = '';
            let dateText = '-';

            if (Object.hasOwn(vaccData, 'status')) {
                if (status == 0) {
                    statusText = '<span class="badge badge-danger">Not Vaccinated</span>';
                } else {
                    statusText = '<span class="badge badge-success">Vaccinated</span>';
                    dateText = (date && date !== '0000-00-00') ? formatDate(date) : '-';
                }
            } else {
                statusText = '<span class="badge badge-secondary">Not Submitted</span>';
            }

            html += `
                <tr>
                    <td style="font-weight: 500;">${vaccineName}</td>
                    <td>${statusText}</td>
                    <td>${dateText}</td>
                    <td>${remarks}</td>
                </tr>
            `;
        });

        html += `</tbody></table></div>`;
    }

    // Inject into the DOM
    $('#vaccination-info-grid').html(html);
}

// Helper function to format date
function formatDate(dateString) {
    if (!dateString || dateString === '0000-00-00') return '-';

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString; // Return original if invalid

        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    } catch (e) {
        return dateString; // Return original if error
    }
}

// Add responsive styles for mobile cards
function addMobileCardStyles() {
    if (!document.getElementById('mobile-preview-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-preview-styles';
        style.textContent = `
            @media (max-width: 768px) {
                .hospitalization-mobile-container,
                .vaccination-mobile-container {
                    padding: 0;
                }

                .hospitalization-card,
                .vaccination-card {
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    transition: box-shadow 0.2s ease;
                }

                .hospitalization-card:hover,
                .vaccination-card:hover {
                    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                }

                .vaccination-card .badge {
                    border-radius: 12px;
                    font-weight: 500;
                }

                .hospitalization-card h6,
                .vaccination-card h6 {
                    margin-bottom: 8px !important;
                }

                .hospitalization-card .row > div,
                .vaccination-card > div {
                    margin-bottom: 8px;
                }

                .hospitalization-card small,
                .vaccination-card small {
                    font-size: 11px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    font-weight: 600;
                }
            }

            .table-responsive {
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .badge {
                font-size: 11px;
                padding: 4px 8px;
                border-radius: 4px;
            }

            .badge-success {
                background-color: #28a745;
                color: white;
            }

            .badge-danger {
                background-color: #dc3545;
                color: white;
            }

            .badge-secondary {
                background-color: #6c757d;
                color: white;
            }
        `;
        document.head.appendChild(style);
    }
}

// Call this when the page loads or when rendering the preview
$(document).ready(function() {
    addMobileCardStyles();
});

function chunkArray(arr, size) {
    const result = [];
    for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
    }
    return result;
}

function admission_is_enabled(disabledFields, field) {
    return !disabledFields.includes(field);
}

function construct_previous_school_table(disabledFields, admissionPrevSchools) {
    var institute = '<?php echo $this->settings->getSetting('your_word_for_institute'); ?>';
    let html = `<div style="overflow-x: auto;"><table class="table table-bordered">
        <tr>`;

    // Header Row
    if (disabledFields.includes('year')) {
        html += `<th>Class</th><th>Year</th>`;
    } else {
        html += `<th>Year</th>`;
    }

    html += `<th>${institute || 'School'}</th>`;

    if (!disabledFields.includes('school_address')) html += `<th>Address</th>`;
    if (!disabledFields.includes('class')) html += `<th>Class</th>`;
    if (admission_is_enabled(disabledFields, 'board')) html += `<th>Board</th>`;
    if (admission_is_enabled(disabledFields, 'medium_of_instruction')) html += `<th>Medium of instruction</th>`;
    if (admission_is_enabled(disabledFields, 'type_of_school')) html += `<th>Type of School</th>`;
    if (admission_is_enabled(disabledFields, 'registration_no')) html += `<th>REGISTER NO/UNIQUE NO/ROLL NO</th>`;
    if (admission_is_enabled(disabledFields, 'expelled_or_suspended')) html += `<th>Expelled or suspended</th>`;
    if (admission_is_enabled(disabledFields, 'transfer_reason')) html += `<th>Transfer reason</th>`;
    if (admission_is_enabled(disabledFields, 'expelled_or_suspended_description')) html +=
        `<th>Expelled or suspended description</th>`;
    if (admission_is_enabled(disabledFields, 'previous_school_ratings')) html +=
        `<th>Previous ${institute || 'School'} ratings</th>`;
    if (admission_is_enabled(disabledFields, 'report_card')) html += `<th>Report card</th>`;

    html += `</tr>`;

    // Data Rows
    if (Array.isArray(admissionPrevSchools)) {
        admissionPrevSchools.forEach(sPrev => {
            html += `<tr>`;
            if (disabledFields.includes('year')) {
                html += `<td>${sPrev.class || '-'}</td><td>${sPrev.year || '-'}</td>`;
            } else {
                html += `<td>${sPrev.year || '-'}</td>`;
            }

            html += `<td>${sPrev.school_name || '-'}</td>`;

            if (!disabledFields.includes('school_address')) html += `<td>${sPrev.school_address || '-'}</td>`;
            if (!disabledFields.includes('class')) html += `<td>${sPrev.class || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'board')) {
                const boardText = sPrev.board === 'Other' ? `${sPrev.board}, ${sPrev.board_other}` : sPrev
                    .board;
                html += `<td>${boardText || '-'}</td>`;
            }

            if (admission_is_enabled(disabledFields, 'medium_of_instruction'))
                html += `<td>${sPrev.medium_of_instruction || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'type_of_school'))
                html += `<td>${sPrev.type_of_school || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'registration_no'))
                html += `<td>${sPrev.registration_no || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'expelled_or_suspended'))
                html += `<td>${sPrev.expelled_or_suspended || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'transfer_reason'))
                html += `<td>${sPrev.transfer_reason || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'expelled_or_suspended_description'))
                html += `<td>${sPrev.expelled_or_suspended_description || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'previous_school_ratings'))
                html += `<td>${sPrev.previous_school_ratings || '-'}</td>`;

            if (admission_is_enabled(disabledFields, 'report_card')) {
                if (sPrev.report_card) {
                    html +=
                        `<td>
                         <a href="${sPrev.report_card_view_url}" target="_blank" class="view-link" style="color:#623CE7; font-weight:500; font-size:1.1em; text-decoration:none;">View</a></td>`;
                } else {
                    html += `<td>-</td>`;
                }
            }


            html += `</tr>`;
        });
    } else {
        html += `<tr><td colspan="10" class="text-center">No records available</td></tr>`;
    }

    html += `</table></div>`;

    $('#previous-education-grid').html(html);
}

function constructSubjectDetailsTable(subjectArray, admission_prev_schools, showSubjectTotalMarks,
    showSubjectPercentage, showOverallTotalMarks, showOverallPercentage, preSchoolingMarks, combinations, comb,
    final_preview, subject_master, lang_selection) {
    let html = '';

    // Initialize subjectArray if undefined or not an array
    if (!subjectArray || !Array.isArray(subjectArray)) {
        console.warn('subjectArray is undefined or not an array, initializing as empty array');
        subjectArray = [];
    }

    // Add null/undefined checks for subjectArray
    if (subjectArray && Array.isArray(subjectArray) && subjectArray.length > 0 && admission_prev_schools) {
        html += `
        <table class="table table-bordered" style="margin: 0;">
            <tr>
                <th style="font-size: 14px;text-align:center">Subjects Details</th>
            </tr>
        </table>
        <div style="overflow-x: auto;">
        <table class="table table-bordered">`;

        if (Array.isArray(admission_prev_schools)) {
            admission_prev_schools.forEach((school, index) => {
                html += `<tr>`;
                html += `<th rowspan="${preSchoolingMarks == 1 ? 2 : 3}">${school.year}</th>`;
                html += `<th>Subjects</th>`;

                subjectArray.forEach(sub => {
                    let lang_name = '';
                    if (school.marks) {
                        school.marks.forEach(val => {
                            let jsan = JSON.parse(val.sub_name);
                            if (sub.id == jsan.sub_id) {
                                lang_name = jsan.name;
                            }
                        });
                    }
                   
                    html += '<th>';
                    // Ensure subject_name exists and handle undefined/null values
                    let subjectName = sub.subject_name || sub.name;
                    html += (sub.type === 'text' && lang_name) ? `${subjectName} - ${lang_name}` :
                        subjectName;
                    html += '</th>';
                });

                if (showOverallTotalMarks) html += '<th>Total Marks Obtained</th>';
                if (showOverallPercentage) html += '<th>Total Percentage</th>';

                html += '</tr><tr>';
                html += `<th>${showSubjectTotalMarks == 1 ? 'Marks' : 'Percentage'}</th>`;
                if (school.marks) {
                    subjectArray.forEach(sub => {
                    let found = false;
                    school.marks.forEach(val => {
                        const jsan = JSON.parse(val.sub_name);
                        if (sub.id == jsan.sub_id) {
                            found = true;
                            if (showSubjectTotalMarks == 1) {
                                let marksValue = (val.marks_scored !== null && val.marks_scored !== undefined && val.marks_scored !== '') ? val.marks_scored : '-';
                                html += `<td>${marksValue}</td>`;
                            } else if(showSubjectPercentage) {
                                let percentageValue = (val.percentage !== null && val.percentage !== undefined && val.percentage !== '') ? val.percentage : '-';
                                html += `<td>${val.percentage}</td>`;
                            }
                        }
                    });

                    if (!found) {
                        html += '<td>-</td>';
                    }
                });
                    if (showOverallTotalMarks) {
                        let totalMarks = (school.total_marks_scored !== null && school.total_marks_scored !== undefined && school.total_marks_scored !== '') ? school.total_marks_scored : '-';
                        html += `<td>${totalMarks}</td>`;
                    }
                    if (showOverallPercentage) {
                        let totalPercentage = (school.total_percentage !== null && school.total_percentage !== undefined && school.total_percentage !== '') ? school.total_percentage : '-';
                        html += `<td>${totalPercentage}</td>`;
                    }
                }
                html += '</tr>';

                if (preSchoolingMarks != 1) {
                    html += `<tr><th>Grade</th>`;
                    subjectArray.forEach(sub => {
                        let matched = (Array.isArray(school.marks) ? school.marks : []).find(val => {
                            let subData;
                            try {
                                subData = JSON.parse(val.sub_name);
                            } catch (e) {
                                return false;
                            }
                            return subData.sub_id == sub.id;
                        });

                        if (matched && showSubjectPercentage) {
                            html += `<td>${matched.grade || '-'}</td>`;
                        } else {
                            html += '<td>-</td>';
                        }
                    });
                    html += '</tr>';
                }
            });
        } else {
            html += `<tr><td colspan="10" class="text-center">No records available</td></tr>`;
        }

        html += `</table></div>`;
    }

    if (combinations) {
        html += `
        <table class="table table-bordered" id="details_schooling">
            <th colspan="2" style="background: #ccc; font-size: 14px;text-align:center">Subjects Offered</th>
            <tr>
                <th>${combinations.combination}</th>
                ${comb ? `<th>${comb}</th>` : ''}
            </tr>
        </table>`;
    }

    if (Array.isArray(lang_selection) && lang_selection.length > 0) {
        html += `<table class="table table-bordered" id="details_schooling">
            <th colspan="2" style=" font-size: 14px;text-align:center">Language selection</th>`;

        ['lang_1_choice', 'lang_2_choice', 'lang_3_choice'].forEach((lang, i) => {
            if (final_preview[lang]) {
                let label = ['I', 'II', 'III'][i];
                let subject = subject_master.find(sub => sub.id == final_preview[lang]);
                if (subject) {
                    html += `<tr><th>${label} Language: ${subject.subject_name}</th></tr>`;
                }
            }
        });

        html += '</table>';
    }

    $('#subject-details-grid').html(html);
}

function renderPhotosAndDocuments(previewData, admissionForm) {
    let html = '';
    const hasDocs = Array.isArray(admissionForm) && admissionForm.length > 0;
    html += `
    ${hasDocs ? generateDocumentsBlock(admissionForm) : "<p>No documents attached</p>"}`;
    $('#document-grid').html(html);
}

function generateDocumentsBlock(forms) {
    let docHTML = '';
    forms.forEach((form, idx) => {
        const label = form.document_type === 'Others' ? form.document_other : form.document_type;
        const document_path = form.document_path;

        // Get file extension and determine file type
        const fileExtension = document_path.split('.').pop().toLowerCase();
        const isPDF = fileExtension === 'pdf';
        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension);

        // Set icon properties based on file type
        let iconClass, iconText, iconColor;
        if (isPDF) {
            iconClass = 'pdf-icon';
            iconText = 'PDF';
            iconColor = '#FF2D2D';
        } else if (isImage) {
            iconClass = 'img-icon';
            iconText = 'IMG';
            iconColor = '#28a745';
        } else {
            iconClass = 'file-icon';
            iconText = 'FILE';
            iconColor = '#6c757d';
        }

        docHTML += `<div class="col-md-6">
            <div class="doc-label mb-1" style="font-size:12px;color:#A3ABC0">${label}</div>
                <div class="doc-card d-flex align-items-center justify-content-between p-2 rounded mb-3" style="background:#fff;">
                    <div class="d-flex align-items-center">
                        <div class="file-icon ${iconClass} d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:${iconColor}; border-radius:8px;">
                            <span style="color:#fff; font-weight:700; font-size:0.9em;">${iconText}</span>
                        </div>
                        <div>
                            <div class="file-name" style="font-weight:600; color:#181028;">${label}</div>
                        </div>
                    </div>
                    <a href="${document_path}" target="_blank" class="view-link" style="color:#623CE7; font-weight:500; font-size:1.1em; text-decoration:none;">View</a>
                </div>
            </div>
        `;
    });
    return docHTML;
}
</script>

<script>
function goToStep(stepNumber) {
    // Find all visible steps
    var $visibleSteps = $('.step:visible');
    // Find the index of the step to go to (1-based)
    var $targetStep = $visibleSteps.filter('[data-step="' + stepNumber + '"]');
    if ($targetStep.length === 0) {
        // If the target step is hidden, do nothing
        return;
    }
    // Hide all step forms
    $('.step-form').addClass('d-none');
    // Show the form for the target step
    $('#step-form-' + stepNumber).removeClass('d-none');
    // Remove all highlights
    $('.step').removeClass('active previous');
    // Add active to the target step
    $targetStep.addClass('active');
    // Add previous to all visible steps before the target
    $visibleSteps.each(function(idx, el) {
        if (parseInt($(el).attr('data-step')) < stepNumber) {
            $(el).addClass('previous');
        }
    });

    // Scroll to top of the page for desktop
    $('html, body').animate({
        scrollTop: 0
    }, 300);
}

function back_to_ToStep(stepNumber) {
    const currentStep = parseInt($('.step.active').attr('data-step'));
    if (stepNumber > currentStep) {
        return;
    }
    goToStep(stepNumber);
}

// Global function for checking student boarding and showing guardian details
function check_student_boarding_show_guardian_details_mobile() {
    var af_id = '<?php echo $final_preview->id ?>';
    $.ajax({
        url: '<?php echo site_url('admission_controller/check_student_boarding'); ?>',
        type: 'post',
        data: {
            'af_id': af_id
        },
        success: function(data) {
            if (data == 0) {
                // Student doesn't need boarding - hide guardian details and skip guardian step
                $('#guardian_details').hide();
                $('.step[data-step="3"]').hide(); // Hide guardian step from navigation

                // Skip to the next available step after guardian (usually medical or documents)
                var currentStep = 2; // Coming from parent details (step 3)
                var nextAvailableStep = findNextAvailableStep(currentStep + 1); // Skip guardian step (4)
                if(nextAvailableStep === 6){
                    goto_preview_page('parent-form');
                }
                if (nextAvailableStep) {
                    console.log('Mobile boarding check: No boarding needed, going to step', nextAvailableStep);
                    mobileGoToStep(nextAvailableStep);
                }

                get_parent_data();
                return 1;
            } else {
                // Student needs boarding - show guardian details and navigate to guardian step
                $('#guardian_details').show();
                $('.step[data-step="3"]').show(); // Show guardian step in navigation

                // Navigate to guardian step
                mobileGoToStep(3);

                get_parent_data();
                return 1;
            }
        },
        error: function() {
            console.log('Error checking boarding status');
            // Fallback to next available step
            var currentStep = 2;
            var nextStep = findNextAvailableStep(currentStep);
            if(nextStep === 6){
                goto_preview_page('parent-form');
            }
            if (nextStep) {
                mobileGoToStep(nextStep);
            }
        }
    });
}

function mobileSaveStep2() {

    // Mobile number validation - same as desktop
    var f_mobile_no = $("#f_mobile_no").val(),
        m_mobile_no = $("#m_mobile_no").val(),
        father_email_id = $("#f_email").val(),
        mother_email_id = $("#m_email").val();

    if (f_mobile_no && m_mobile_no && f_mobile_no === m_mobile_no) {
        // Show inline error messages
        showMobileDuplicateError();
        return false;
    } else {
        // Hide mobile duplicate error if validation passes
        hideMobileDuplicateError();
    }

    // Email validation - same as desktop
    if (father_email_id && mother_email_id && father_email_id === mother_email_id) {
        // Show inline error messages
        showEmailDuplicateError();
        return false;
    } else {
        // Hide email duplicate error if validation passes
        hideEmailDuplicateError();
    }

    var $form = $('#parent-form');

    if ($form.parsley().validate()) {

        var form = $('#parent-form')[0];
        var formData = new FormData(form);

        // Show loading state
        $('.save-step2.mobile-save-btn').text('Please wait...').prop('disabled', true);

        // Make AJAX call similar to the original save-step2
        $.ajax({
            url: '<?php echo site_url('admission_controller/update_parent_details'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            success: function(data) {
                $('.save-step2.mobile-save-btn').text('Save & Proceed').prop('disabled', false);

                if (data == 1) {
                    // Get configuration values - same as desktop
                    var document_form =
                        '<?php echo $this->settings->getSetting('disable_document_tab_in_admissions') ?>';
                    var medical_form =
                        '<?php echo $this->settings->getSetting('enabled_medical_form_tab_in_admissions') ?>';
                    var gaurdian_form = '<?php echo $config_val['show_guardian_details'] ?>';
                    var show_gaurdian_form_based_on_boarder =
                        '<?php echo $config_val['enable_guardian_details_based_on_boarder'] ?>';

                    const isGuardianEnabled = gaurdian_form === '1';
                    const isGuardianBasedOnBoarder = show_gaurdian_form_based_on_boarder === '1';
                    const isMedicalEnabled = medical_form === '1';
                    const isDocumentEnabled = document_form === '' || document_form === '0'; // 0 means enabled

                    // If guardian depends on boarding - use mobile-specific function
                    if (isGuardianEnabled && isGuardianBasedOnBoarder) {
                        console.log('Mobile Save Step 2: Checking boarding for guardian details');
                        check_student_boarding_show_guardian_details_mobile();
                        return;
                    }

                    // Use findNextAvailableStep to handle disabled steps properly
                    var currentStep = 2; // Parent form is step 3
                    var nextStep = findNextAvailableStep(currentStep);

                    // Update parent data before navigation
                    get_parent_data();

                    if (nextStep === 6) {
                        // If next step is preview (step 6), use goto_preview_page
                        goto_preview_page('parent-form');
                    } else {
                        // Navigate to the next available step
                        mobileGoToStep(nextStep);
                    }
                } else {
                    alert("Something went wrong, try again.");
                }
            },
            error: function() {
                $('.save-step2.mobile-save-btn').text('Save & Proceed').prop('disabled', false);
                alert("Something went wrong, try again.");
            }
        });
    } else {
        console.log('Parent form validation failed');
        alert('Please fill in all required fields correctly.');
    }
}

function makeSwalDraggable() {
    const swalContainer = $('.swal2-container');
    const swalPopup = $('.swal2-popup');

    if (swalPopup.length && !swalPopup.hasClass('draggable-initialized')) {
        swalPopup.addClass('draggable-initialized');

        // Add cursor style to title for drag indication
        $('.swal2-title, .swal2-header').css({
            'cursor': 'move',
            'user-select': 'none'
        });

        let isDragging = false;
        let startX, startY, initialX, initialY;

        // Mouse events
        swalPopup.on('mousedown', '.swal2-title, .swal2-header', function(e) {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            const rect = swalPopup[0].getBoundingClientRect();
            initialX = rect.left;
            initialY = rect.top;

            swalPopup.css('position', 'fixed');
            e.preventDefault();
        });

        $(document).on('mousemove', function(e) {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            const newX = initialX + deltaX;
            const newY = initialY + deltaY;

            // Boundary checks
            const maxX = window.innerWidth - swalPopup.outerWidth();
            const maxY = window.innerHeight - swalPopup.outerHeight();

            const boundedX = Math.max(0, Math.min(newX, maxX));
            const boundedY = Math.max(0, Math.min(newY, maxY));

            swalPopup.css({
                'left': boundedX + 'px',
                'top': boundedY + 'px',
                'transform': 'none'
            });
        });

        $(document).on('mouseup', function() {
            isDragging = false;
        });

        // Touch events for mobile
        swalPopup.on('touchstart', '.swal2-title, .swal2-header', function(e) {
            const touch = e.originalEvent.touches[0];
            isDragging = true;
            startX = touch.clientX;
            startY = touch.clientY;

            const rect = swalPopup[0].getBoundingClientRect();
            initialX = rect.left;
            initialY = rect.top;

            swalPopup.css('position', 'fixed');
            e.preventDefault();
        });

        $(document).on('touchmove', function(e) {
            if (!isDragging) return;

            const touch = e.originalEvent.touches[0];
            const deltaX = touch.clientX - startX;
            const deltaY = touch.clientY - startY;

            const newX = initialX + deltaX;
            const newY = initialY + deltaY;

            const maxX = window.innerWidth - swalPopup.outerWidth();
            const maxY = window.innerHeight - swalPopup.outerHeight();

            const boundedX = Math.max(0, Math.min(newX, maxX));
            const boundedY = Math.max(0, Math.min(newY, maxY));

            swalPopup.css({
                'left': boundedX + 'px',
                'top': boundedY + 'px',
                'transform': 'none'
            });
        });

        $(document).on('touchend', function() {
            isDragging = false;
        });
    }
}

// Override Swal.fire to automatically make all popups draggable
const originalSwalFire = Swal.fire;
Swal.fire = function(...args) {
    const result = originalSwalFire.apply(this, args);

    // Add draggable functionality after popup opens
    setTimeout(() => {
        makeSwalDraggable();
    }, 100);

    return result;
};

// Handle Acknowledgement Upload
function handleAcknowledgementUpload(input) {
    var file = input.files[0];
    if (file) {
        uploadDocumentToS3(file, 'acknowledgement_path', 'ack_progress_bar', 'ack_progress_percent',
            'ack_progress_area', 'acknowledgement_card', 'acknowledgement_file');
    }
}

// Handle Declaration Upload
function handledeclarationUpload(input) {
    var file = input.files[0];
    if (file) {
        uploadDocumentToS3(file, 'aadhar_declaration_path', 'declaration_progress_bar', 'declaration_progress_percent',
            'ack_progress_area', 'declaration_card', 'aadhar_declaration');
    }
}

// Handle PAN Document Upload
function handlePanDocumentUpload(input) {
    var file = input.files[0];
    if (file) {
        // Use null for progress elements since PAN template doesn't have them
        uploadDocumentToS3(file, 'pan_doc_path', null, null, null, 'pancard_doc_card', 'pancard_doc_file');
    }
}

// Handle PAN Declaration Upload
function handleDeclarationUpload(input) {
    var file = input.files[0];
    if (file) {
        uploadDocumentToS3(file, 'pan_declaration_path', 'pan_progress_bar', 'pan_progress_percent',
            'pan_progress_area', 'pan_declaration_card', 'pan_declaration');
    }
}

// Handle PAN Acknowledgement Upload
function handlepanAcknowledgementUpload(input) {
    var file = input.files[0];
    if (file) {
        // Use null for progress elements since PAN acknowledgement template doesn't have them
        uploadDocumentToS3(file, 'pan_acknowledgement_path', null, null, null, 'pan_acknowledgement_card',
            'pan_acknowledgement');
    }
}
function handleAadharDocumentUpload(input) {
    var file = input.files[0];
    if (file) {
        uploadDocumentToS3(file, 'aadhar_doc_path', 'aadhar_progress_bar', 'aadhar_progress_percent', 'aadhar_progress_area','aadhar_doc_card','aadhar_doc_file');
    }
}

// Upload Document to S3
function uploadDocumentToS3(file, pathInputId, progressBarId, progressPercentId, progressAreaId, cardId, fileInputId) {
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>' ;

    if (file && validate_documents_config_based(file)) {
        // Clear error messages for different card types
        if (cardId === 'aadhar_doc_card') {
            $('.aadhar_doc_card_error').html("");
        } else if (cardId === 'pancard_doc_card') {
            $('.pancard_doc_card_error').html("");
        } else if (cardId === 'pan_declaration_card') {
            $('.pan_declaration_card_error').html("");
        } else if (cardId === 'pan_acknowledgement_card') {
            $('.pan_acknowledgement_card_error').html("");
        } else if (cardId === 'declaration_card') {
            $('.declaration_card_error').html("");
        } else if (cardId === 'acknowledgement_card') {
            $('.acknowledgement_card_error').html("");
        } else {
            // Fallback for other card types
            $(`#${cardId}_error`).html("");
        }
    } else {
        var errorMessage = `Allowed file size exceeded. (Max.  ${max_size_string} Mb) / Allowed file types are jpeg, jpg and pdf`;

        // Display error messages for different card types
        if (cardId === 'aadhar_doc_card') {
            $('.aadhar_doc_card_error').html(errorMessage);
        } else if (cardId === 'pancard_doc_card') {
            $('.pancard_doc_card_error').html(errorMessage);
        } else if (cardId === 'pan_declaration_card') {
            $('.pan_declaration_card_error').html(errorMessage);
        } else if (cardId === 'pan_acknowledgement_card') {
            $('.pan_acknowledgement_card_error').html(errorMessage);
        } else if (cardId === 'declaration_card') {
            $('.declaration_card_error').html(errorMessage);
        } else if (cardId === 'acknowledgement_card') {
            $('.acknowledgement_card_error').html(errorMessage);
        } else {
            // Fallback for other card types
            $(`#${cardId}_error`).html(errorMessage);
        }

        console.log('File validation failed for', file.name);
        $('#' + fileInputId).val('');
        return;
    }
    // Clear any existing error messages for this upload card
    const uploadCard = document.getElementById(cardId);
    if (uploadCard) {
        const errorMsg = uploadCard.parentNode.querySelector('.error-message');
        if (errorMsg) {
            errorMsg.remove();
        }
        // Reset border color
        uploadCard.style.borderColor = '';
    }

    // Show progress area and reset progress
    if (progressAreaId && $('#' + progressAreaId).length) {
        $('#' + progressAreaId).show();
    }
    if (progressBarId && $('#' + progressBarId).length) {
        $('#' + progressBarId).css('width', '0%');
    }
    if (progressPercentId && $('#' + progressPercentId).length) {
        $('#' + progressPercentId).text('0%');
    }

    // Get file extension to determine icon
    var fileExtension = file.name.split('.').pop().toLowerCase();
    var fileIcon = 'PDF';
    var iconColor = '#FF2D2D';

    if (fileExtension === 'jpg' || fileExtension === 'jpeg' || fileExtension === 'png') {
        fileIcon = 'IMG';
        iconColor = '#4CAF50';
    }

    // Update the upload card to show file preview
    $('#' + cardId).html(`
        <div class="uploaded-file-card d-flex align-items-center justify-content-between p-3 rounded" style="background:#fff; border-radius:6px; border: 1px dashed #dcdcdc;">
            <div class="file-icon d-flex align-items-center justify-content-center me-3" style="width:48px; height:48px; background:${iconColor}; border-radius:10px;">
                <span style="color:#fff; font-weight:700; font-size:1.1em;">${fileIcon}</span>
            </div>
            <div class="flex-grow-1" style="min-width:0;">
                <div class="d-flex align-items-center justify-content-between" style="gap: 16px;">
                    <div class="file-name" style="font-weight:600; color:#181028; font-size:1em; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;">
                        ${file.name}
                    </div>
                    <div class="file-size text-muted" style="font-size: 1em; min-width:60px; text-align:right;">
                        ${(file.size / (1024 * 1024)).toFixed(2)} mb
                    </div>
                    <div id="delete_btn_${cardId}">
                        <button type="button" class="btn btn-link text-danger p-0 ms-2 d-flex align-items-center" onclick="deleteDeclarationFile('${fileInputId}','${pathInputId}')">
                            <span style="display: flex; align-items: center; width: 24px; height: 24px;">
                                <?php $this->load->view('svg_icons/delete_icon.svg') ?>
                            </span>
                        </button>
                    </div>
                </div>
                <div id="progress_area_${cardId}" class="d-flex align-items-center mt-2" style="gap:8px;">
                    <div style="flex:1;">
                        <div style="background:#edeafd; border-radius:6px; height:8px; width:100%;">
                            <div id="progress_bar_${cardId}" style="background:#6c63ff; height:8px; border-radius:6px; width:0%"></div>
                        </div>
                    </div>
                    <div id="progress_percent_${cardId}" style="min-width:32px; text-align:right; color:#181028; font-weight:500;">0%</div>
                </div>
            </div>
        </div>
    `);

    // Disable the file input during upload
    $('#' + fileInputId).prop('disabled', true);

    $.ajax({
        url: '<?php echo site_url("S3_admission_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'Admission_form_document'
        },
        success: function(response) {
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;

            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = Math.round((e.loaded / e.total) * 100);

                            // Update progress bar for declaration files
                            if (progressBarId && progressPercentId) {
                                $('#' + progressBarId).css('width', percentComplete + '%');
                                $('#' + progressPercentId).text(percentComplete + '%');
                            }

                            // Update progress bar for document cards (if cardId exists)
                            if (typeof cardId !== 'undefined') {
                                $('#progress_bar_' + cardId).css('width', percentComplete +
                                    '%');
                                $('#progress_percent_' + cardId).text(percentComplete +
                                    '%');
                            }
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    // Store the S3 path in hidden input
                    $('#' + pathInputId).val(path);

                    // Debug and hide progress area

                    if (typeof cardId !== 'undefined' && cardId) {
                        var progressAreaSelector = '#progress_area_' + cardId;

                        var progressElement = $(progressAreaSelector);

                        if (progressElement.length > 0) {
                            progressElement.hide();
                            progressElement.css('display', 'none !important');
                            progressElement.addClass('d-none');
                        }

                        // Also hide the percentage text element
                        var percentElement = $('#progress_percent_' + cardId);
                        if (percentElement.length > 0) {
                            percentElement.hide();
                            percentElement.css('display', 'none !important');
                            percentElement.addClass('d-none');
                        }

                        // Hide the progress bar element as well
                        var progressBarElement = $('#progress_bar_' + cardId);
                        if (progressBarElement.length > 0) {
                            progressBarElement.hide();
                            progressBarElement.css('display', 'none !important');
                            progressBarElement.addClass('d-none');
                        }

                        if (progressElement.length === 0) {

                            // Method 1: Try to find by partial ID match
                            $('[id*="progress_area"]').each(function() {
                                $(this).hide();
                                $(this).css('display', 'none !important');
                            });

                            // Method 2: Try specific known IDs for all progress elements
                            var knownProgressIds = [
                                '#progress_area_aadhar_doc_card',
                                '#aadhar_progress_area',
                                '#progress_area_' + cardId,
                                '#progress_percent_aadhar_doc_card',
                                '#progress_percent_' + cardId,
                                '#progress_bar_aadhar_doc_card',
                                '#progress_bar_' + cardId
                            ];

                            knownProgressIds.forEach(function(id) {
                                var element = $(id);
                                if (element.length > 0) {
                                    element.hide();
                                    element.css('display', 'none !important');
                                    element.addClass('d-none');
                                }
                            });

                            // Method 3: Hide by content and style
                            $('div').each(function() {
                                var $this = $(this);
                                var style = $this.attr('style') || '';
                                var id = $this.attr('id') || '';

                                if (style.includes('gap:8px') ||
                                    style.includes('background:#edeafd') ||
                                    id.includes('progress')) {
                                    console.log('Hiding progress element by style/content:', id);
                                    $this.hide();
                                }
                            });
                        }
                    } else {
                        console.log('cardId is undefined or empty');
                    }

                    // Additional timeout-based hiding attempt
                    setTimeout(function() {
                        console.log('=== TIMEOUT-BASED PROGRESS HIDING ===');

                        // Try all possible progress element selectors
                        var progressSelectors = [
                            '#progress_area_' + cardId,
                            '#progress_area_aadhar_doc_card',
                            '#aadhar_progress_area',
                            '#progress_percent_' + cardId,
                            '#progress_percent_aadhar_doc_card',
                            '#progress_bar_' + cardId,
                            '#progress_bar_aadhar_doc_card',
                            '[id*="progress_area"]',
                            '[id*="progress_percent"]',
                            '[id*="progress_bar"]',
                            '[style*="gap:8px"]',
                            '[style*="background:#edeafd"]',
                            '[style*="background:#6c63ff"]'
                        ];

                        progressSelectors.forEach(function(selector) {
                            var elements = $(selector);
                            if (elements.length > 0) {
                                console.log('Timeout hiding elements with selector:', selector, 'Count:', elements.length);
                                elements.hide();
                                elements.css('display', 'none !important');
                                elements.addClass('d-none');
                            }
                        });

                        // Force hide any remaining progress elements
                        $('.d-flex').each(function() {
                            var $this = $(this);
                            if ($this.find('[id*="progress_bar"]').length > 0 ||
                                $this.find('[id*="progress_percent"]').length > 0) {
                                $this.hide();
                            }
                        });

                        // Hide any element containing percentage text
                        $('*').each(function() {
                            var $this = $(this);
                            var text = $this.text().trim();
                            if (text === '100%' || text.includes('%')) {
                                // Check if this is likely a progress percentage (not other percentages)
                                var id = $this.attr('id') || '';
                                var parentId = $this.parent().attr('id') || '';

                                if (id.includes('progress') ||
                                    parentId.includes('progress') ||
                                    $this.siblings('[id*="progress"]').length > 0) {
                                    $this.hide();
                                    $this.css('display', 'none !important');
                                }
                            }
                        });

                    }, 1000);

                    // Complete progress bar
                    if (progressBarId && progressPercentId) {
                        $('#' + progressBarId).css('width', '100%');
                        $('#' + progressPercentId).text('100%');
                    }

                    // Complete progress bar for document cards
                    if (typeof cardId !== 'undefined') {
                        $('#progress_bar_' + cardId).css('width', '100%');
                        $('#progress_percent_' + cardId).text('100%');
                    }

                    // Hide progress area and show delete icon after a short delay
                    setTimeout(function() {
                        console.log('Progress completion - progressAreaId:',
                            progressAreaId, 'cardId:', cardId);

                        // Hide progress areas
                        if (progressAreaId) {
                            $('#' + progressAreaId).hide();
                            console.log('Hidden progressAreaId:', progressAreaId);
                        }

                        if (typeof cardId !== 'undefined' && cardId) {
                            var progressElement = $('#progress_area_' + cardId);
                            progressElement.hide();

                            // Show delete icon for document cards
                            var deleteElement = $('#delete_btn_' + cardId);
                            console.log('Delete element found:', deleteElement.length);
                            deleteElement.show();
                        } else {
                            console.log('cardId is undefined or empty');
                        }
                    }, 500);

                    // Re-enable file input
                    $('#' + fileInputId).prop('disabled', false);

                    console.log('File uploaded successfully: ' + path);
                },
                error: function(err) {
                    console.log('Upload error:', err);
                    if (progressAreaId) {
                        $('#' + progressAreaId).hide();
                    }
                    $('#' + fileInputId).prop('disabled', false);

                    // Show error message
                    alert('Upload failed. Please try again.');
                }
            });
        },
        error: function(err) {
            console.log('Signed URL error:', err);
            if (progressAreaId) {
                $('#' + progressAreaId).hide();
            }
            $('#' + fileInputId).prop('disabled', false);
            alert('Failed to get upload URL. Please try again.');
        }
    });
}

// Helper function to get the correct onchange handler based on file input ID
function getOnChangeHandler(fileInputId) {
    if (fileInputId.includes('acknowledgement')) {
        return 'handleAcknowledgementUpload(this)';
    } else if (fileInputId.includes('aadhar_doc_file')) {
        return 'handleAadharDocumentUpload(this)';
    } else if (fileInputId.includes('pancard_doc_file')) {
        return 'handlePanDocumentUpload(this)';
    } else if (fileInputId.includes('pan_declaration')) {
        return 'handleDeclarationUpload(this)';
    } else if (fileInputId.includes('pan_acknowledgement')) {
        return 'handlepanAcknowledgementUpload(this)';
    } else {
        // Default to declaration upload for aadhar_declaration and other cases
        return 'handledeclarationUpload(this)';
    }
}

// Delete Declaration File
function deleteDeclarationFile(fileInputId, pathInputId) {
    // Clear the file input and hidden input
    var max_size_string = '<?php echo $documents_size_in_admissions ;?>' ;
    $('#' + fileInputId).val('');
    $('#' + pathInputId).val('');

    // Reset the upload card to original state - map file input ID to card ID
    var cardId = '';
    if (fileInputId === 'aadhar_declaration') {
        cardId = 'declaration_card';
    } else if (fileInputId === 'acknowledgement_file') {
        cardId = 'acknowledgement_card';
    } else if (fileInputId === 'aadhar_doc_file') {
        cardId = 'aadhar_doc_card';
    } else if (fileInputId === 'pancard_doc_file') {
        cardId = 'pancard_doc_card';
    } else if (fileInputId === 'pan_declaration') {
        cardId = 'pan_declaration_card';
    } else if (fileInputId === 'pan_acknowledgement') {
        cardId = 'pan_acknowledgement_card';
    } else {
        // Fallback: try the original replacement logic
        cardId = fileInputId.replace('_file', '_card').replace('aadhar_declaration', 'declaration_card');
    }

    // Determine the appropriate error class based on cardId
    var errorClass = '';
    if (cardId === 'declaration_card') {
        errorClass = 'declaration_card_error';
    } else if (cardId === 'acknowledgement_card') {
        errorClass = 'acknowledgement_card_error';
    } else if (cardId === 'aadhar_doc_card') {
        errorClass = 'aadhar_doc_card_error';
    } else if (cardId === 'pancard_doc_card') {
        errorClass = 'pancard_doc_card_error';
    } else if (cardId === 'pan_declaration_card') {
        errorClass = 'pan_declaration_card_error';
    } else if (cardId === 'pan_acknowledgement_card') {
        errorClass = 'pan_acknowledgement_card_error';
    }

    $('#' + cardId).html(`
        <div style="max-height: 25px; max-width: 25px; margin: auto; display: flex; align-items: center; justify-content: center;">
            <?php $this->load->view('svg_icons/upload_document_icon.svg'); ?>
        </div>
        <div class="d-flex flex-column align-items-center mt-4">
            <i class="bi bi-upload" style="font-size: 24px;"></i>
            <span><strong style="color: #6c63ff;">Click to upload</strong></span>
            <small class="text-muted">JPG, JPEG, or PDF (max. ${max_size_string}MB)</small>
            ${errorClass ? `<small class="${errorClass} text-danger"></small>` : ''}
        </div>
        <input type="file" id="${fileInputId}" name="${$('#' + fileInputId).attr('name')}" class="remove-required" style="display: none;" accept="image/jpeg, application/pdf" onchange="${getOnChangeHandler(fileInputId)}" />
    `);

    // Reset card styling
    $('#' + cardId).css({
        'cursor': 'pointer',
        'background-color': '#fafafa',
        'border': '1px dashed #dcdcdc'
    });

}

// Store original required states for parent fields
var originalRequiredStates = {
    father: {},
    mother: {}
};

// Function to store original required states
function storeOriginalRequiredStates() {
    // Store father fields required states
    $('#father_details_tab input, #father_details_tab select, #father_details_tab textarea').each(function() {
        var fieldId = $(this).attr('id') || $(this).attr('name');
        if (fieldId) {
            originalRequiredStates.father[fieldId] = {
                required: $(this).attr('required') !== undefined,
                parsleyRequired: $(this).attr('data-parsley-required') !== undefined,
                parsleyMessage: $(this).attr('data-parsley-required-message') || '',
                parsleyType: $(this).attr('data-parsley-type') || '',
                parsleyMinlength: $(this).attr('data-parsley-minlength') || '',
                parsleyMaxlength: $(this).attr('data-parsley-maxlength') || ''
            };
        }
    });

    // Store mother fields required states
    $('#mother_details_tab input, #mother_details_tab select, #mother_details_tab textarea').each(function() {
        var fieldId = $(this).attr('id') || $(this).attr('name');
        if (fieldId) {
            originalRequiredStates.mother[fieldId] = {
                required: $(this).attr('required') !== undefined,
                parsleyRequired: $(this).attr('data-parsley-required') !== undefined,
                parsleyMessage: $(this).attr('data-parsley-required-message') || '',
                parsleyType: $(this).attr('data-parsley-type') || '',
                parsleyMinlength: $(this).attr('data-parsley-minlength') || '',
                parsleyMaxlength: $(this).attr('data-parsley-maxlength') || ''
            };
        }
    });
}

// Function to restore required validation for father fields
function restoreFatherRequiredValidation() {
    $('#father_details_tab input, #father_details_tab select, #father_details_tab textarea').each(function() {
        var fieldId = $(this).attr('id') || $(this).attr('name');
        if (fieldId && originalRequiredStates.father[fieldId]) {
            var originalState = originalRequiredStates.father[fieldId];

            if (originalState.required) {
                $(this).attr('required', true);
            }
            if (originalState.parsleyRequired) {
                $(this).attr('data-parsley-required', true);
            }
            if (originalState.parsleyMessage) {
                $(this).attr('data-parsley-required-message', originalState.parsleyMessage);
            }
            if (originalState.parsleyType) {
                $(this).attr('data-parsley-type', originalState.parsleyType);
            }
            if (originalState.parsleyMinlength) {
                $(this).attr('data-parsley-minlength', originalState.parsleyMinlength);
            }
            if (originalState.parsleyMaxlength) {
                $(this).attr('data-parsley-maxlength', originalState.parsleyMaxlength);
            }
        }
    });
}

// Function to restore required validation for mother fields
function restoreMotherRequiredValidation() {
    $('#mother_details_tab input, #mother_details_tab select, #mother_details_tab textarea').each(function() {
        var fieldId = $(this).attr('id') || $(this).attr('name');
        if (fieldId && originalRequiredStates.mother[fieldId]) {
            var originalState = originalRequiredStates.mother[fieldId];

            if (originalState.required) {
                $(this).attr('required', true);
            }
            if (originalState.parsleyRequired) {
                $(this).attr('data-parsley-required', true);
            }
            if (originalState.parsleyMessage) {
                $(this).attr('data-parsley-required-message', originalState.parsleyMessage);
            }
            if (originalState.parsleyType) {
                $(this).attr('data-parsley-type', originalState.parsleyType);
            }
            if (originalState.parsleyMinlength) {
                $(this).attr('data-parsley-minlength', originalState.parsleyMinlength);
            }
            if (originalState.parsleyMaxlength) {
                $(this).attr('data-parsley-maxlength', originalState.parsleyMaxlength);
            }
        }
    });
}

function fill_single_parent() {
    var single_parent = $('#single_parent').val();
    if(single_parent == 'No'){
        return false;
    }
    if (single_parent == 'Father') {
        // Clear and disable all mother section elements
        $('#mother_details_tab input, #mother_details_tab select, #mother_details_tab textarea').each(function() {
            var $element = $(this);
            var elementType = $element.prop('type') || $element.prop('tagName').toLowerCase();

            // Clear the field values first
            if (elementType === 'checkbox' || elementType === 'radio') {
                $element.prop('checked', false);
                $element.prop('disabled', true);
            } else if (elementType === 'file') {
                $element.val('');
                $element.prop('disabled', true);
            } else if (elementType === 'select' || elementType === 'select-one') {
                $element.val('').trigger('change');
                $element.prop('disabled', true);
            } else {
                $element.val('');
                $element.prop('readonly', true);
            }

            // Remove validation and errors
            $element.removeClass('parsley-error')
                   .removeAttr('required')
                   .removeAttr('data-parsley-required')
                   .removeAttr('data-parsley-required-message')
                   .removeAttr('data-parsley-type')
                   .removeAttr('data-parsley-minlength')
                   .removeAttr('data-parsley-maxlength');
        });

        // Enable all father section elements
        $('#father_details_tab input, #father_details_tab select, #father_details_tab textarea').each(function() {
            var $element = $(this);
            var elementType = $element.prop('type') || $element.prop('tagName').toLowerCase();

            if (elementType === 'checkbox' || elementType === 'radio' || elementType === 'file') {
                $element.prop('disabled', false);
            } else if (elementType === 'select' || elementType === 'select-one') {
                $element.prop('disabled', false);
                // Don't clear existing values - preserve saved data
            } else {
                $element.prop('readonly', false);
                // Don't clear existing values - preserve saved data
            }
        });

        // Restore father validation and remove mother validation
        restoreFatherRequiredValidation();

        // Visual feedback
        $('#mother_details_tab').addClass('opacity-50');
        $('#father_details_tab').removeClass('opacity-50');

    } else if (single_parent == 'Mother') {
        // Clear and disable all father section elements
        $('#father_details_tab input, #father_details_tab select, #father_details_tab textarea').each(function() {
            var $element = $(this);
            var elementType = $element.prop('type') || $element.prop('tagName').toLowerCase();

            // Clear the field values first
            if (elementType === 'checkbox' || elementType === 'radio') {
                $element.prop('checked', false);
                $element.prop('disabled', true);
            } else if (elementType === 'file') {
                $element.val('');
                $element.prop('disabled', true);
            } else if (elementType === 'select' || elementType === 'select-one') {
                $element.val('').trigger('change');
                $element.prop('disabled', true);
            } else {
                $element.val('');
                $element.prop('readonly', true);
            }

            // Remove validation and errors
            $element.removeClass('parsley-error')
                   .removeAttr('required')
                   .removeAttr('data-parsley-required')
                   .removeAttr('data-parsley-required-message')
                   .removeAttr('data-parsley-type')
                   .removeAttr('data-parsley-minlength')
                   .removeAttr('data-parsley-maxlength');
        });

        // Enable all mother section elements
        $('#mother_details_tab input, #mother_details_tab select, #mother_details_tab textarea').each(function() {
            var $element = $(this);
            var elementType = $element.prop('type') || $element.prop('tagName').toLowerCase();

            if (elementType === 'checkbox' || elementType === 'radio' || elementType === 'file') {
                $element.prop('disabled', false);
            } else if (elementType === 'select' || elementType === 'select-one') {
                $element.prop('disabled', false);
                // Don't clear existing values - preserve saved data
            } else {
                $element.prop('readonly', false);
                // Don't clear existing values - preserve saved data
            }
        });

        // Restore mother validation and remove father validation
        restoreMotherRequiredValidation();

        // Visual feedback
        $('#father_details_tab').addClass('opacity-50');
        $('#mother_details_tab').removeClass('opacity-50');

    } else {
        // Enable all elements in both sections
        $('#father_details_tab input, #father_details_tab select, #father_details_tab textarea, #mother_details_tab input, #mother_details_tab select, #mother_details_tab textarea').each(function() {
            var $element = $(this);
            var elementType = $element.prop('type') || $element.prop('tagName').toLowerCase();

            if (elementType === 'checkbox' || elementType === 'radio' || elementType === 'file') {
                $element.prop('disabled', false);
            } else if (elementType === 'select' || elementType === 'select-one') {
                $element.prop('disabled', false);
                // Don't clear existing values - preserve saved data
            } else {
                $element.prop('readonly', false);
                // Don't clear existing values - preserve saved data
            }
        });

        // Restore both father and mother validation
        restoreFatherRequiredValidation();
        restoreMotherRequiredValidation();

        // Remove visual effects
        $('#father_details_tab').removeClass('opacity-50');
        $('#mother_details_tab').removeClass('opacity-50');
    }
}

// Helper functions to show/hide inline error messages
function showMobileDuplicateError() {
    $('#f_mobile_duplicate_error').show();
    $('#m_mobile_duplicate_error').show();
    $('#f_mobile_no').addClass('parsley-error');
    $('#m_mobile_no').addClass('parsley-error');
}

function hideMobileDuplicateError() {
    $('#f_mobile_duplicate_error').hide();
    $('#m_mobile_duplicate_error').hide();
    $('#f_mobile_no').removeClass('parsley-error');
    $('#m_mobile_no').removeClass('parsley-error');
}

function showEmailDuplicateError() {
    $('#f_email_duplicate_error').show();
    $('#m_email_duplicate_error').show();
    $('#f_email').addClass('parsley-error');
    $('#m_email').addClass('parsley-error');
}

function hideEmailDuplicateError() {
    $('#f_email_duplicate_error').hide();
    $('#m_email_duplicate_error').hide();
    $('#f_email').removeClass('parsley-error');
    $('#m_email').removeClass('parsley-error');
}

// Function to check and hide duplicate errors when fields change
function checkAndHideDuplicateErrors() {
    var f_mobile_no = $("#f_mobile_no").val();
    var m_mobile_no = $("#m_mobile_no").val();
    var father_email_id = $("#f_email").val();
    var mother_email_id = $("#m_email").val();

    // Hide mobile error if numbers are different or empty
    if (!f_mobile_no || !m_mobile_no || f_mobile_no !== m_mobile_no) {
        hideMobileDuplicateError();
    }

    // Hide email error if emails are different or empty
    if (!father_email_id || !mother_email_id || father_email_id !== mother_email_id) {
        hideEmailDuplicateError();
    }
}

// Initialize the function on page load
$(document).ready(function() {
    // Store original required states when page loads
    storeOriginalRequiredStates();

    // Check if single parent is already selected and apply the logic
    var currentSingleParent = $('#single_parent').val();
    if (currentSingleParent) {
        fill_single_parent();
    }

    // Add event listeners to hide duplicate errors when fields change
    $('#f_mobile_no, #m_mobile_no').on('input keyup change blur', function() {
        checkAndHideDuplicateErrors();
    });

    $('#f_email, #m_email').on('input keyup change blur', function() {
        checkAndHideDuplicateErrors();
    });
});
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>