<!-- Mobile Circulars UI -->
<div class="mobile-container">
    <!-- Header -->
    <div class="mobile-header">
        <button class="back-btn" onclick="window.history.back()">
            <i class="fa fa-chevron-left"></i>
        </button>
        <div class="header-title-centered">Circulars</div>
    </div>

    <!-- Tab Navigation - Exact match to your design -->
    <div class="tab-bar">
        <button class="tab-item active" data-filter="all" onclick="switchTab(this, 'all')">
            All
        </button>
        <button class="tab-item" data-filter="read" onclick="switchTab(this, 'read')">
            Read
        </button>
        <button class="tab-item" data-filter="unread" onclick="switchTab(this, 'unread')">
            Unread
        </button>
    </div>


    <!-- Circulars List -->
    <div class="circulars-list" id="circularsList">
        <div class="loading-spinner" id="loadingSpinner">
            <i class="fa fa-spinner fa-spin"></i>
        </div>
    </div>

    <!-- Hidden inputs for existing functionality -->
    <input type="hidden" id="from_date" name="from_date" />
    <input type="hidden" id="to_date" name="to_date" />
    <input type="hidden" id="category" value="all" />
    <input type="hidden" id="show_unread" value="0" />
    <input type="hidden" id="circular_count" />
</div>

<!-- Form for viewing circulars - matches reference file -->
<form method="post" id="view-form" action="<?php echo site_url('parent/circular_inbox/view') ?>">
    <input type="hidden" name="circular_id" id="circular_id">
    <input type="hidden" name="parent_circular_id" id="parent_circular_id">
    <input type="hidden" name="is_read" id="is_read">
    <input type="hidden" name="category" id="category">
</form>

<!-- Bottom Bar - Exact match to your design -->
<div class="bottom-bar">
    <button class="bottom-btn" onclick="toggleSort()">
        <i class="fa fa-sort bottom-icon"></i>
        <span class="bottom-text">Sort</span>
    </button>
    <button class="bottom-btn" onclick="toggleFilters()">
        <i class="fa fa-filter bottom-icon"></i>
        <span class="bottom-text">Filter</span>
        <span class="filter-badge">2</span>
    </button>
</div>

<!-- Filter Modal - Swipeable -->
<div class="filter-modal" id="filterModal">
    <div class="filter-backdrop" onclick="closeFilterModal()"></div>
    <div class="filter-sheet">
        <!-- Handle bar for swipe -->
        <div class="filter-handle"></div>

        <!-- Filter Header -->
        <div class="filter-header">
            <h3 class="filter-title">Filter</h3>
        </div>

        <!-- Filter Content -->
        <div class="filter-content">
            <!-- Categories Section -->
            <div class="filter-section">
                <div class="filter-row" onclick="toggleCategoriesExpand()">
                    <span class="filter-label">Categories</span>
                    <div class="filter-row-right">
                        <div class="category-indicator"></div>
                        <i class="fa fa-chevron-right filter-arrow" id="categories-arrow"></i>
                    </div>
                </div>

                <!-- Categories Expanded Section -->
                <div class="categories-expanded" id="categoriesExpanded" style="display: none;">
                    <div class="categories-pills">
                        <?php if (!empty($categories)): ?>
                            <?php foreach ($categories as $key => $cat): ?>
                                <div class="category-pill" data-category="<?php echo $cat->name; ?>">
                                    <span><?php echo $cat->name; ?></span>
                                    <i class="fa fa-times"></i>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="category-pill active" data-category="Events">
                                <span>Events</span>
                                <i class="fa fa-times"></i>
                            </div>
                            <div class="category-pill active" data-category="Time Table">
                                <span>Time Table</span>
                                <i class="fa fa-times"></i>
                            </div>
                            <div class="category-pill active" data-category="Transport">
                                <span>Transport</span>
                                <i class="fa fa-times"></i>
                            </div>
                            <div class="category-pill-inactive" data-category="Competition">
                                <span>Competition</span>
                            </div>
                            <div class="category-pill-inactive" data-category="Food">
                                <span>Food</span>
                            </div>
                            <div class="category-pill-inactive" data-category="Student Info">
                                <span>Student Info</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Duration Section -->
            <div class="filter-section">
                <div class="filter-row" onclick="toggleDurationExpand()">
                    <span class="filter-label">Duration</span>
                    <div class="filter-row-right">
                        <i class="fa fa-chevron-down filter-arrow" id="duration-arrow"></i>
                    </div>
                </div>

                <!-- Duration Expanded Section -->
                <div class="duration-expanded" id="durationExpanded" style="display: none;">
                    <div class="duration-info">
                        <span class="duration-range">From 1 Aug 25 to 31 Aug 25</span>
                    </div>

                    <div class="duration-pills">
                        <div class="duration-pill" data-duration="today">Today</div>
                        <div class="duration-pill" data-duration="yesterday">Yesterday</div>
                        <div class="duration-pill" data-duration="last7">Last 7 Days</div>
                        <div class="duration-pill" data-duration="last30">Last 30 Days</div>
                        <div class="duration-pill active" data-duration="custom">Custom Date Range</div>
                    </div>

                    <!-- Custom Date Range Section -->
                    <div class="custom-date-section" id="customDateSection">
                        <div class="custom-date-header">Custom Date Range</div>
                        <div class="date-inputs">
                            <div class="date-input-group">
                                <label>From Date</label>
                                <input type="text" class="date-input" id="fromDateInput" value="1 Aug 25" readonly>
                            </div>
                            <div class="date-input-group">
                                <label>To Date</label>
                                <input type="text" class="date-input" id="toDateInput" value="10 Aug 25" readonly>
                            </div>
                        </div>

                        <!-- Calendar -->
                        <div class="calendar-container">
                            <div class="calendar-header">
                                <i class="fa fa-chevron-left" onclick="previousMonth()"></i>
                                <span class="calendar-month" id="calendarMonth">Sep 2025</span>
                                <i class="fa fa-chevron-right" onclick="nextMonth()"></i>
                            </div>
                            <div class="calendar-grid">
                                <div class="calendar-days">
                                    <div class="day-header">Sun</div>
                                    <div class="day-header">Mon</div>
                                    <div class="day-header">Tue</div>
                                    <div class="day-header">Wed</div>
                                    <div class="day-header">Thu</div>
                                    <div class="day-header">Fri</div>
                                    <div class="day-header">Sat</div>
                                </div>
                                <div class="calendar-dates" id="calendarDates">
                                    <!-- Calendar dates will be generated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Actions -->
        <div class="filter-actions">
            <button class="apply-filter-btn" onclick="applyFilterModal()">Apply Filter</button>
            <button class="reset-filter-btn" onclick="resetFilters()">Reset Filter</button>
        </div>
    </div>
</div>

<!-- Modal for Circular Details -->
<div class="modal fade" id="circularModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-fullscreen-sm-down" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="circularTitle"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="circularContent">
                <!-- Content will be loaded here -->
            </div>
            <div id="circularFiles"></div>
        </div>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script>
<?php if ($is_deactivated != 1) { ?>
// Global variables
let currentFilter = 'all';
let currentCategory = 'all';
let allCirculars = [];
let filteredCirculars = [];

// Category colors and special categories from PHP
const categoryColors = <?php echo !empty($cat_colors) ? json_encode($cat_colors, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT) : '{}'; ?>;
const specialCategories = <?php echo !empty($special_categories) ? json_encode($special_categories) : '[]'; ?>;

// Initialize on document ready
$(document).ready(function() {
    // Set default date range (last 30 days)
    let defaultStart = moment().subtract(29, 'days');
    let defaultEnd = moment();

    $('#from_date').val(defaultStart.format('DD-MM-YYYY'));
    $('#to_date').val(defaultEnd.format('DD-MM-YYYY'));

    // Initialize filter values
    currentFilter = 'all';
    currentCategory = 'all';

    console.log('Initializing with:', {
        currentFilter: currentFilter,
        currentCategory: currentCategory,
        from_date: $('#from_date').val(),
        to_date: $('#to_date').val()
    });

    // Load real data immediately
    loadCirculars();
});

// Load circulars with dynamic data - Load ALL data without filters initially
function loadCirculars() {
    // Show loading spinner
    $('#loadingSpinner').show();

    $.ajax({
        url: '<?php echo site_url('parent/circular_inbox/getCirculars'); ?>',
        type: 'POST',
        dataType: 'json',
        data: {
            category: 'all',
            from_date: $('#from_date').val(),
            to_date: $('#to_date').val(),
            show_unread: 0,  // Load all data (both read and unread)
            parent_id: <?php echo $parent_id; ?>
        },
        success: function(response) {
            $('#loadingSpinner').hide();
            console.log('API Response received:', response);
            allCirculars = response;
            console.log('About to call applyFilters...');
            applyFilters();
            console.log('Loaded ALL circulars:', response.length);
        },
        error: function(xhr, status, error) {
            $('#loadingSpinner').hide();
            console.error('Error loading circulars:', error);
            showError('Failed to load circulars');
        }
    });
}

// Load circulars with specific filters (for tab switching)
function loadCircularsWithFilter() {
    // Show loading spinner
    $('#loadingSpinner').show();

    var category = currentCategory === 'all' ? 'all' : currentCategory;
    var show_unread = currentFilter === 'unread' ? 1 : 0;

    $.ajax({
        url: '<?php echo site_url('parent/circular_inbox/getCirculars'); ?>',
        type: 'POST',
        dataType: 'json',
        data: {
            category: category,
            from_date: $('#from_date').val(),
            to_date: $('#to_date').val(),
            show_unread: show_unread,
            parent_id: <?php echo $parent_id; ?>
        },
        success: function(response) {
            $('#loadingSpinner').hide();
            allCirculars = response;
            applyFilters();
            console.log('Loaded filtered circulars:', response.length);
        },
        error: function(xhr, status, error) {
            $('#loadingSpinner').hide();
            console.error('Error loading circulars:', error);
            showError('Failed to load circulars');
        }
    });
}

// Reload data when filters change
function reloadData() {
    loadCircularsWithFilter();
}

// Apply current filters
function applyFilters() {
    console.log('Applying filters - Total circulars:', allCirculars.length);
    console.log('Current filter:', currentFilter);
    console.log('Current category:', currentCategory);

    filteredCirculars = allCirculars.filter(circular => {
        // Filter by read/unread status
        if (currentFilter === 'read' && circular.is_read == 0) return false;
        if (currentFilter === 'unread' && circular.is_read == 1) return false;

        // Filter by category
        if (currentCategory !== 'all' && circular.category !== currentCategory) return false;

        return true;
    });

    console.log('Filtered circulars:', filteredCirculars.length);
    renderCirculars();
}

// Render circulars in mobile UI - Fixed to match reference file format
function renderCirculars() {
    alert('renderCirculars');
    const container = $('#circularsList');

    console.log('Rendering circulars - Count:', filteredCirculars.length);

    if (filteredCirculars.length === 0) {
        container.html('<div class="no-data-display">No Circulars/Emails</div>');
        console.log('No circulars to display');
        return;
    }

    let html = '';
    let unreadCount = 0;

    filteredCirculars.forEach(function(circular) {
        var important_msg = Array.isArray(specialCategories) && specialCategories.includes(circular.category) ? 'imp_teach' : '';
        let isRead = circular.is_read == 0;
        var read_class = circular.is_read == 0 ? 'unread' : 'read';
        var title = circular.title.length > 30 ? circular.title.substr(0, 30) + '...' : circular.title;
        var date = '';
        var sentDate = moment(circular.sent_on);

        // Format date exactly like reference file
        if (sentDate.isSame(moment(), 'day')) {
            date = sentDate.format('h:mm a');
        } else if (sentDate.isSame(moment().subtract(1, 'days'), 'day')) {
            date = 'Yesterday';
        } else {
            date = sentDate.format('MMM D');
        }

        if (isRead) {
            unreadCount++;
        }

        var backgroundColor = categoryColors && typeof categoryColors == 'object' ? (categoryColors[circular.category] || '#d34140') : '#d34140';

        html += `
            <div id="${circular.id}" class="circular-panel-xs ${read_class} ${important_msg}" onclick="viewCircular(${circular.id}, ${circular.is_read}, ${circular.parent_circular_id}, '${circular.category}')">
                <div class="col-12" style="padding: 0px 8px 0px 13px;">
                    <div class="row" style="margin-bottom: 8px;">
                        <div class="col-6" style="padding-left: 0px;">
                            <div class="new_bk" style="background-color: ${backgroundColor}">
                                <span>${circular.category.replace(/\\'/g, "'")}</span>
                            </div>
                        </div>
                        <div class="col-6" style="padding-right: 0px;">
                            <span class="float-right abc" style="position: relative;right: 5px; padding-left: 10px;color: #737373;">
                                ${circular.file_path ? '<span style="font-size: 18px;padding-right: 5px;"><i class="fa fa-paperclip" aria-hidden="true"></i></span>' : ''}
                                ${date}
                            </span>
                        </div>
                    </div>
                    <span><strong>${title}</strong></span>
                </div>
            </div>
        `;
    });

    container.html(html);
    $('#circular_count').val(unreadCount);

    // Update filter badge count
    updateFilterBadge(unreadCount);
}

// Update filter badge count
function updateFilterBadge(unreadCount) {
    const badge = $('.filter-badge');
    if (unreadCount > 0) {
        badge.text(unreadCount).show();
    } else {
        badge.hide();
    }
}

// Get category icon
function getCategoryIcon(category) {
    const icons = {
        'Events': '📅',
        'Time Table': '⏰',
        'Transport': '🚌',
        'Competition': '🏆',
        'Fees': '💰',
        'Student Info': '📚'
    };
    return icons[category] || '📄';
}

// Tab switching - Updated for new design
function switchTab(element, filter) {
    $('.tab-item').removeClass('active');
    $(element).addClass('active');

    currentFilter = filter;
    $('#show_unread').val(filter === 'unread' ? '1' : '0');

    // Reload data with new filter
    loadCircularsWithFilter();
}

// Toggle filter modal - Updated for new design
function toggleFilters() {
    const modal = $('#filterModal');
    if (modal.hasClass('show')) {
        closeFilterModal();
    } else {
        openFilterModal();
    }
}

// Open filter modal
function openFilterModal() {
    const modal = $('#filterModal');
    modal.addClass('show');

    // Add swipe functionality
    addSwipeGestures();
}

// Close filter modal
function closeFilterModal() {
    const modal = $('#filterModal');
    modal.removeClass('show');
}

// Add swipe gestures for mobile
function addSwipeGestures() {
    const sheet = $('.filter-sheet')[0];
    const handle = $('.filter-handle')[0];
    let startY = 0;
    let currentY = 0;
    let isDragging = false;

    // Touch events for swipe down to close
    sheet.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
        isDragging = true;
    });

    sheet.addEventListener('touchmove', function(e) {
        if (!isDragging) return;

        currentY = e.touches[0].clientY;
        const deltaY = currentY - startY;

        if (deltaY > 0) {
            sheet.style.transform = `translateY(${deltaY}px)`;
        }
    });

    sheet.addEventListener('touchend', function(e) {
        if (!isDragging) return;
        isDragging = false;

        const deltaY = currentY - startY;

        if (deltaY > 100) {
            closeFilterModal();
        } else {
            sheet.style.transform = 'translateY(0)';
        }
    });
}

// Toggle categories expand
function toggleCategoriesExpand() {
    console.log('Categories expand clicked');
    // Implementation for categories expansion
}

// Toggle duration expand
function toggleDurationExpand() {
    console.log('Duration expand clicked');
    // Implementation for duration expansion
}

// Apply filters from modal
function applyFilterModal() {
    console.log('Apply filters clicked');
    closeFilterModal();
    // Implementation for applying filters
}

// Reset filters
function resetFilters() {
    console.log('Reset filters clicked');
    // Implementation for resetting filters
}

// Filter by category
function filterByCategory(element, category) {
    $('.filter-tag').removeClass('active');
    $(element).addClass('active');

    currentCategory = category;
    $('#category').val(category);

    applyFilters();
}

// Toggle sort (placeholder)
function toggleSort() {
    console.log('Sort functionality to be implemented');
}

// Toggle categories expansion
function toggleCategoriesExpand() {
    const expanded = $('#categoriesExpanded');
    const arrow = $('#categories-arrow');

    if (expanded.is(':visible')) {
        expanded.slideUp(300);
        arrow.removeClass('fa-chevron-down').addClass('fa-chevron-right');
    } else {
        expanded.slideDown(300);
        arrow.removeClass('fa-chevron-right').addClass('fa-chevron-down');
    }
}

// Toggle duration expansion
function toggleDurationExpand() {
    const expanded = $('#durationExpanded');
    const arrow = $('#duration-arrow');

    if (expanded.is(':visible')) {
        expanded.slideUp(300);
        arrow.removeClass('fa-chevron-up').addClass('fa-chevron-down');
    } else {
        expanded.slideDown(300);
        arrow.removeClass('fa-chevron-down').addClass('fa-chevron-up');

        // Show custom date section since it's active by default
        $('#customDateSection').show();

        // Generate calendar after a short delay to ensure DOM is ready
        setTimeout(function() {
            generateCalendar();
        }, 350);
    }
}

// Handle duration pill clicks
$(document).on('click', '.duration-pill', function() {
    $('.duration-pill').removeClass('active');
    $(this).addClass('active');

    const duration = $(this).data('duration');
    if (duration === 'custom') {
        $('#customDateSection').show();
        // Generate calendar when custom date range is selected
        setTimeout(function() {
            generateCalendar();
        }, 100);
    } else {
        $('#customDateSection').hide();
    }
});

// Generate calendar
function generateCalendar() {
    console.log('Generating calendar...');
    const calendarDates = $('#calendarDates');
    console.log('Calendar container found:', calendarDates.length > 0);

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    console.log('Current date:', currentDate, 'Year:', year, 'Month:', month);

    // Clear existing dates
    calendarDates.empty();

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Add empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
        calendarDates.append('<div class="calendar-date other-month"></div>');
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const isToday = day === currentDate.getDate() &&
                       month === currentDate.getMonth() &&
                       year === currentDate.getFullYear();

        const isSelected = day === 8; // Example: 8th is selected

        let classes = 'calendar-date';
        if (isToday) classes += ' today';
        if (isSelected) classes += ' selected';

        calendarDates.append(`<div class="${classes}" data-date="${day}">${day}</div>`);
    }

    console.log('Calendar generated with', daysInMonth, 'days');
    console.log('Calendar HTML:', calendarDates.html());
}

// Calendar navigation
function previousMonth() {
    console.log('Previous month clicked');
    // Implementation for previous month
}

function nextMonth() {
    console.log('Next month clicked');
    // Implementation for next month
}

// Handle calendar date clicks
$(document).on('click', '.calendar-date', function() {
    if (!$(this).hasClass('other-month')) {
        $('.calendar-date').removeClass('selected');
        $(this).addClass('selected');

        const selectedDate = $(this).data('date');
        console.log('Selected date:', selectedDate);
    }
});

// Open circular details - FIXED: proper file_path handling
function openCircular(circularId, parentCircularId) {
    $('#circularContent').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i></div>');
    $('#circularTitle').html('');

    $.ajax({
        url: '<?php echo site_url('parent/Circular_inbox/get_circular_details'); ?>',
        type: 'POST',
        data: {
            circularId: circularId,
            parent_circular_id: parentCircularId
        },
        dataType: 'html',
        success: function(data) {
            const previewData = $.parseJSON(data);
            $('#circularTitle').html(previewData.title);
            $('#circularContent').html(previewData.body);

            // Handle file attachments - FIXED: proper type checking
            let fileHtml = '';
            if (previewData.file_path) {
                // Check if file_path is an array
                if (Array.isArray(previewData.file_path)) {
                    previewData.file_path.forEach(function(file) {
                        fileHtml += `
                            <button class="btn btn-primary btn-sm m-1"
                                    onclick="downloadFile(this)"
                                    data-file-path="${file.file_path}"
                                    data-file-name="${file.file_name}">
                                <i class="fa fa-download"></i> ${file.file_name}
                            </button>
                        `;
                    });
                } else if (typeof previewData.file_path === 'string' && previewData.file_path.trim() !== '') {
                    // Handle single file path as string
                    const fileName = previewData.file_name || 'Download File';
                    fileHtml += `
                        <button class="btn btn-primary btn-sm m-1"
                                onclick="downloadFile(this)"
                                data-file-path="${previewData.file_path}"
                                data-file-name="${fileName}">
                            <i class="fa fa-download"></i> ${fileName}
                        </button>
                    `;
                } else if (typeof previewData.file_path === 'object' && previewData.file_path !== null) {
                    // Handle single file object
                    fileHtml += `
                        <button class="btn btn-primary btn-sm m-1"
                                onclick="downloadFile(this)"
                                data-file-path="${previewData.file_path.file_path || previewData.file_path}"
                                data-file-name="${previewData.file_path.file_name || 'Download File'}">
                            <i class="fa fa-download"></i> ${previewData.file_path.file_name || 'Download File'}
                        </button>
                    `;
                }
            }
            $('#circularFiles').html(fileHtml);

            // Mark as read
            markAsRead(parentCircularId);

            $('#circularModal').modal('show');
        },
        error: function() {
            alert('Failed to load circular details');
        }
    });
}

// Mark circular as read
function markAsRead(parentCircularId) {
    const circular = allCirculars.find(c => c.parent_circular_id == parentCircularId);
    if (circular && circular.is_read == 0) {
        circular.is_read = 1;

        // Update UI
        $(`.circular-item[onclick*="${parentCircularId}"]`).removeClass('unread');

        // Update unread count
        let currentCount = parseInt($('#circular_count').val()) || 0;
        if (currentCount > 0) {
            $('#circular_count').val(currentCount - 1);
        }
    }
}

// Download file function (from original)
function downloadFile(element) {
    var filePath = element.getAttribute('data-file-path');
    var fileName = element.getAttribute('data-file-name');
    const originalContent = element.innerHTML;
    element.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
    element.disabled = true;

    var xhr = new XMLHttpRequest();
    xhr.open('GET', filePath, true);
    xhr.responseType = 'blob';

    xhr.onload = function() {
        if (xhr.status === 200) {
            var blob = xhr.response;
            var link = document.createElement('a');
            var url = window.URL.createObjectURL(blob);

            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }
        element.innerHTML = originalContent;
        element.disabled = false;
    };

    xhr.onerror = function() {
        console.error('Network error while trying to download file.');
        element.innerHTML = originalContent;
        element.disabled = false;
    };

    xhr.send();
}

// Utility functions
function showLoading(show) {
    if (show) {
        $('#loadingSpinner').show();
        $('#circularsList').addClass('loading');
    } else {
        $('#loadingSpinner').hide();
        $('#circularsList').removeClass('loading');
    }
}

function showError(message) {
    $('#circularsList').html(`<div class="error-message">${message}</div>`);
}

// View circular function - matches reference file
function viewCircular(id, is_read, parent_circular_id, category) {
    $("#circular_id").val(id);
    $("#category").val(category);
    $("#parent_circular_id").val(parent_circular_id);
    $("#is_read").val(is_read);
    $("#view-form").submit();
}

<?php } ?>
</script>

<style>
/* Mobile Container */
.mobile-container {
    max-width: 100%;
    margin: 0 auto;
    background: #f8f9fa;
    min-height: 100vh;
    padding-bottom: 80px;
}

/* Header */
.mobile-header {
    background: #fff;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    position: relative;
}

.back-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #333;
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.back-btn:hover {
    background: #f0f0f0;
}

.header-title-centered {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* Tab Bar - Exact match to your design */
.tab-bar {
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0;
    border-bottom: 1px solid #e9ecef;
    position: relative;
}

.tab-item {
    flex: 1;
    background: none;
    border: none;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
    color: #6c757d;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border-bottom: 3px solid transparent;
}

.tab-item.active {
    color: #6366f1;
    border-bottom-color: #6366f1;
}

.tab-item:hover:not(.active) {
    color: #495057;
}

/* Filter Tags */
.filter-tags {
    background: #fff;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
}

.tag-scroll {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.25rem;
}

.tag-scroll::-webkit-scrollbar {
    height: 3px;
}

.tag-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.tag-scroll::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

.filter-tag {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    color: #666;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s;
}

.filter-tag.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.filter-tag:hover:not(.active) {
    background: #e9ecef;
}

.tag-icon {
    font-size: 0.9rem;
}

/* Circulars List */
.circulars-list {
    padding: 1rem;
    position: relative;
}

.circulars-list.loading {
    min-height: 200px;
}

/* Circular Cards - Exact match to your design */
.circular-card {
    background: #f8f9fa;
    border-radius: 16px;
    margin-bottom: 0.75rem;
    padding: 1rem;
    display: flex;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    border: none;
    box-shadow: none;
}

.circular-card:hover {
    background: #f0f1f3;
}

.circular-card.selected {
    background: #EFECFD;
}

.circular-card.unread {
    background: #EFECFD;
}

.card-date {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.date-number {
    font-size: 1rem;
    line-height: 1;
    font-weight: 700;
}

.date-month {
    font-size: 0.65rem;
    text-transform: uppercase;
    line-height: 1;
    font-weight: 500;
    margin-top: 1px;
}

.card-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.card-title {
    font-size: 0.95rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-category {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    margin-top: auto;
}

.category-icon {
    font-size: 0.8rem;
    color: #6c757d;
}

.category-text {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

/* Loading and States */
.loading-spinner {
    text-align: center;
    padding: 2rem;
    color: #666;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.loading-spinner i {
    font-size: 2rem;
}

.no-data, .no-data-display {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
    font-size: 1rem;
}

/* Reference file card styles */
.circular-panel-xs {
    padding: 15px;
    overflow-y: hidden;
    overflow-wrap: break-word;
    box-shadow: inset 0 0 15px #ffffff;
    margin: 3% 1%;
    background: #f8f9fa;
    border-radius: 16px;
    cursor: pointer;
}

.circular-panel-xs.unread {
    background: #EFECFD;
}

.circular-panel-xs.read {
    background: #e5e3e3;
    box-shadow: 0px 3px 8px #ccc !important;
    border-radius: 1.2rem !important;
    min-height: 105px !important;
    font-weight: 200;
    border-color: #ccc !important;
    color: #b5b5b5;
    border: none !important;
    padding: 5px 0px !important;
}

.new_bk {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.error-message {
    text-align: center;
    padding: 2rem;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 8px;
    margin: 1rem;
}

/* Modal Enhancements */
.modal-fullscreen-sm-down {
    margin: 0;
    max-width: 100%;
    height: 100%;
}

.modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: none;
    border-radius: 0;
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
}

/* Bottom Bar - Exact alignment match to your design */
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 16px 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 80px;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.bottom-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.bottom-btn:hover {
    opacity: 0.7;
}

.bottom-icon {
    font-size: 18px;
    color: #6c757d;
}

.bottom-text {
    font-size: 16px;
    color: #6c757d;
    font-weight: 400;
    margin: 0;
}

.filter-badge {
    background: #6366f1;
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 1px 6px;
    border-radius: 8px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 2px;
    position: relative;
    top: -2px;
}

/* Filter Modal - Swipeable Bottom Sheet */
.filter-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.filter-modal.show {
    display: block;
    opacity: 1;
}

.filter-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

.filter-sheet {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f8f9fa;
    border-radius: 20px 20px 0 0;
    min-height: 60vh;
    max-height: 90vh;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.filter-modal.show .filter-sheet {
    transform: translateY(0);
}

.filter-handle {
    width: 40px;
    height: 4px;
    background: #d1d5db;
    border-radius: 2px;
    margin: 12px auto 8px;
    cursor: grab;
}

.filter-header {
    padding: 16px 24px 8px;
    border-bottom: 1px solid #e5e7eb;
    background: #f8f9fa;
}

.filter-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    text-align: center;
}

.filter-content {
    padding: 0 24px;
    flex: 1;
    overflow-y: auto;
}

.filter-section {
    border-bottom: 1px solid #e5e7eb;
}

.filter-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
    cursor: pointer;
    transition: background 0.2s;
}

.filter-row:hover {
    background: rgba(0, 0, 0, 0.02);
}

.filter-label {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
}

.filter-row-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.category-indicator {
    width: 12px;
    height: 12px;
    background: #6366f1;
    border-radius: 50%;
}

.filter-arrow {
    font-size: 14px;
    color: #9ca3af;
    transition: transform 0.2s;
}

.filter-actions {
    padding: 24px;
    background: #f8f9fa;
    border-top: 1px solid #e5e7eb;
}

/* Categories Expanded Styles */
.categories-expanded {
    padding: 1rem 0;
    border-top: 1px solid #f0f0f0;
}

.categories-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-pill {
    background: #6c5ce7;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.category-pill.active {
    background: #6c5ce7;
}

.category-pill i {
    font-size: 0.75rem;
}

.category-pill-inactive {
    background: #f8f9fa;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    border: 1px solid #e9ecef;
}

/* Duration Expanded Styles */
.duration-expanded {
    padding: 1rem 0;
    border-top: 1px solid #f0f0f0;
}

.duration-info {
    margin-bottom: 1rem;
}

.duration-range {
    color: #6c757d;
    font-size: 0.875rem;
}

.duration-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.duration-pill {
    background: #f8f9fa;
    color: #6c757d;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    border: 1px solid #e9ecef;
}

.duration-pill.active {
    background: #6c5ce7;
    color: white;
    border-color: #6c5ce7;
}

/* Custom Date Range Styles */
.custom-date-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.custom-date-header {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #374151;
}

.date-inputs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.date-input-group {
    flex: 1;
}

.date-input-group label {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.date-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #6c5ce7;
    border-radius: 8px;
    font-size: 0.875rem;
    background: #f8f9fa;
    color: #374151;
}

/* Calendar Styles */
.calendar-container {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.calendar-month {
    font-weight: 600;
    color: #374151;
}

.calendar-header i {
    cursor: pointer;
    padding: 0.5rem;
    color: #6c757d;
}

.calendar-header i:hover {
    color: #374151;
}

.calendar-grid {
    padding: 1rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.day-header {
    text-align: center;
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
    padding: 0.5rem;
}

.calendar-dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
}

.calendar-date {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.calendar-date:hover {
    background: #f3f4f6;
}

.calendar-date.selected {
    background: #6c5ce7;
    color: white;
}

.calendar-date.today {
    background: #e5e7eb;
    font-weight: 600;
}

.calendar-date.other-month {
    color: #d1d5db;
}

.apply-filter-btn {
    width: 100%;
    background: #6366f1;
    color: white;
    border: none;
    padding: 16px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    cursor: pointer;
    transition: background 0.2s;
}

.apply-filter-btn:hover {
    background: #5856eb;
}

.reset-filter-btn {
    width: 100%;
    background: none;
    color: #6366f1;
    border: none;
    padding: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.2s;
}

.reset-filter-btn:hover {
    opacity: 0.8;
}

/* Responsive Design */
@media (min-width: 576px) {
    .mobile-container {
        max-width: 480px;
        margin: 0 auto;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }

    .bottom-bar {
        max-width: 480px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 0;
    }

    .modal-fullscreen-sm-down {
        margin: 1.75rem auto;
        max-width: 500px;
        height: auto;
    }

    .modal-fullscreen-sm-down .modal-content {
        height: auto;
        border-radius: 0.5rem;
    }
}

@media (max-width: 575px) {
    .circular-card {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .card-date {
        width: 44px;
        height: 44px;
    }

    .date-number {
        font-size: 0.95rem;
    }

    .date-month {
        font-size: 0.6rem;
    }

    .card-title {
        font-size: 0.9rem;
    }

    .category-text {
        font-size: 0.75rem;
    }
}
</style>