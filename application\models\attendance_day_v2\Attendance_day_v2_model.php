<?php

class Attendance_day_v2_model extends CI_model {
    public function __construct() {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
    }



    public function getAllClassSection(){
        $show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
        $this->db_readonly->select('c.id as classID, cs.id as sectionID, c.class_name as class_name, cs.section_name as  section_name')
                          ->from('class c')
                          ->join('class_section  cs' ,'cs.class_id=c.id')
                          ->where('c.acad_year_id', $this->yearId)
                          ->where('c.is_placeholder !=',1)
                          ->order_by('cs.display_order, cs.id');
        if(!$show_placeholder_sections) {
          $this->db_readonly->where('cs.is_placeholder !=', 1);
        }
          $result = $this->db_readonly->get()->result();
          return $result;
        }

        public function getSectionsByClassTeacher($staff_id) {
            return $this->db_readonly
                ->select('cs.class_id as classID, cs.id as sectionID, cs.class_name, cs.section_name')
                ->from('class c')
                ->join('class_section  cs' ,'cs.class_id=c.id')
                ->where('cs.class_teacher_id', $staff_id)
                ->order_by('cs.display_order, cs.id')
                ->where('c.acad_year_id', $this->yearId)
                ->or_where('cs.assistant_class_teacher_id', $staff_id)
                ->or_where('cs.assistant_class_teacher_2', $staff_id)
                ->get()->result();

        }
      public function aDDcalendarEvents(){
        $class_secID    =$this->input->post('classSec');
        $date           =$this->input->post('date');
        $event_name     =$this->input->post('event_name');
        list($class, $section) = explode('_', $class_secID);
        $insert = array(
          'class'       => $class,
          'section'     => $section,
          'date'        => $date,
          'event_name'  => $event_name,
      );
        $this->db->insert('attendance_std_v2_calendarevents', $insert);
        return 1;
      }

      public function getCalenderEvents(){
        return $this->db_readonly->select('ace.id,c.class_name, cs.section_name, DATE_FORMAT(ace.date, "%d-%b-%Y") as event_date, ace.event_name')
                                    ->from('attendance_std_v2_calendarevents ace')
                                    ->join('class c', 'c.id = ace.class')
                                    ->join('class_section cs', 'cs.id = ace.section')
                                    ->get()->result();

      }

      public function editevents(){
        $id=$_POST['id'];
       return $this->db->select('ace.id,c.id as classID, c.class_name, cs.id as sectionID, cs.section_name, DATE_FORMAT(ace.date, "%d-%b-%Y") as event_date, ace.event_name')
                        ->from('attendance_std_v2_calendarevents ace')
                        ->join('class c', 'c.id = ace.class')
                        ->join('class_section cs', 'cs.id = ace.section')
                        ->where('ace.id',$id)
                        ->get()->row();

      }

      public function deleteevents() {
        $id = $this->input->post('id');
        $this->db->where('id', $id);
        $result = $this->db->delete("attendance_std_v2_calendarevents");
        return $result;
    }

    public function aDDabsentreason(){
        $reason         =$this->input->post('reasons');
        $consider_as    =$this->input->post('consider_as');
        $insert = array(
          'reasons'         => $reason,
          'consider_as_present'     => $consider_as,
          );
          $this->db->insert('attendance_std_v2_absentreasons', $insert);
          return 1;
      }

      public function getabsentreason(){
        return $this->db_readonly->select('*')
                                 ->from('attendance_std_v2_absentreasons')
                                 ->get()->result();
      }

      public function editreason(){
        $id = $this->input->post('id');
        return $this->db->select('*')
                        ->from('attendance_std_v2_absentreasons')
                        ->where('id', $id)
                        ->get()->row();
      }

      public function updatereason(){
        $renid = $this->input->post('id');
        $data = array(
          'reasons'      => $this->input->post('reasons'),
          'consider_as_present'  => $this->input->post('consider_as')
        );
        return $this->db->where('id', $renid)->update('attendance_std_v2_absentreasons', $data);
      }

      public function deletereason(){
        $renid = $this->input->post('id');
        return $this->db->where('id', $renid)->delete('attendance_std_v2_absentreasons');
      }

      private function _student_data_for_attendance($class, $section){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as std_name";

        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as std_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(s.enrollment_number, 'NA'), ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as std_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(s.admission_no, 'NA'), ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as std_name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as std_name";
        } else {
          $std_name = "CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as std_name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 's.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 's.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 's.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }

        $this->db_readonly->select("s.id, $std_name, CONCAT(IFNULL(p.first_name, ''), ' ', IFNULL(p.last_name, '')) as fatherName, p.mobile_no as FatherNo,sy.high_quality_picture_url");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id = s.id');
        $this->db_readonly->join('student_relation sr', 'sr.std_id = s.id');
        $this->db_readonly->join('parent p', 'p.id = sr.relation_id');
        $this->db_readonly->where('s.admission_status', 2);
        $this->db_readonly->where('sr.relation_type', 'Father');
        $this->db_readonly->where('sy.promotion_status !=', 4);
        $this->db_readonly->where('sy.promotion_status !=', 5);
        if ($class) {
            $this->db_readonly->where('sy.class_id', $class);
        }
        if ($section) {
            $this->db_readonly->where('sy.class_section_id', $section);
        }
        $this->db_readonly->where('sy.acad_year_id', $this->yearId);
        $this->db_readonly->order_by($order_by);
        return $this->db_readonly->get()->result();

      }

      // Get students who are on leave for a specific date and section
      private function _get_students_on_leave($date, $section) {
        $this->db_readonly->select('ls.student_id, ls.leave_type, ls.reason, ls.from_date, ls.to_date, ls.noofdays');
        $this->db_readonly->from('leave_student ls');
        $this->db_readonly->join('student_admission sa', 'sa.id = ls.student_id');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id = sa.id');
        $this->db_readonly->where('sy.class_section_id', $section);
        $this->db_readonly->where('sy.acad_year_id', $this->yearId);
        $this->db_readonly->where('ls.acad_year_id', $this->yearId);
        $this->db_readonly->where('ls.status', 'Approved'); // Only approved leaves
        $this->db_readonly->where('ls.from_date <=', $date);
        $this->db_readonly->where('ls.to_date >=', $date);

        $result = $this->db_readonly->get()->result();

        // Create an associative array with student_id as key for easy lookup
        $leave_data = [];
        foreach ($result as $leave) {
            // Determine attendance status based on leave days
            $morning_status = 0; // Default absent
            $afternoon_status = 0; // Default absent

            if ($leave->noofdays == 0.5) {
                // Half day leave - mark morning as leave (absent) and afternoon as present
                $morning_status = 0; // Morning leave (absent)
                $afternoon_status = 1; // Afternoon present
            } else if ($leave->noofdays >= 1) {
                // Full day leave - mark both sessions as absent
                $morning_status = 0; // Morning absent
                $afternoon_status = 0; // Afternoon absent
            }

            $leave_data[$leave->student_id] = [
                'leave_type' => $leave->leave_type,
                'reason' => $leave->reason,
                'from_date' => $leave->from_date,
                'to_date' => $leave->to_date,
                'noofdays' => $leave->noofdays,
                'is_on_leave' => true,
                'morning_session_status' => $morning_status,
                'afternoon_session_status' => $afternoon_status
            ];
        }

        return $leave_data;
      }

      private function _get_taken_by_attendance($staff_id){
        $result= $this->db->select("CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as staff_name")
        ->from('staff_master s')
        ->where('id',$staff_id)
        ->get()->row();
        if(!empty($result)){
          return $result->staff_name;
        }else{
          return 'Admin';
        }
      }

      private function _get_atttendance_data($date,$class, $section){

        $session= $this->db_readonly->select("asd.id as sessionID, DATE_FORMAT(asd.att_taken_date, '%d-%b-%Y') as taken_date, asd.taken_by")
        ->from('attendance_std_day_v2_session asd')
        ->where('asd.att_taken_date',$date)
        ->where('asd.class_id', $class)
        ->where('asd.class_section_id',$section)
        ->get()->row();

        if(!empty($session)){
          $att_data= $this->db->select("asds.student_admission_id,asds.override_present as override,asds.absent_remarks as overide_remarks,asds.morning_session_status as morning,asds.afternoon_session_status as afternoon, asds.id as att_std_id, ifnull(asds.is_late,0) as is_late")
          ->from('attendance_std_day_v2_students asds')
          ->where('asds.attendance_day_v2_session_id',$session->sessionID)
          ->get()->result();
          $attArry=[];
          foreach ($att_data as $key => $value) {
            $attArry[$value->student_admission_id]=$value;
          }
          $absent= $this->db->select("sum(case when asds.morning_session_status = 0 then 1 else 0 end) AS morning_absent_count,sum(case when asds.afternoon_session_status = 0 then 1 else 0 end) AS afternoon_absent_count")
                            ->from('attendance_std_day_v2_students asds')
                            ->join('attendance_std_day_v2_session asd','asd.id = asds.attendance_day_v2_session_id')
                            ->where('asd.id',$session->sessionID)
                            ->get()->row();

          $session->taken_by = $this->_get_taken_by_attendance($session->taken_by);
          $session->absent = $absent;
          $session->att_data = $attArry;
          $session->isTaken = 1;
          $session->sessionID = $session->sessionID;
        }else{
          $session = new stdClass();
          $session->isTaken = 0;
          $session->taken_date = '';
          $session->taken_by = '';
          $session->att_data = [];
          $session->absent = '';
          $session->sessionID = '';
        }
        return $session;
      }


      private function _get_class_teacher_for_attendance($section){
        $result = $this->db_readonly->select("CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) as classTeacher, c.id as classID, c.class_name, cs.section_name, cs.id as sectionID")
        ->from('class_section cs')
        ->join('class c', 'c.id=cs.class_id')
        ->join('staff_master sm', 'cs.class_teacher_id=sm.id', 'left')
        ->where('cs.id', $section)
        ->get()->row();
    if (empty($result) || empty($result->classTeacher)) {
        // If no class teacher, fetch class and section info and set classTeacher as SuperAdmin
        $classInfo = $this->db_readonly->select('c.id as classID, c.class_name, cs.section_name, cs.id as sectionID')
            ->from('class_section cs')
            ->join('class c', 'c.id=cs.class_id')
            ->where('cs.id', $section)
            ->get()->row();
        if ($classInfo) {
            $classInfo->classTeacher = 'SuperAdmin';
            return $classInfo;
        } else {
            // Section not found at all
            $empty = new stdClass();
            $empty->classTeacher = 'SuperAdmin';
            $empty->classID = '';
            $empty->class_name = '';
            $empty->section_name = '';
            $empty->sectionID = '';
            return $empty;
        }
    }
        return $result;
      }
      private function _is_holiday($date, $section) {
        $result = $this->db_readonly->select('ce.id')
            ->from('calendar_events_v2 ce')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
            ->where('cea.assigned_section_id', $section)
            ->where('ce.event_type IN ("holiday", "holiday_range")')
            ->where('ce.from_date <=', $date)
            ->where('ce.to_date >=', $date)
            ->get()
            ->row();
        return !empty($result);
    }

    public function getstudentsclassWise($input) {
        // Validate input parameters
        if (!isset($input['selected_date']) || !isset($input['classsecID'])) {
            return ['error' => 'Required parameters missing'];
        }

        $date = date('Y-m-d', strtotime($input['selected_date']));
        list($class, $section) = explode('_', $input['classsecID']);
        $isHoliday = $this->_is_holiday($date, $section);

        if ($isHoliday) {
            return ['isHoliday' => true];
        }

        // Get session count for the day
        $dayName = date('l', strtotime($date));
        $sessionInfo = $this->db_readonly->select('os.session_count')
            ->from('calendar_events_v2_override_sessions os')
            ->join('calendar_v2_master cm','cm.id = os.calendar_v2_master_id')
            ->join('calendar_events_v2_assigned cea','cea.calendar_v2_master_id = cm.id')
            ->where('cea.assigned_section_id', $section)
            ->where('os.day_name', $dayName)
            ->where('cm.academic_year', $this->yearId)
            ->where('cm.start_date <=', $date)
            ->where('cm.end_date >=', $date)
            ->get()
            ->row();

        $sessionCount = $sessionInfo ? $sessionInfo->session_count : 2; 
        

        $student_data = $this->_student_data_for_attendance($class, $section);
        $class_data = $this->_get_class_teacher_for_attendance($section);
        $attendance_data = $this->_get_atttendance_data($date, $class, $section);
// echo "<pre>"; print_r($student_data);
// echo "<pre>"; print_r($attendance_data); die();
        // Get students on leave for this date and section
        $leave_data = $this->_get_students_on_leave($date, $section);

        // Validate student_data before iterating
        if (!is_array($student_data) && !is_object($student_data)) {
            $student_data = [];
        }

        foreach ($student_data as $key => $val) {
            // Check if student is on leave
            if (isset($leave_data[$val->id])) {
                $val->is_on_leave = true;
                $val->leave_type = $leave_data[$val->id]['leave_type'];
                $val->leave_reason = $leave_data[$val->id]['reason'];
                $val->leave_from_date = $leave_data[$val->id]['from_date'];
                $val->leave_to_date = $leave_data[$val->id]['to_date'];
                $val->noofdays = $leave_data[$val->id]['noofdays'];
            } else {
                $val->is_on_leave = false;
            }

            if (!empty($attendance_data->att_data)) {
                $attData = $attendance_data->att_data;
                if (array_key_exists($val->id, $attData)) {
                    $val->override = $attData[$val->id]->override;
                    $val->overide_remarks = $attData[$val->id]->overide_remarks;

                    // If student is on leave, apply leave-based attendance automatically
                    if (isset($leave_data[$val->id])) {
                        $val->morning = $leave_data[$val->id]['morning_session_status'];
                        $val->afternoon = $leave_data[$val->id]['afternoon_session_status'];
                    } else {
                        // Use existing attendance data if not on leave
                        $val->morning = $attData[$val->id]->morning;
                        $val->afternoon = $attData[$val->id]->afternoon;
                    }

                    $val->is_late = $attData[$val->id]->is_late;
                    $val->att_std_primary_id = $attData[$val->id]->att_std_id;
                } else {
                    // If no existing attendance data but student is on leave, set leave-based attendance
                    if (isset($leave_data[$val->id])) {
                        $val->morning = $leave_data[$val->id]['morning_session_status'];
                        $val->afternoon = $leave_data[$val->id]['afternoon_session_status'];
                        $val->override = 0;
                        $val->overide_remarks = '';
                        $val->is_late = 0;
                        $val->att_std_primary_id = null;
                    }
                }
            } else {
                // If no attendance data exists but student is on leave, set leave-based attendance
                if (isset($leave_data[$val->id])) {
                    $val->morning = $leave_data[$val->id]['morning_session_status'];
                    $val->afternoon = $leave_data[$val->id]['afternoon_session_status'];
                    $val->override = 0;
                    $val->overide_remarks = '';
                    $val->is_late = 0;
                    $val->att_std_primary_id = null;
                }
            }
        }

        $temp = new stdClass();
        $temp->class_teacher = (!empty($class_data)) ? $class_data->classTeacher : '';
        $temp->class_name = (!empty($class_data)) ? $class_data->class_name : '';
        $temp->section_name = (!empty($class_data)) ? $class_data->section_name : '';
        $temp->class_id = (!empty($class_data)) ? $class_data->classID : '';
        $temp->section_id = (!empty($class_data)) ? $class_data->sectionID : '';
        $temp->taken_date = $attendance_data->taken_date;
        $temp->taken_by = $attendance_data->taken_by;
        $temp->isTaken = $attendance_data->isTaken;
        $temp->absent = $attendance_data->absent;
        $temp->sessionID = $attendance_data->sessionID;
        $temp->sessionCount = $sessionCount;
        return ['isHoliday' => false, 'students' => ['student_data' => $student_data, 'summary' => $temp]];
    }



    public function addAttData($input) {
        // Ensure late_time column exists
        $this->ensure_late_time_column_exists();

        // Validate input parameter
        if (!is_array($input) || empty($input)) {
            return ['error' => 'Invalid input data provided'];
        }

        $this->db->trans_start();

        $taken_by = $this->authorization->getAvatarStakeHolderId();

        // Get date from input parameter with fallback options
        $date_input = '';
        if (isset($input['date']) && !empty($input['date'])) {
            $date_input = $input['date'];
        } elseif (isset($input['selected_date']) && !empty($input['selected_date'])) {
            $date_input = $input['selected_date'];
        } elseif (isset($input['att_taken_date']) && !empty($input['att_taken_date'])) {
            $date_input = $input['att_taken_date'];
        } else {
            $this->db->trans_rollback();
            return ['error' => 'Date is required'];
        }

        $date = date('Y-m-d', strtotime($date_input));

        // Get class_sec from input parameter, fallback to classsecID if class_sec is not available
        $class_sec = isset($input['class_sec']) ? $input['class_sec'] : (isset($input['classsecID']) ? $input['classsecID'] : '');

        if (empty($class_sec)) {
            $this->db->trans_rollback();
            return ['error' => 'Class section ID is required'];
        }

        list($class, $section) = explode('_', $class_sec);

        // Get leave data for students in this section on this date
        $leave_data = $this->_get_students_on_leave($date, $section);

        $student_data = array();
        $attn_data = array(
            'att_taken_date' => $date,
            'taken_by' => $taken_by,
            'taken_on' => date('Y-m-d H:i:s'),
            'class_id' => $class,
            'class_section_id' => $section,
            'acad_year_id' => $this->acad_year->getAcadYearId()
        );

        if (!empty($attn_data)) {
            $this->db->insert('attendance_std_day_v2_session', $attn_data);
        }
        $attendance_insert_id = $this->db->insert_id();

        if (isset($input['attendance']) && is_array($input['attendance']) && !empty($input['attendance'])) {
        foreach ($input['attendance'] as $student_id => $student_info) {
                $std_name = isset($student_info['Std_name']) ? $student_info['Std_name'] : '';
                $attendance_morning = isset($student_info['attendance_morning']) ? $student_info['attendance_morning'] : 0;
                $attendance_afternoon = isset($student_info['attendance_afternoon']) ? $student_info['attendance_afternoon'] : 0;
            $reasons = isset($student_info['reasons']) ? $student_info['reasons'] : 'null';
            $absent_reason_id = isset($student_info['reasons_id']) ? $student_info['reasons_id'] : 'null';

            // Check if student is on leave and override attendance accordingly
            if (isset($leave_data[$student_id])) {
                $attendance_morning = $leave_data[$student_id]['morning_session_status'];
                $attendance_afternoon = $leave_data[$student_id]['afternoon_session_status'];

                // Set leave-related remarks if no specific reason is provided
                if ($reasons === 'null' || empty($reasons)) {
                    $reasons = 'On Leave - ' . $leave_data[$student_id]['leave_type'];
                }
            }

            // Determine override based on absent reason's consider_as_present value
            $override = 0;
            if (!empty($reasons) && !empty($absent_reason_id) && $absent_reason_id !== 'null') {
                $absent_reason = $this->db_readonly->select('consider_as_present')
                    ->from('attendance_std_v2_absentreasons')
                    ->where('id', $absent_reason_id)
                    ->get()->row();
                if ($absent_reason && $absent_reason->consider_as_present == 1) {
                    $override = 1;
                }
            }
            $islate = isset($student_info['islate']) ? $student_info['islate'] : 0;

            // Handle late_time if present
            $late_time = null;
            if (isset($student_info['late_time']) && !empty($student_info['late_time'])) {
                $time = strtotime($student_info['late_time']);
                if ($time !== false) {
                    $late_time = date('Y-m-d H:i:s', $time);
                }
            }

            $student_data[] = array(
                'student_admission_id' => $student_id,
                'morning_session_status' => $attendance_morning,
                'afternoon_session_status' => $attendance_afternoon,
                'override_present' => $override,
                'absent_remarks' => $reasons,
                'absent_reason_id' => $absent_reason_id,
                'is_late' => $islate,
                'late_time' => $late_time,
            );
        }
        }

        foreach ($student_data as &$student) {
            $student['attendance_day_v2_session_id'] = $attendance_insert_id;
        }

        if (!empty($student_data)) {
            $this->db->insert_batch('attendance_std_day_v2_students', $student_data);
        }

        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return $attendance_insert_id;
        }
    }

  public function getabsent_stds(){
        $id = $this->input->post('insert_id');
        $input_date = $this->input->post('selectedDate');
        $selectedDate = date('Y-m-d', strtotime($input_date));
        // Get section and date from session
        $session = $this->db_readonly->select('class_section_id, att_taken_date')
            ->from('attendance_std_day_v2_session')
            ->where('id', $id)
            ->get()
            ->row();

        if (!$session) {
            return [];
        }

        $section = $session->class_section_id;
        $date = $session->att_taken_date;
        $dayName = date('l', strtotime($date));

        $sessionInfo = $this->db_readonly->select('os.session_count')
            ->from('calendar_events_v2_override_sessions os')
            ->join('calendar_v2_master cm','cm.id = os.calendar_v2_master_id')
            ->join('calendar_events_v2_assigned cea','cea.calendar_v2_master_id = cm.id')
            ->where('cea.assigned_section_id', $section)
            ->where('os.day_name', $dayName)
            ->where('cm.academic_year', $this->yearId)
            ->where('cm.start_date <=', $date)
            ->where('cm.end_date >=', $date)
            ->get()
            ->row();

        $sessionCount = $sessionInfo ? $sessionInfo->session_count : 2; 

        $this->db_readonly->select("
            ads.student_admission_id,
            ads.attendance_day_v2_session_id,
            ads.morning_session_status AS morning,
            ads.afternoon_session_status AS afternoon,
            sa.id as student_id,
            CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) AS std_name,
            concat(cs.class_name, ' ', cs.section_name) as class_Section,
            DATE_FORMAT(asds.att_taken_date, '%b %D %Y') AS attendance_taken_date,
            p.mobile_no as parentMobile,
            sr.relation_type as parentRelation
        ");
        $this->db_readonly->from('attendance_std_day_v2_students ads');
        $this->db_readonly->join('attendance_std_day_v2_session asds', 'asds.id = ads.attendance_day_v2_session_id');
        $this->db_readonly->join('student_admission sa', 'sa.id = ads.student_admission_id');
        $this->db_readonly->join('student_relation sr', 'sr.std_id = sa.id');
        $this->db_readonly->join('parent p', 'p.id = sr.relation_id');
        $this->db_readonly->join('student_year sy', 'sa.id = sy.student_admission_id');
        $this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id');

        // Exclude students who are on approved leave for this date
        $this->db_readonly->where("sa.id NOT IN (
            SELECT ls.student_id
            FROM leave_student ls
            WHERE ls.status = 'Approved'
            AND ls.acad_year_id = {$this->yearId}
            AND ls.from_date <= '{$selectedDate}'
            AND ls.to_date >= '{$selectedDate}'
        )");

        $this->db_readonly->where('sy.acad_year_id', $this->yearId);
        $this->db_readonly->where('asds.id', $id);
        $this->db_readonly->where('DATE(asds.att_taken_date)', $selectedDate);

        if ($sessionCount == 2) {
            $this->db_readonly->group_start();
            $this->db_readonly->where('ads.morning_session_status', 0);
            $this->db_readonly->or_where('ads.afternoon_session_status', 0);
            $this->db_readonly->group_end();
        } else {
            $this->db_readonly->where('ads.morning_session_status', 0);
        }

        $result = $this->db_readonly->get()->result();
        return $result;
    }

    public function update_notified_at($session_insert_id) {
          return $this->db->where('id', $session_insert_id)->update('attendance_std_day_v2_session', ['notified_at' => date('Y-m-d H:i:s')]);
      }

      public function update_Attendance() {
        // Ensure late_time column exists
        $this->ensure_late_time_column_exists();

        $input = $this->input->post();
        $this->db->trans_start();
        $taken_by = $this->authorization->getAvatarStakeHolderId();
        $date = $this->input->post('date');
        $sectionId = $this->input->post('secID');
        $sessionId = $this->input->post('sessionId');
        $history_data = [];

        // Get leave data for students in this section on this date
        $leave_data = $this->_get_students_on_leave($date, $sectionId);

        foreach ($input['attendance'] as $student_id => &$student_info) {
            $existing_data = $this->db
                ->where('student_admission_id', $student_id)
                ->where('attendance_day_v2_session_id', $sessionId)
                ->get('attendance_std_day_v2_students')
                ->row_array();

            $std_name = isset($student_info['Std_name']) ? $student_info['Std_name'] : '';
            $attendance_morning = isset($student_info['attendance_morning']) ? $student_info['attendance_morning'] : 0;
            $attendance_afternoon = isset($student_info['attendance_afternoon']) ? $student_info['attendance_afternoon'] : 0;
            $reasons = isset($student_info['reasons']) ? $student_info['reasons'] : 'null';
            $absent_reason_id = isset($student_info['reasons_id']) ? $student_info['reasons_id'] : 'null';

            // Check if student is on leave and override attendance accordingly
            if (isset($leave_data[$student_id])) {
                $attendance_morning = $leave_data[$student_id]['morning_session_status'];
                $attendance_afternoon = $leave_data[$student_id]['afternoon_session_status'];

                // Set leave-related remarks if no specific reason is provided
                if ($reasons === 'null' || empty($reasons)) {
                    $reasons = 'On Leave - ' . $leave_data[$student_id]['leave_type'];
                }
            }

            // Determine override based on absent reason's consider_as_present value
            $override = 0;
            if (!empty($reasons) && !empty($absent_reason_id) && $absent_reason_id !== 'null') {
                $absent_reason = $this->db_readonly->select('consider_as_present')
                    ->from('attendance_std_v2_absentreasons')
                    ->where('id', $absent_reason_id)
                    ->get()->row();
                if ($absent_reason && $absent_reason->consider_as_present == 1) {
                    $override = 1;
                }
            }
            $islate = isset($student_info['islate']) ? $student_info['islate'] : 0;

            // Handle late_time if present
            $late_time = null;
            if (isset($student_info['late_time']) && !empty($student_info['late_time'])) {
                $time = strtotime($student_info['late_time']);
                if ($time !== false) {
                    $late_time = date('Y-m-d H:i:s', $time);
                }
            }

            $update_data = [
                'morning_session_status' => $attendance_morning,
                'afternoon_session_status' => $attendance_afternoon,
                'is_late' => $islate,
                'override_present' => $override,
                'absent_remarks' => $reasons,
                'late_time' => $late_time,
            ];
            if ($existing_data) {
              if ($existing_data['morning_session_status'] != $attendance_morning ||
                  $existing_data['afternoon_session_status'] != $attendance_afternoon) {
                  $this->db
                      ->where('student_admission_id', $student_id)
                      ->where('attendance_day_v2_session_id', $sessionId)
                      ->update('attendance_std_day_v2_students', $update_data);
                  $history_data[] = array(
                      'att_day_v2_std_admission_id' => $student_id,
                      'action_by' => $taken_by,
                      'att_day_session' => $sessionId,
                      'att_day_student' => $student_id
                  );
              }
          }
      }
        if (!empty($history_data)) {
            $this->db->insert_batch('attendance_std_day_v2_edit_history', $history_data);
        }

        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return false;
        } else {
            $this->db->trans_commit();
            return true;
        }
    }

    public function getclassection_name() {
        $class_sec = $this->input->post('classsecID');

        // Validate that classsecID exists and is an array
        if (!is_array($class_sec) || empty($class_sec)) {
            return []; // Return empty array if no valid data
        }

        $class = [];
        $section = [];

        foreach($class_sec as $key => $value) {
            if (!empty($value) && strpos($value, '_') !== false) {
                list($class[], $section[]) = explode('_', $value);
            }
        }

        // If no valid class/section pairs found, return empty
        if (empty($section)) {
            return [];
        }
        $this->db_readonly->select('cs.section_name,cs.class_id,cs.id,class.class_name');
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class', 'class.id = cs.class_id');
        $this->db_readonly->where('class.acad_year_id',$this->yearId);
        if(!empty($section)){
            $this->db_readonly->where_in('cs.id', $section);  // Fixed this line
        }
        $this->db_readonly->order_by('class.display_order');
        $return = $this->db_readonly->get()->result();
            return $return;
        }

  private function _get_total_working_days($section, $start_date, $end_date) {
    $calendar = $this->db_readonly->select("cm.id, cm.start_date, cm.end_date")
        ->from('calendar_v2_master cm')
        ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id=cm.id')
        ->where_in('cea.assigned_section_id', $section)
        ->where('cm.academic_year', $this->yearId)
        ->where('cm.start_date <=', $end_date)
        ->where('cm.end_date >=', $start_date)
        ->get()
        ->row();

    if (empty($calendar)) {
        return 0; // Return 0 if section is not assigned to any calendar
    }

    $session_counts = $this->db_readonly->select('day_name, session_count')
        ->from('calendar_events_v2_override_sessions')
        ->where('calendar_v2_master_id', $calendar->id)
        ->get()
        ->result();

    $session_map = [];
    foreach ($session_counts as $sc) {
        $session_map[strtolower($sc->day_name)] = (int)$sc->session_count;
    }

    $total_working_days = 0;
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    while ($start <= $end) {
        $day_name = strtolower($start->format('l')); // Get day of week (e.g. monday, tuesday, etc.)
        if (isset($session_map[$day_name]) && $session_map[$day_name] > 0) {
            $total_working_days++;
        }
        $start->modify('+1 day');
    }

    return $total_working_days;
}

    public function getAttendanceDetails($input) {
        $class_sec = $this->input->post('classsecID');

        // Validate that classsecID exists and is an array
        if (!is_array($class_sec) || empty($class_sec)) {
            return []; // Return empty array if no valid data
        }

        $class = [];
        $section = [];

        foreach($class_sec as $key => $value) {
            if (!empty($value) && strpos($value, '_') !== false) {
                list($class[], $section[]) = explode('_', $value);
            }
        }

        // If no valid class/section pairs found, return empty
        if (empty($class) || empty($section)) {
            return [];
        }

        // Validate date parameters
        if (!isset($input['from_date']) || !isset($input['to_date'])) {
            return ['error' => 'Date parameters are required'];
        }

        $fromDate = $input['from_date'];
        $toDate = $input['to_date'];

        // Fetch assigned calendar and session count data
        $assigned_calendar = $this->db_readonly->select('cea.calendar_v2_master_id')
            ->from('calendar_events_v2_assigned cea')
            ->where_in('cea.assigned_section_id', $section)
            ->get()
            ->row();
        if (empty($assigned_calendar)) {
            return array();
        }
        // Retrieve session count data and build day name => session_count mapping
        $sessionCounts = $this->db_readonly->select('day_name, session_count')
            ->from('calendar_events_v2_override_sessions')
            ->where('calendar_v2_master_id', $assigned_calendar->calendar_v2_master_id)
            ->get()
            ->result();
        $sessionMap = [];
        foreach ($sessionCounts as $sc) {
            $sessionMap[$sc->day_name] = $sc->session_count;
        }

        $total_working_days = $this->_get_total_working_days($section, date('Y-m-d', strtotime($fromDate)), date('Y-m-d', strtotime($toDate)));

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name";
        switch ($prefix_student_name) {
            case "roll_number":
                $std_name = "CONCAT(IF(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name";
                break;
            case "enrollment_number":
                $std_name = "CONCAT(IFNULL(sd.enrollment_number, 'NA'), ' - ', IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name";
                break;
            case "admission_number":
                $std_name = "CONCAT(IFNULL(sd.admission_no, 'NA'), ' - ', IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name";
                break;
            case "alpha_rollnum":
                $std_name = "CONCAT(IFNULL(sy.alpha_rollnum, 'NA'), ' - ', IFNULL(sd.first_name, ''), ' ', IFNULL(sd.last_name, '')) AS std_name";
                break;
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'cs.display_order';
        switch ($prefix_order_by) {
            case "roll_number":
                $order_by = 'sy.roll_no, sd.first_name';
                break;
            case "enrollment_number":
                $order_by = 'sd.enrollment_number, sd.first_name';
                break;
            case "admission_number":
                $order_by = 'sd.admission_no, sd.first_name';
                break;
            case "alpha_rollnum":
                $order_by = 'sy.alpha_rollnum, sd.first_name';
                break;
        }

        // Get all students in the selected sections first
        $this->db_readonly->select("
            sd.id as student_admission_id,
            $std_name,
            sy.roll_no,
            sd.admission_no,
            CONCAT(c.class_name, ' ', cs.section_name) AS csName,
            sy.class_section_id,
            $total_working_days AS total_days
        ")
        ->from('student_admission sd')
        ->join('student_year sy', 'sd.id = sy.student_admission_id')
        ->join('class c', 'c.id = sy.class_id')
        ->join('class_section cs', 'sy.class_section_id = cs.id', 'left')
        ->where('sy.acad_year_id', $this->yearId)
        ->where('sd.admission_status', 2)
        ->where('sy.promotion_status !=', 4)
        ->where('sy.promotion_status !=', 5);
        if ($input['classsecID'] != 0) {
            $this->db_readonly->where_in('sy.class_id', $class);
            $this->db_readonly->where_in('sy.class_section_id', $section);
        }
        $this->db_readonly->order_by("$order_by");
        $students = $this->db_readonly->get()->result_array();

        if (empty($students)) {
            return array();
        }

        // Now get attendance data for the date range
        $this->db_readonly->select("
            DATE_FORMAT(atses.att_taken_date, '%d-%b-%Y') AS att_date,
            DAYNAME(atses.att_taken_date) AS dayName,
            asds.student_admission_id,
            asds.morning_session_status AS morning,
            asds.afternoon_session_status AS afternoon
        ")
        ->from('attendance_std_day_v2_students asds')
        ->join('attendance_std_day_v2_session atses', 'atses.id = asds.attendance_day_v2_session_id')
        ->where('atses.att_taken_date >=', date('Y-m-d', strtotime($fromDate)))
        ->where('atses.att_taken_date <=', date('Y-m-d', strtotime($toDate)))
        ->where_in('asds.student_admission_id', array_column($students, 'student_admission_id'));
        if ($input['classsecID'] != 0) {
            $this->db_readonly->where_in('atses.class_id', $class);
            $this->db_readonly->where_in('atses.class_section_id', $section);
        }

        $attendance_data = $this->db_readonly->get()->result_array();

        // Get leave data for all students in the date range
        $leave_data_by_date = [];
        $start_date = new DateTime($fromDate);
        $end_date = new DateTime($toDate);

        while ($start_date <= $end_date) {
            $current_date = $start_date->format('Y-m-d');
            $formatted_date = $start_date->format('d-M-Y');

            foreach ($section as $sec_id) {
                $leave_data = $this->_get_students_on_leave($current_date, $sec_id);
                if (!empty($leave_data)) {
                    $leave_data_by_date[$formatted_date] = array_merge(
                        isset($leave_data_by_date[$formatted_date]) ? $leave_data_by_date[$formatted_date] : [],
                        $leave_data
                    );
                }
            }
            $start_date->modify('+1 day');
        }

        // Organize attendance data by date and student
        $attendance_by_date = [];
        foreach ($attendance_data as $att) {
            $attendance_by_date[$att['att_date']][$att['student_admission_id']] = $att;
        }

        // Build final result including students on leave
        $final_result = [];

        // Generate all dates in the range
        $date_range = [];
        $start_date = new DateTime($fromDate);
        $end_date = new DateTime($toDate);

        while ($start_date <= $end_date) {
            $date_range[] = $start_date->format('d-M-Y');
            $start_date->modify('+1 day');
        }

        // Process each date in the range
        foreach ($date_range as $date_key) {
            $current_date_obj = DateTime::createFromFormat('d-M-Y', $date_key);
            $day_name = $current_date_obj->format('l');
            $current_date_ymd = $current_date_obj->format('Y-m-d');

            foreach ($students as $student) {
                $student_id = $student['student_admission_id'];
                $section_id = $student['class_section_id'];

                // Check if this student has attendance record for this date
                $attendance_record = isset($attendance_by_date[$date_key][$student_id])
                    ? $attendance_by_date[$date_key][$student_id]
                    : null;

                // Check if student is on leave for this date
                $is_on_leave = isset($leave_data_by_date[$date_key][$student_id]);
                $leave_info = $is_on_leave ? $leave_data_by_date[$date_key] : null;

                $sessions = isset($sessionMap[$day_name]) ? $sessionMap[$day_name] : 2;
                $present = 0;
                $morning = null;
                $afternoon = null;

                if ($attendance_record) {
                    // Student has attendance record
                    $morning = $attendance_record['morning'];
                    $afternoon = $attendance_record['afternoon'];
                } elseif ($is_on_leave) {
                    // Student is on leave but no attendance record - mark as per leave status
                    $morning = $leave_info[$student_id]['morning_session_status'];
                    $afternoon = $leave_info[$student_id]['afternoon_session_status'];
                } else {
                    // No attendance record and not on leave - skip this student for this date
                    continue;
                }

                // Calculate present status
                if ($sessions == 1) {
                    $present = ($morning == 1) ? 1 : 0;
                } elseif ($sessions == 2) {
                    $morningPresent = $morning == 1;
                    $afternoonPresent = $afternoon == 1;

                    if ($morningPresent && $afternoonPresent) {
                        $present = 1;
                    } elseif ($morningPresent || $afternoonPresent) {
                        $present = 0.5;
                    }
                }

                $student_data = array_merge($student, [
                    'att_date' => $date_key,
                    'dayName' => $day_name,
                    'session_count' => $sessions,
                    'present' => $present,
                    'morning' => $morning,
                    'afternoon' => $afternoon,
                    'is_on_leave' => $is_on_leave,
                    'leave_info' => $is_on_leave ? $leave_info[$student_id] : null
                ]);

                $final_result[$date_key][] = $student_data;
            }
        }
        return $final_result;
    }




     public function getAttendanceHistory(){
      $input = $this->input->post();
      $id = $input['id'];
      
      $result= $this->db_readonly->select("aeh.history_data, a.friendly_name as taken_by,DATE_FORMAT(aeh.action_on, '%d-%b-%Y %h:%i %p') as event_date")
                                  ->from('attendance_std_day_v2_edit_history aeh')
                                  ->join('avatar a', 'a.stakeholder_id = aeh.action_by AND a.avatar_type = 4', 'left')
                                  ->where('aeh.att_day_v2_id', $id)
                                  ->get()
                                  ->result();
                                 
      return $result;



     }

    public function update_student_att_data_by_att_id($student_id, $att_id, $session_column, $sessionValue, $session_id, $history_data, $additionalUpdates = []) {
        $taken_by = $this->authorization->getAvatarStakeHolderId();

        // Validate required parameters
        if (empty($att_id)) {
            log_message('error', 'update_student_att_data_by_att_id: att_id is empty');
            return false;
        }

        // Check if the attendance record exists
        $existing_record = $this->db->select('id')
            ->where('id', $att_id)
            ->get('attendance_std_day_v2_students')
            ->row();

        if (!$existing_record) {
            log_message('error', 'update_student_att_data_by_att_id: Attendance record not found for att_id: ' . $att_id);
            return false;
        }

        // Ensure late_time column exists
        $this->ensure_late_time_column_exists();

        $this->db->trans_start();

        // Build update array
        $array = [];
        // Always set the session_column (e.g., is_late)
        if ($session_column) {
            $array[$session_column] = $sessionValue;
        }
        // Always set is_late if present in additionalUpdates (for late marking)
        if (isset($additionalUpdates['is_late'])) {
            $array['is_late'] = $additionalUpdates['is_late'];
        }
        // Always set late_time if present in additionalUpdates
        if (!empty($additionalUpdates['late_time'])) {
            $time = strtotime($additionalUpdates['late_time']);
            if ($time !== false) {
                $array['late_time'] = date('Y-m-d H:i:s', $time);
            } else {
                $array['late_time'] = null;
            }
        }
        // If unchecking late or adding absent remarks, clear late_time and is_late if needed
        if (($session_column == 'is_late' && $sessionValue == 0) || $session_column == 'absent_remarks') {
            $array['late_time'] = null;
            if ($session_column == 'absent_remarks') {
                $array['is_late'] = 0;
            }
        }
        // Merge any other additional updates (except late_time, is_late)
        foreach ($additionalUpdates as $key => $value) {
            if ($key != 'late_time' && $key != 'is_late') {
                $array[$key] = $value;
            }
        }
        // Set override_present based on absent reason's consider_as_present value
        if ($session_column == 'absent_remarks') {
            $array['override_present'] = 0; // Default to 0

            // Extract reason ID from the reason text if it contains " - "
            $reason_parts = explode(' - ', $sessionValue);
            if (count($reason_parts) >= 2) {
                $reason_name = trim($reason_parts[0]);
                $absent_reason = $this->db_readonly->select('consider_as_present')
                    ->from('attendance_std_v2_absentreasons')
                    ->where('reasons', $reason_name)
                    ->get()->row();
                if ($absent_reason && $absent_reason->consider_as_present == 1) {
                    $array['override_present'] = 1;
                }
            }
        }
        // Update attendance record
        $this->db->where('id', $att_id);
        $update_result = $this->db->update('attendance_std_day_v2_students', $array);

        // Check if update was successful
        if (!$update_result) {
            $this->db->trans_rollback();
            log_message('error', 'Failed to update attendance record. Query: ' . $this->db->last_query());
            return false;
        }

        // Insert history record
        $history = array(
            'att_day_v2_id' => $att_id,
            'action_by' => $taken_by,
            'att_day_session' => $session_id,
            'action_on' => date('Y-m-d H:i:s'),
            'history_data' => $history_data,
        );
        $history_result = $this->db->insert('attendance_std_day_v2_edit_history', $history);

        // Check if history insert was successful
        if (!$history_result) {
            $this->db->trans_rollback();
            log_message('error', 'Failed to insert attendance history. Query: ' . $this->db->last_query());
            return false;
        }

        $this->db->trans_complete();

        if ($this->db->trans_status() === FALSE) {
            log_message('error', 'Transaction failed in update_student_att_data_by_att_id');
            return false;
        }

        return true;
    }

    /**
     * Ensure late_time column exists in attendance_std_day_v2_students table
     */
    private function ensure_late_time_column_exists() {
        if (!$this->db->field_exists('late_time', 'attendance_std_day_v2_students')) {
            try {
                $sql = "ALTER TABLE `attendance_std_day_v2_students` ADD COLUMN `late_time` TIMESTAMP NULL DEFAULT NULL AFTER `is_late`";
                $this->db->query($sql);
                log_message('info', 'Successfully added late_time column to attendance_std_day_v2_students table');
            } catch (Exception $e) {
                log_message('error', 'Failed to add late_time column: ' . $e->getMessage());
            }
        }
    }

     public function getClassAssignedSessions($section, $date) {
        $dayName = date('l', strtotime($date));

        $result = $this->db_readonly->select('os.calendar_v2_master_id, os.day_name, os.session_count,
                                            cm.start_date, cm.end_date, cea.assigned_section_id')
                                    ->from('calendar_events_v2_override_sessions os')
                                    ->join('calendar_v2_master cm','cm.id = os.calendar_v2_master_id')
                                    ->join('calendar_events_v2_assigned cea','cea.calendar_v2_master_id = cm.id')
                                    ->where('cea.assigned_section_id', $section)
                                    ->where('os.day_name', $dayName)
                                    ->where('cm.academic_year', $this->yearId)
                                    ->where('cm.start_date <=', date('Y-m-d', strtotime($date)))
                                    ->where('cm.end_date >=', date('Y-m-d', strtotime($date)))
                                    ->get()
                                    ->result();

        return $result;
    }

    public function getAssignedDate($input) {
        // Validate input parameters
        if (!isset($input['selectedDate']) || !isset($input['selectedSec'])) {
            return ['error' => 'Required parameters missing'];
        }

        $selectedDate = date('Y-m-d', strtotime($input['selectedDate']));
        list($class, $section) = explode('_', $input['selectedSec']);

      $result = $this->db_readonly->select('cm.id, cm.status')
          ->from('calendar_v2_master cm')
          ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id=cm.id')
          ->where('cm.academic_year', $this->yearId)
          ->where('cea.assigned_section_id', $section)
          ->where('cm.start_date <=', $selectedDate)
          ->where('cm.end_date >=', $selectedDate)
          ->get()
          ->row();

      if (!$result) {
          return false; // No calendar template assigned
      }

      // Return an object with calendar info and lock status
      return array(
          'calendar_id' => $result->id,
          'is_locked' => $result->status == 1 ? true : false
      );
  }

    public function getHolidaysAndWeekends($from_date, $to_date) {
    $class_sec = $this->input->post('classsecID');

    // Validate that classsecID exists and is an array
    if (!is_array($class_sec) || empty($class_sec)) {
        return ['dates' => [], 'weekends' => []]; // Return empty structure if no valid data
    }

    $class = [];
    $section = [];

    foreach($class_sec as $key => $value) {
        if (!empty($value) && strpos($value, '_') !== false) {
            list($class[], $section[]) = explode('_', $value);
        }
    }

    // If no valid class/section pairs found, return empty
    if (empty($class) || empty($section)) {
        return ['dates' => [], 'weekends' => []];
    }

    $from_date = date('Y-m-d', strtotime($from_date));
    $to_date = date('Y-m-d', strtotime($to_date));
    
        $holidays = $this->db_readonly->select('ce.from_date, ce.to_date')
            ->from('calendar_events_v2 ce')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
            ->where_in('cea.assigned_section_id', $section)
            ->where_in('ce.event_type', ['holiday', 'holiday_range'])
            ->where('ce.from_date <=', $to_date)
            ->where('ce.to_date >=', $from_date)
            ->get()
            ->result();

        $holiday_dates = [];
        foreach ($holidays as $holiday) {
                $interval = new DateInterval('P1D');
                $start = new DateTime($holiday->from_date);
                $end = new DateTime($holiday->to_date);
                $daterange = new DatePeriod($start, $interval, $end->modify('+1 day'));
                foreach ($daterange as $date) {
                    $holiday_dates[] = $date->format('Y-m-d');
            }
        }
        $holidayCount = count($holiday_dates); // Ensure $holidayCount is always defined

        $weekends = [];
        $start = new DateTime($from_date);
        $end = new DateTime($to_date);
        $interval = new DateInterval('P1D');
        $daterange = new DatePeriod($start, $interval, $end->modify('+1 day'));

        foreach ($daterange as $date) {
            if ($date->format('N') == 7) { // Sunday
                $weekends[] = $date->format('Y-m-d');
        }
    }

        // Return both the merged dates and the holidayCount
        return [
            'dates' => array_merge($holiday_dates, $weekends),
        'holidayCount' => $holidayCount
        ];
}


    public function getNotTakenAttendanceData($payload) {
        $classsecID = isset($payload['classsecID']) ? $payload['classsecID'] : [];
        $fromDate = date('Y-m-d', strtotime($payload['from_date']));
        $toDate = date('Y-m-d', strtotime($payload['to_date']));
        $dateRange = $this->__generate_date_range($fromDate, $toDate);

        $section = [];
        $class = [];

        // Validate classsecID is an array before using in_array
        if (is_array($classsecID) && in_array('all_all', $classsecID)) {
            // Fetch all sections
            $sections = $this->db_readonly->select('cs.id as section_id, cs.class_id as class_id')
                ->from('class_section cs')
                ->join('class c', 'c.id = cs.class_id')
                ->where('c.acad_year_id', $this->yearId)
                ->get()->result();
            
            foreach ($sections as $row) {
                $section[] = $row->section_id;
                $class[] = $row->class_id;
            }
        } else {
            // Process selected sections
            foreach ($classsecID as $csID) {
                if (strpos($csID, '_') !== false) {
                    list($class_id, $section_id) = explode('_', $csID);
                    $class[] = $class_id;
                    $section[] = $section_id;
                }
            }
        }

        // Return empty if no valid selections
        if (empty($section) || empty($class)) {
            return [
                'dateRange' => $dateRange,
                'report' => []
            ];
        }
        $this->db_readonly->select("CONCAT(IFNULL(sm.first_name, ''), ' ', IFNULL(sm.last_name, '')) as classTeacher, 
                                cs.id as sectionID, c.id as classID, c.class_name, cs.section_name")
            ->from('class_section cs')
            ->join('staff_master sm', 'sm.id = cs.class_teacher_id', 'left')
            ->join('class c', 'c.id = cs.class_id')
            ->where('c.acad_year_id', $this->yearId)
            ->where_in('cs.id', $section)
            ->where_in('c.id', $class);

        $details = $this->db_readonly->get()->result();
        
        if (empty($details)) return [];

        // Get calendar assignments
        $this->db_readonly->select('assigned_section_id, assigned_class_id, calendar_v2_master_id')
            ->from('calendar_events_v2_assigned')
            ->where_in('assigned_section_id', $section)
            ->where_in('assigned_class_id', $class);
        $calendarAssignments = $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($calendarAssignments); die();
        
        $classes2 = [];
        $sections2 = [];
        $calendarIds = [];
        foreach ($calendarAssignments as $assignment) {
            $classes2[]= $assignment->assigned_class_id;
            $sections2[]= $assignment->assigned_section_id;
            $calendarIds[] = $assignment->calendar_v2_master_id;
        }
        
        // Attendance data
        $attendanceArray = [];
        if (!empty($calendarAssignments)) {
            $this->db_readonly->select('class_id, class_section_id, att_taken_date')
                ->from('attendance_std_day_v2_session')
                ->where_in('att_taken_date', $dateRange)
                ->where_in('class_section_id',$sections2 )
                ->where_in('class_id', $classes2);
            $attendanceData = $this->db_readonly->get()->result();
            
            foreach ($attendanceData as $row) {
                $attendanceArray[$row->class_id][$row->class_section_id][$row->att_taken_date] = true;
            }
        }

        $holidays = [];
        if (!empty($calendarAssignments)) {
            $this->db_readonly->select("assigned.assigned_class_id, assigned.assigned_section_id, events.from_date, events.to_date")
                ->from("calendar_events_v2 events")
                ->join("calendar_v2_master master", "events.calendar_v2_master_id = master.id")
                ->join("calendar_events_v2_assigned assigned", "assigned.calendar_v2_master_id = master.id")
                ->where_in("events.event_type", ['holiday', 'holiday_range'])
                ->where("events.from_date <=", $toDate)
                ->where("events.to_date >=", $fromDate)
                ->where("master.academic_year", $this->yearId)
                ->where_in('assigned.assigned_section_id', $sections2)
                ->where_in('assigned.assigned_class_id', $classes2);

            $holidayResults = $this->db_readonly->get()->result();

            foreach ($holidayResults as $row) {
                $dates = $this->__generate_date_range($row->from_date, $row->to_date);
                foreach ($dates as $date) {
                    $holidays[$row->assigned_class_id][$row->assigned_section_id][$date] = true;
                }
            }
        }
        // Weekends
        $weekendsArray = [];
        if (!empty($calendarAssignments)) {
            $this->db_readonly->select('cs.day_name, assigned.assigned_class_id, assigned.assigned_section_id, cs.session_count')
                ->from('calendar_events_v2_override_sessions as cs')
                ->join('calendar_events_v2_assigned as assigned', 'assigned.calendar_v2_master_id = cs.calendar_v2_master_id')
                ->where('cs.session_count', 0)
                ->where_in('assigned.assigned_section_id', $sections2)
                ->where_in('assigned.assigned_class_id', $classes2);
            $weekends = $this->db_readonly->get()->result();

            foreach ($weekends as $row) {
                $day = strtolower($row->day_name);
                $weekendsArray[$row->assigned_class_id][$row->assigned_section_id][$day] = true;
            }
        }
        // Final Report
        $report = [];
        foreach ($details as $detail) {
            $row = [
                'class' => $detail->class_name,
                'section' => $detail->section_name,
                'sectionID' => $detail->sectionID,
                'classID' => $detail->classID,
                'classTeacher' => $detail->classTeacher,
                'status' => []
            ];

            $assignedLookup = [];
            foreach ($calendarAssignments as $assignment) {
                $assignedLookup[$assignment->assigned_class_id][$assignment->assigned_section_id] = true;
            }

            $isAssigned = !empty($assignedLookup[$detail->classID][$detail->sectionID]);

            foreach ($dateRange as $date) {
                if ($isAssigned) {
                    // Attendance taken
                    if (!empty($attendanceArray[$detail->classID][$detail->sectionID][$date])) {
                        $row['status'][$date] = 1;
                        continue;
                    }
                    // Weekend
                    $dayName = strtolower(date('l', strtotime($date)));
                    if (!empty($weekendsArray[$detail->classID][$detail->sectionID][$dayName])) {
                        $row['status'][$date] = 'WE';
                        continue;
                    }
                    // Holiday
                    if (!empty($holidays[$detail->classID][$detail->sectionID][$date])) {
                        $row['status'][$date] = 'H';
                        continue;
                    }
                    // Not taken
                    $row['status'][$date] = 2;
                } else {
                    $row['status'][$date] = '-';
                }
            }
            $report[] = $row;
        }
        
        return [
            'dateRange' => $dateRange,
            'report' => $report
        ];
    }
 
 
    private function __checkIfHoliday($from_date, $to_date) {
        $from_date = date('Y-m-d', strtotime($from_date));
        $to_date = date('Y-m-d', strtotime($to_date));
    
        $this->db_readonly->select("assigned.assigned_class_id, assigned.assigned_section_id, events.from_date, events.to_date");
        $this->db_readonly->from("calendar_v2_master cal");
        $this->db_readonly->join("calendar_events_v2 events", "events.calendar_v2_master_id = cal.id");
        $this->db_readonly->join("calendar_events_v2_assigned assigned", "assigned.calendar_v2_master_id = cal.id");
        $this->db_readonly->where_in("events.event_type", ['holiday', 'holiday_range']);
        $this->db_readonly->where("events.to_date >=", $from_date);
        $this->db_readonly->where("events.from_date <=", $to_date);
    
        $results = $this->db_readonly->get()->result();
        $holidays = [];
    
        foreach ($results as $row) {
            $dates = $this->__generate_date_range($row->from_date, $row->to_date);
            foreach ($dates as $date) {
                $holidays[$row->assigned_class_id][$row->assigned_section_id][$date] = true;
            }
        }
    
        return $holidays;
    }
    


    private function __generate_date_range($start, $end) {
        $range = [];
        $start = new DateTime($start);
        $end = new DateTime($end);
        while ($start <= $end) {
            $range[] = $start->format('Y-m-d');
            $start->modify('+1 day');
        }
        return $range;
    }

 public function getStudentConsecutiveAbsentData($payload) {
    // Validate input parameters
    if (!isset($payload['from_date']) || !isset($payload['to_date']) || !isset($payload['classsecID'])) {
        return ['error' => 'Required parameters missing'];
    }

    $fromDate = date('Y-m-d', strtotime($payload['from_date']));
    $toDate = date('Y-m-d', strtotime($payload['to_date']));

    $classSectionPairs = $payload['classsecID'];
    $classIds = [];
    $sectionIds = [];

    foreach ($classSectionPairs as $pair) {
        if (strpos($pair, '_') !== false) {
            list($classId, $sectionId) = explode('_', $pair);
            $classIds[] = (int)$classId;
            $sectionIds[] = (int)$sectionId;
        }
    }

    $calendars = $this->db_readonly->select('id, start_date, end_date')
        ->from('calendar_v2_master')
        ->where('academic_year', $this->yearId)
        ->get()
        ->result();

    if (empty($calendars)) {
        return ['error' => 'No active academic calendars found'];
    }

    $assignments = $this->db_readonly->select('calendar_v2_master_id, assigned_class_id, assigned_section_id')
        ->from('calendar_events_v2_assigned')
        ->where_in('calendar_v2_master_id', array_column($calendars, 'id'))
        ->where_in('assigned_class_id', $classIds)
        ->where_in('assigned_section_id', $sectionIds)
        ->get()
        ->result();

    if (empty($assignments)) {
        return ['error' => 'No calendar assignments found for the selected class and section'];
    }

    $classSectionCalendarMap = [];
    $calendarDetails = [];

    foreach ($calendars as $cal) {
        $calendarDetails[$cal->id] = $cal;
    }

    foreach ($assignments as $assignment) {
        $key = $assignment->assigned_class_id . '-' . $assignment->assigned_section_id;
        $classSectionCalendarMap[$key] = $assignment->calendar_v2_master_id;
    }

    $sessionCounts = $this->db_readonly->select('calendar_v2_master_id, day_name, session_count')
        ->from('calendar_events_v2_override_sessions')
        ->where_in('calendar_v2_master_id', array_column($calendars, 'id'))
        ->get()
        ->result();

    $sessionMap = [];
    foreach ($sessionCounts as $sc) {
        $sessionMap[$sc->calendar_v2_master_id][strtolower($sc->day_name)] = (int)$sc->session_count;
    }

    $holidays = $this->__checkIfHoliday($fromDate, $toDate);

    $this->db_readonly->select([
        "CONCAT(IFNULL(admission.first_name, ''), ' ', IFNULL(admission.last_name, '')) as student_name",
        's.student_admission_id as admission_no',
        'c.id as classID',
        'c.class_name as class',
        'cs.id as sectionID',
        'cs.section_name as section',
        's.morning_session_status',
        's.afternoon_session_status',
        "IFNULL(admission.enrollment_number, '-') as enrollment_number",
        "DATE_FORMAT(session.att_taken_date, '%d-%b-%Y') as att_taken_date"
    ])
    ->from('attendance_std_day_v2_students s')
    ->join('student_admission as admission', 'admission.id = s.student_admission_id')
    ->join('attendance_std_day_v2_session as session', 'session.id = s.attendance_day_v2_session_id')
    ->join('class as c', 'c.id = session.class_id')
    ->join('class_section as cs', 'cs.id = session.class_section_id')
    ->where('session.acad_year_id', $this->yearId)
    ->where("session.att_taken_date BETWEEN '$fromDate' AND '$toDate'")
    ->where_in('c.id', $classIds)
    ->where_in('cs.id', $sectionIds)
    ->group_start()
        ->where('s.morning_session_status', 0)
        ->or_where('s.afternoon_session_status', 0)
    ->group_end();

    $studentDetails = $this->db_readonly->get()->result();

    if (empty($studentDetails)) {
        return [];
    }

    $absenceReport = [];

    foreach ($studentDetails as $detail) {
        $studentId = $detail->admission_no;
        $classID = $detail->classID;
        $sectionID = $detail->sectionID;
        $date = date('Y-m-d', strtotime($detail->att_taken_date));
        $dayName = strtolower(date('l', strtotime($date)));
        $key = $classID . '-' . $sectionID;

        if (!isset($classSectionCalendarMap[$key])) {
            continue;
        }

        $calendarId = $classSectionCalendarMap[$key];
        $calendar = $calendarDetails[$calendarId] ?? null;

        if (!$calendar || $date < $calendar->start_date || $date > $calendar->end_date) {
            continue;
        }

        if (isset($holidays[$classID][$sectionID][$date])) {
            continue;
        }

        $sessionCount = $sessionMap[$calendarId][$dayName] ?? 0;
        if ($sessionCount === 0) {
            continue;
        }

        $morning = is_numeric($detail->morning_session_status) ? (int)$detail->morning_session_status : null;
        $afternoon = is_numeric($detail->afternoon_session_status) ? (int)$detail->afternoon_session_status : null;
        $formattedDate = date('d-M-Y', strtotime($date));

        if ($morning === null && $afternoon === null) {
            continue;
        }

        $absenceExists = false;
        $absenceNote = '';
        $absenceValue = 0;

        if ($sessionCount === 1) {
            // One session = treat as full-day
            if ($morning === 0) {
                $absenceNote = $formattedDate;
                $absenceValue = 1.0;
                $absenceExists = true;
            }
        } else {
            // Two sessions = morning/afternoon tracking
            if ($morning === 0 && $afternoon === 0) {
                $absenceNote = $formattedDate;
                $absenceValue = 1.0;
                $absenceExists = true;
            } elseif ($morning === 0 && $afternoon !== 0) {
                $absenceNote = $formattedDate . ' (M)';
                $absenceValue = 0.5;
                $absenceExists = true;
            } elseif ($afternoon === 0 && $morning !== 0) {
                $absenceNote = $formattedDate . ' (A)';
                $absenceValue = 0.5;
                $absenceExists = true;
            } elseif ($morning === 0 && $afternoon === null) {
                $absenceNote = $formattedDate . ' (M)';
                $absenceValue = 0.5;
                $absenceExists = true;
            } elseif ($afternoon === 0 && $morning === null) {
                $absenceNote = $formattedDate . ' (A)';
                $absenceValue = 0.5;
                $absenceExists = true;
            }
        }

        if ($absenceExists) {
            if (!isset($absenceReport[$studentId])) {
                $absenceReport[$studentId] = [
                    'class' => $detail->class,
                    'section' => $detail->section,
                    'student_name' => $detail->student_name,
                    'admission_no' => $detail->admission_no,
                    'absent_dates' => [],
                    'total_absences' => 0,
                    'enrollment_number' => $detail->enrollment_number
                ];
            }

            $absenceReport[$studentId]['total_absences'] += $absenceValue;
            $absenceReport[$studentId]['absent_dates'][] = $absenceNote;
        }
    }

    // Filter: only students with 2 or more absences
    $filteredReport = array_filter($absenceReport, function($student) {
        return $student['total_absences'] >= 2;
    });

    return array_values($filteredReport);
}





public function getMonthwiseAttendanceReport($payload) {
    // Validate input parameters
    if (!isset($payload['from_date']) || !isset($payload['to_date']) || !isset($payload['classsecID'])) {
        return ['error' => 'Required parameters missing'];
    }

    $fromDate = date('Y-m-d', strtotime($payload['from_date']));
    $toDate = date('Y-m-d', strtotime($payload['to_date']));
    $classsecID = $payload['classsecID'];
    $parts = explode('_', $classsecID);
    $classId = isset($parts[0]) ? $parts[0] : '';
    $sectionId = isset($parts[1]) ? $parts[1] : '';

    // 1. Get academic calendar
    $this->db_readonly->select('cm.id, cm.start_date, cm.end_date');
    $this->db_readonly->from('calendar_v2_master cm');
    $this->db_readonly->join('calendar_events_v2_assigned ce', 'ce.calendar_v2_master_id = cm.id');
    $this->db_readonly->where('ce.assigned_class_id', $classId);
    $this->db_readonly->where('ce.assigned_section_id', $sectionId);
    $this->db_readonly->where('cm.academic_year', $this->yearId);
    $calendar = $this->db_readonly->get()->row();

    if (!$calendar) {
        return ['error' => 'No active academic calendar found for this class section'];
    }

    // 2. Session override map
    $sessionCounts = $this->db_readonly->select('day_name, session_count')
        ->from('calendar_events_v2_override_sessions')
        ->where('calendar_v2_master_id', $calendar->id)
                                    ->get()
                                    ->result();

    $sessionMap = [];
    foreach ($sessionCounts as $sc) {
        $sessionMap[strtolower($sc->day_name)] = (int)$sc->session_count;
    }

    // 3. Adjust date range
    $calendarStart = $calendar->start_date;
    $calendarEnd = $calendar->end_date;
    $effectiveFromDate = max($fromDate, $calendarStart);
    $effectiveToDate = min($toDate, $calendarEnd);

    if ($effectiveFromDate > $effectiveToDate) {
        return ['error' => 'The requested date range is outside the academic calendar dates'];
    }

    $allDates = $this->__generate_date_range($effectiveFromDate, $effectiveToDate);

    // 4. Get holidays and weekends
    $holidaysRaw = $this->__checkIfHoliday($effectiveFromDate, $effectiveToDate);
    $weekendRaw = $this->__getWeekendDays($classId, $sectionId);

    if (!is_array($holidaysRaw) || !is_array($weekendRaw)) {
        return ['error' => 'Holiday or weekend data missing or invalid.'];
    }

    $holidays = isset($holidaysRaw[$classId][$sectionId]) ? array_keys($holidaysRaw[$classId][$sectionId]) : [];
    $weekendDays = isset($weekendRaw[$classId][$sectionId]) ? array_keys($weekendRaw[$classId][$sectionId]) : [];

    // Prepare weekend date list
        $weekends = [];
    foreach ($allDates as $date) {
        $dayName = strtolower(date('l', strtotime($date)));
        if (in_array($dayName, $weekendDays)) {
            $weekends[] = $date;
            }
        }

    // 5. Filter working days and build maps
    $nonWorkingDays = array_unique(array_merge($holidays, $weekends));
    $workingDays = array_diff($allDates, $nonWorkingDays);
    $workingDaysMap = array_flip($workingDays);  // for fast lookup

    // 6. Organize working days month-wise
    $monthwiseWorkingDays = [];
    $monthwiseSessions = [];

    foreach ($workingDays as $wd) {
        $month = date('M-y', strtotime($wd));
        $dayName = strtolower(date('l', strtotime($wd)));
        $sessions = isset($sessionMap[$dayName]) ? $sessionMap[$dayName] : 2;

        if (!isset($monthwiseWorkingDays[$month])) {
            $monthwiseWorkingDays[$month] = 0;
            $monthwiseSessions[$month] = 0;
        }

        $monthwiseWorkingDays[$month]++;
        $monthwiseSessions[$month] += $sessions;
    }

    // 7. Get student attendance
    $this->db_readonly->select("
        s.student_admission_id,
        CONCAT(IFNULL(admission.first_name, ''), ' ', IFNULL(admission.last_name, '')) as student_name,
        admission.admission_no,
        DATE(session.att_taken_date) as att_date,
        DAYNAME(session.att_taken_date) as day_name,
        s.morning_session_status,
        s.afternoon_session_status
    ");
    $this->db_readonly->from('attendance_std_day_v2_students s');
    $this->db_readonly->join('student_admission admission', 'admission.id = s.student_admission_id');
    $this->db_readonly->join('attendance_std_day_v2_session session', 'session.id = s.attendance_day_v2_session_id');
    $this->db_readonly->where('session.acad_year_id', $this->yearId);
    $this->db_readonly->where('session.class_id', $classId);
    $this->db_readonly->where('session.class_section_id', $sectionId);
    $this->db_readonly->where("session.att_taken_date BETWEEN '$effectiveFromDate' AND '$effectiveToDate'");
    $result = $this->db_readonly->get()->result();

    // 8. Process report
    $students = [];
    foreach ($result as $row) {
        $sid = $row->student_admission_id;
        $date = $row->att_date;
        $dayName = strtolower($row->day_name);
        $monthKey = date('M-y', strtotime($date));

        if (!isset($workingDaysMap[$date])) continue;

        if (!isset($students[$sid])) {
            $students[$sid] = [
                'student_id' => $sid,
                'student_name' => $row->student_name,
                'admission_no' => $row->admission_no,
                'months' => []
    ];
}

        if (!isset($students[$sid]['months'][$monthKey])) {
            $students[$sid]['months'][$monthKey] = [
                'working_days' => 0,
                'total_sessions' => 0,
                'present_sessions' => 0,
                'absent_sessions' => 0,
                'attendance_percentage' => 0
            ];
        }

        $sessionCount = isset($sessionMap[$dayName]) ? $sessionMap[$dayName] : 2;

        $students[$sid]['months'][$monthKey]['total_sessions'] += $sessionCount;

        // Count attendance
        if ($sessionCount == 1) {
            if ($row->morning_session_status == 1) {
                $students[$sid]['months'][$monthKey]['present_sessions'] += 1;
        } else {
                $students[$sid]['months'][$monthKey]['absent_sessions'] += 1;
            }
        } elseif ($sessionCount == 2) {
            $students[$sid]['months'][$monthKey]['present_sessions'] += ($row->morning_session_status == 1 ? 0.5 : 0);
            $students[$sid]['months'][$monthKey]['absent_sessions'] += ($row->morning_session_status == 1 ? 0 : 0.5);

            $students[$sid]['months'][$monthKey]['present_sessions'] += ($row->afternoon_session_status == 1 ? 0.5 : 0);
            $students[$sid]['months'][$monthKey]['absent_sessions'] += ($row->afternoon_session_status == 1 ? 0 : 0.5);
        }
    }

    // 9. Final report preparation
    $finalReport = [
        'calendar_start' => $calendarStart,
        'calendar_end' => $calendarEnd,
        'report_start' => $effectiveFromDate,
        'report_end' => $effectiveToDate,
        'students' => []
    ];

    foreach ($students as &$student) {
        // Calculate yearly totals for this student
        $yearly_total_sessions = 0;
        $yearly_present_sessions = 0;

        foreach ($student['months'] as $month => &$data) {
            $data['working_days'] = isset($monthwiseWorkingDays[$month]) ? $monthwiseWorkingDays[$month] : 0;
            $data['total_sessions'] = isset($monthwiseSessions[$month]) ? $monthwiseSessions[$month] : 0;
            $data['attendance_percentage'] = ($data['total_sessions'] > 0)
            ? round(($data['present_sessions'] / (float)$data['total_sessions']) * 100, 1)
            : 0;

            // Add to yearly totals
            $yearly_total_sessions += $data['total_sessions'];
            $yearly_present_sessions += $data['present_sessions'];
        }

        // Calculate yearly percentage
        $yearly_percentage = ($yearly_total_sessions > 0)
            ? round(($yearly_present_sessions / (float)$yearly_total_sessions) * 100, 1)
            : 0;

        $student['yearly_percentage'] = $yearly_percentage;
        $student['yearly_total_sessions'] = $yearly_total_sessions;
        $student['yearly_present_sessions'] = $yearly_present_sessions;

        $finalReport['students'][] = $student;
    }

    return $finalReport;
  }


private function __getWeekendDays($classID, $sectionID) {
    $this->db_readonly->select('cs.day_name, assigned.assigned_class_id, assigned.assigned_section_id, cs.session_count');
    $this->db_readonly->from('calendar_events_v2_override_sessions as cs');
    $this->db_readonly->join('calendar_events_v2_assigned as assigned', 'assigned.calendar_v2_master_id =cs.calendar_v2_master_id');
    $this->db_readonly->where('cs.session_count', 0);
    $this->db_readonly->where('assigned.assigned_class_id', $classID);
    $this->db_readonly->where('assigned.assigned_section_id', $sectionID);
    
    $weekends = $this->db_readonly->get()->result();

    $weekendsArray = [];
    foreach ($weekends as $row) {
        $day = strtolower($row->day_name);
        $weekendsArray[$row->assigned_class_id][$row->assigned_section_id][$day] = 0;
            }

    return $weekendsArray;
}

    public function getSpecialCaseReport($payload) {
        // Validate input parameters
        if (!isset($payload['selectedDate'])) {
            return ['error' => 'Selected date is required'];
        }

        $formattedDate = date('Y-m-d', strtotime($payload['selectedDate']));
        $classsecID = !empty($payload['classsecId']) ? $payload['classsecId'] : null;
        $selectedRemark = !empty($payload['report_remarks']) ? $payload['report_remarks'] : '';

        if (empty($classsecID)) {
            return ['error' => 'No class/section selected'];
        }

        $sections = [];
        $classes = [];

    // Get all sections/classes
        if (is_array($classsecID) && in_array('all_all', $classsecID)) {
            $sectionsData = $this->db_readonly->select('cs.id as section_id, cs.class_id as class_id')
                ->from('class_section cs')
                ->join('class c', 'c.id = cs.class_id')
                ->where('c.acad_year_id', $this->yearId)
                ->get()->result();
            foreach ($sectionsData as $row) {
                $sections[] = $row->section_id;
                $classes[] = $row->class_id;
            }
        } else {
            foreach ($classsecID as $csID) {
                if (strpos($csID, '_') !== false) {
                    list($class_id, $section_id) = explode('_', $csID);
                    $classes[] = $class_id;
                    $sections[] = $section_id;
                }
            }
        }

        if (empty($sections) || empty($classes)) {
            return ['error' => 'No valid class/section selected'];
        }

    // Get calendar assignments
        $calendarAssignments = $this->db_readonly->select('cea.calendar_v2_master_id, cea.assigned_class_id, cea.assigned_section_id')
            ->from('calendar_events_v2_assigned cea')
            ->join('calendar_v2_master cm', 'cm.id = cea.calendar_v2_master_id')
            ->where('cm.academic_year', $this->yearId)
            
            ->where_in('cea.assigned_section_id', $sections)
            ->get()->result();

        if (empty($calendarAssignments)) {
            return ['error' => 'No calendar assignments found'];
        }

        $calendarMap = [];
        foreach ($calendarAssignments as $row) {
            $key = $row->assigned_class_id . '_' . $row->assigned_section_id;
            $calendarMap[$key] = $row->calendar_v2_master_id;
        }
        // Get session counts
        $sessionCounts = $this->db_readonly->select('calendar_v2_master_id, day_name, session_count')
            ->from('calendar_events_v2_override_sessions')
            ->where_in('calendar_v2_master_id', array_column($calendarAssignments, 'calendar_v2_master_id'))
            ->get()->result();

        $sessionMap = [];
        foreach ($sessionCounts as $sc) {
            $sessionMap[$sc->calendar_v2_master_id][strtolower($sc->day_name)] = (int)$sc->session_count;
        }
        // Get holiday map
        $holidays = $this->__checkIfHoliday($formattedDate, $formattedDate); // Only 1 day range

        // Get attendance data
        $this->db_readonly->select("
            year.roll_no,
            admission.admission_no,
            IFNULL(admission.enrollment_number, '-') as enrollment_number,
            student.is_late,
            CONCAT(IFNULL(admission.first_name, ''), ' ', IFNULL(admission.last_name, '')) as studentName,

            c.class_name,
            cs.section_name,
            student.morning_session_status,
            student.afternoon_session_status,
            session.att_taken_date,
            year.student_admission_id,
            c.id as class_id,
            cs.id as section_id
        ");
        $this->db_readonly->from('attendance_std_day_v2_session session');
        $this->db_readonly->join('attendance_std_day_v2_students student', 'student.attendance_day_v2_session_id = session.id');
        $this->db_readonly->join('student_admission admission', 'admission.id = student.student_admission_id');
        $this->db_readonly->join('student_year year', 'year.student_admission_id = admission.id AND year.acad_year_id = ' . $this->yearId);
        $this->db_readonly->join('class c', 'c.id = year.class_id');
        $this->db_readonly->join('class_section cs', 'cs.id = year.class_section_id');
        $this->db_readonly->where('DATE(session.att_taken_date)', $formattedDate);
        // $this->db_readonly->where_in('c.id', $classes);
        $this->db_readonly->where_in('cs.id', $sections);

        $results = $this->db_readonly->get()->result();
        if (empty($results)) {
            return [];
        }
        // Final filter
        $dayName = strtolower(date('l', strtotime($formattedDate)));
        $filteredResults = [];
        $x= 1;
        foreach ($results as $row) {
            $key = $row->class_id . '_' . $row->section_id;
            if (!isset($calendarMap[$key])) continue;
            $calendarId = $calendarMap[$key];
            // Check session count
            $sessionCount = isset($sessionMap[$calendarId][$dayName]) ? $sessionMap[$calendarId][$dayName] : 0;
            if ($sessionCount == 0) continue;
            
            // Check holiday
            if (isset($holidays[$row->class_id][$row->section_id][$formattedDate])) {
                continue;
            }
            
            // Attendance status logic
            $attendanceStatus = '';
            if ($sessionCount == 1) {

                $attendanceStatus = ($row->morning_session_status == 0) ? 'Absent' : 'Present';
                if ($row->is_late == 1) $attendanceStatus = 'Late';
            } else if($sessionCount == 2) {
                if ($row->morning_session_status == 0 && $row->afternoon_session_status == 0) {
                    $attendanceStatus = 'Absent';
                } elseif ($row->morning_session_status == 0) {
                    $attendanceStatus = 'Morning Absent';
                } elseif ($row->afternoon_session_status == 0) {
                    $attendanceStatus = 'Afternoon Absent';
                } elseif ($row->is_late == 1) {
                    $attendanceStatus = 'Late';
                }
            } else {
                continue; // Invalid session count
            }
            
            $row->attendance_status = $attendanceStatus;
            if($row->attendance_status == 'Present' || trim($row->attendance_status) == '') {continue;}
            $filteredResults[] = $row;
        }
        
        return $filteredResults;
}

    public function get_emergency_exit_records($from_date, $to_date, $class_section_id = '') {
        $this->db_readonly->select("asdvs.id as emergency_exit_id, 
            concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, 
            CONCAT(sec.class_name, ' ', sec.section_name) as class_section, 
            ifnull(asdvs.emergency_exit_remarks, '-') as emergency_exit_remarks, 
            asdvs.emergency_exit_pickup_by, 
            CASE 
                WHEN asdvs.emergency_exit_pickup_by = 'Father' THEN concat(ifnull(p_father.first_name, ''), ' ', ifnull(p_father.last_name, ''))
                WHEN asdvs.emergency_exit_pickup_by = 'Mother' THEN concat(ifnull(p_mother.first_name, ''), ' ', ifnull(p_mother.last_name, ''))
                WHEN asdvs.emergency_exit_pickup_by = 'Other' THEN asdvs.emergency_exit_pickup_by_other_name
                ELSE '-'
            END as pickup_name,
            CASE 
                WHEN asdvs.emergency_exit_pickup_by = 'Father' THEN p_father.picture_url
                WHEN asdvs.emergency_exit_pickup_by = 'Mother' THEN p_mother.picture_url
                WHEN asdvs.emergency_exit_pickup_by = 'Other' THEN asdvs.emergency_exit_pickup_by_other_photo
                ELSE NULL
            END as photo_url,
            CASE 
                WHEN asdvs.emergency_exit_pickup_by = 'Father' THEN ifnull(p_father.mobile_no, '-')
                WHEN asdvs.emergency_exit_pickup_by = 'Mother' THEN ifnull(p_mother.mobile_no, '-')
                WHEN asdvs.emergency_exit_pickup_by = 'Other' THEN '-'
                ELSE '-'
            END as parent_contact,
            Date_format(asdvs.emergency_exit_time, '%d-%b-%Y %h:%i %p') as emergency_exit_time,
            concat(ifnull(sm.first_name, '-'), ' ', ifnull(sm.last_name, '-')) as allowed_by_name,
            concat(ifnull(p_authorized.first_name, ''), ' ', ifnull(p_authorized.last_name, '')) as authorized_by_name,
            ifnull(tth.thing_name, 'No Transport') as transport_route")
                ->from('attendance_std_day_v2_students asdvs')
                ->where('asdvs.is_emergency_exit', 1)
                ->join('attendance_std_day_v2_session asdvs_session', 'asdvs_session.id = asdvs.attendance_day_v2_session_id', 'left')
                ->join('student_relation sr_father', 'sr_father.std_id = asdvs.student_admission_id AND sr_father.relation_type = "Father"', 'left')
                ->join('parent p_father', 'p_father.id = sr_father.relation_id', 'left')
                ->join('student_relation sr_mother', 'sr_mother.std_id = asdvs.student_admission_id AND sr_mother.relation_type = "Mother"', 'left')
                ->join('parent p_mother', 'p_mother.id = sr_mother.relation_id', 'left')
                ->join('student_admission sa', 'sa.id = asdvs.student_admission_id', 'left')
                ->join('class_section sec', 'sec.id = asdvs_session.class_section_id', 'left')
                ->join('avatar av', 'av.id = asdvs.emergency_exit_allowed_by and av.avatar_type = 4', 'left')
                ->join('staff_master sm', 'sm.id = av.stakeholder_id', 'left')
                ->join('avatar av_authorized', 'av_authorized.id = asdvs.emergency_exit_authorized_by and av_authorized.avatar_type = 2', 'left')
                ->join('parent p_authorized', 'p_authorized.id = av_authorized.stakeholder_id', 'left')
                ->join('tx_student_journeys tsj', 'tsj.entity_source_id = sa.id AND tsj.entity_type = "Student" AND tsj.day=dayname(asdvs.emergency_exit_time) and tsj.journey_type = "DROPPING"', 'left')
                ->join('tx_journeys tj', 'tj.id = tsj.journey_id', 'left')
                ->join('tx_things tth', 'tth.id = tj.thing_id', 'left');
        
        if (!empty($from_date) && !empty($to_date)) {
            $this->db_readonly->where('DATE(asdvs.emergency_exit_time) >=', $from_date);
            $this->db_readonly->where('DATE(asdvs.emergency_exit_time) <=', $to_date);
        }
        
        if (!empty($class_section_id)) {
            $this->db_readonly->where('asdvs_session.class_section_id', $class_section_id);
        }
        
        $this->db_readonly->order_by('asdvs.emergency_exit_time', 'DESC');
        
        $query = $this->db_readonly->get()->result();

        return $query;
  }

    public function get_present_students($class_section_id, $date) {
        $this->db_readonly->select("sa.id as student_admission_id,
            CONCAT(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name")
            ->from("attendance_std_day_v2_students asdv")
            ->join("attendance_std_day_v2_session asdvs_session", "asdvs_session.id = asdv.attendance_day_v2_session_id")
            ->join("student_admission sa", "sa.id = asdv.student_admission_id")
            ->join("student_year sy", "sy.student_admission_id = sa.id")
            ->where("asdvs_session.class_section_id", $class_section_id)
            ->where("asdvs_session.att_taken_date", $date)
            ->where("sy.acad_year_id", $this->yearId)
            ->where("sy.class_section_id", $class_section_id) // Ensure student belongs to the selected section
            ->where("(asdv.morning_session_status=1 or asdv.afternoon_session_status=1)")
            ->where("asdv.is_emergency_exit != 1")
            ->order_by("sa.first_name", "ASC");

        $query = $this->db_readonly->get()->result();

        return $query;
    }

    public function get_parent_info($student_id, $pickup_by) {
        $this->load->library('filemanager');
        
        $this->db_readonly->select("CONCAT(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as name, p.picture_url as parent_photo, p.mobile_no as parent_mobile")
            ->from("student_relation sr")
            ->join("parent p", "p.id = sr.relation_id")
            ->where("sr.std_id", $student_id)
            ->where("sr.relation_type", ucfirst($pickup_by));
        $query = $this->db_readonly->get()->row();

        if ($query) {
            $query->parent_photo = $this->filemanager->getFilePath($query->parent_photo);
        }

        return $query;
    }

    public function get_both_parents_info($student_id) {
        $this->load->library('filemanager');

        // Get both father and mother information including avatar_id for parent_id
        // Using INNER JOIN for avatar to ensure we only get parents with avatar records
        $this->db_readonly->select("sr.relation_type, CONCAT(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as name, p.picture_url as parent_photo, p.mobile_no as mobile, a.id as parent_id")
            ->from("student_relation sr")
            ->join("parent p", "p.id = sr.relation_id")
            ->join("avatar a", "a.stakeholder_id = p.id AND a.avatar_type = 2", 'inner') // avatar_type 2 for parents
            ->where("sr.std_id", $student_id)
            ->where_in("sr.relation_type", ['Father', 'Mother']);

        $query = $this->db_readonly->get()->result();

        // Debug logging (remove in production)
        // log_message('debug', 'get_both_parents_info - Student ID: ' . $student_id);
        // log_message('debug', 'get_both_parents_info - Query result: ' . print_r($query, true));

        $result = ['father' => null, 'mother' => null];

        // If no parents found with avatar records, try fallback query without avatar requirement
        if (empty($query)) {
            // log_message('debug', 'get_both_parents_info - No parents with avatars found, trying fallback query');

            $this->db_readonly->select("sr.relation_type, CONCAT(ifnull(p.first_name, ''), ' ', ifnull(p.last_name, '')) as name, p.picture_url as parent_photo, p.mobile_no as mobile, NULL as parent_id")
                ->from("student_relation sr")
                ->join("parent p", "p.id = sr.relation_id")
                ->where("sr.std_id", $student_id)
                ->where_in("sr.relation_type", ['Father', 'Mother']);

            $query = $this->db_readonly->get()->result();
            // log_message('debug', 'get_both_parents_info - Fallback query result: ' . print_r($query, true));
        }

        foreach ($query as $parent) {
            $parent_data = [
                'parent_id' => $parent->parent_id, // Include parent_id (avatar_id) - may be null
                'name' => $parent->name,
                'mobile' => $parent->mobile,
                'photo' => $this->filemanager->getFilePath($parent->parent_photo)
            ];

            if ($parent->relation_type === 'Father') {
                $result['father'] = $parent_data;
            } elseif ($parent->relation_type === 'Mother') {
                $result['mother'] = $parent_data;
            }
        }

        // Debug logging (remove in production)
        // log_message('debug', 'get_both_parents_info - Final result: ' . print_r($result, true));

        return $result;
    }

    public function save_emergency_exit() {

        $input = $this->input->post();

        // Validate that input is an array
        if (!is_array($input) || empty($input)) {
            return ['error' => 'Invalid input data'];
        }

        // echo '<pre>'; print_r($input); die();

        // Validate required fields
        if (!isset($input['exit_datetime']) || empty($input['exit_datetime'])) {
            return ['error' => 'Exit datetime is required'];
        }

        if (!isset($input['pickup_by']) || empty($input['pickup_by'])) {
            return ['error' => 'Pickup by information is required'];
        }

        if (!isset($input['student_id']) || empty($input['student_id'])) {
            return ['error' => 'Student ID is required'];
        }

        if ($input['pickup_by'] == 'Other') {
            $pickup_by_other_photo = isset($input['uploaded_photo_url']) ? $input['uploaded_photo_url'] : '';
            if (!isset($input['pickup_other_name']) || empty($input['pickup_other_name'])) {
                return ['error' => 'Pickup person name is required when pickup by is Other'];
            }
        } else {
            $pickup_by_other_photo = '';
        }

        // Format emergency_exit_time to MySQL datetime format
        $emergency_exit_time = date('Y-m-d H:i:s', strtotime($input['exit_datetime']));
        $emergency_exit_date = date('Y-m-d', strtotime($input['exit_datetime']));

        //Get the attendance_std_day_v2_students id
        $this->db_readonly->select("asdv.id")
            ->from("attendance_std_day_v2_students asdv")
            ->join("attendance_std_day_v2_session asdvs_session", "asdvs_session.id = asdv.attendance_day_v2_session_id and asdvs_session.att_taken_date='$emergency_exit_date'")
            ->where("asdv.student_admission_id", $input['student_id']);
        $query = $this->db_readonly->get()->row();

        if (empty($query)) {
            return false;
        }

        $data = array(
            'student_admission_id' => $input['student_id'],
            'emergency_exit_time' => $emergency_exit_time,
            'emergency_exit_pickup_by' => $input['pickup_by'],
            'emergency_exit_pickup_by_other_name' => ($input['pickup_by'] == 'Other') ? $input['pickup_other_name'] : '',
            'emergency_exit_pickup_by_other_photo' => $pickup_by_other_photo,
            'emergency_exit_remarks' => isset($input['remarks']) ? $input['remarks'] : '',
            'emergency_exit_allowed_by' => $this->authorization->getAvatarId(),
            'emergency_exit_authorized_by' => isset($input['authorized_by_parent_id']) ? $input['authorized_by_parent_id'] : null,
            'emergency_exit_authorized_by_mobile' => isset($input['authorized_by_mobile']) ? $input['authorized_by_mobile'] : '',
            'emergency_exit_otp_verified_at' => date('Y-m-d H:i:s'),
            'is_emergency_exit' => 1
        );
        
        // Update the student_attendance_day_v2 record
        $this->db->where('id', $query->id)
                 ->update('attendance_std_day_v2_students', $data);
        
        // Send notification if enabled
        if($this->settings->getSetting('emergency_exit_student_notification')){
            $this->load->helper('texting_helper');
            $input_arr = array();
            $input_arr['student_ids'] = [$input['student_id']];
            $input_arr['mode'] = 'notification';
            $input_arr['source'] = 'Emergency Exit';
            $input_arr['message'] = "Your child left early from school today.";
            $input_arr['title'] = 'Emergency Exit';
            sendText($input_arr);
        }
        
        return true;
        }

    private function upload_photo() {
        $config['upload_path'] = './uploads/emergency_exit/';
        $config['allowed_types'] = 'gif|jpg|jpeg|png';
        $config['max_size'] = 2048;
        $config['encrypt_name'] = TRUE;
        
        $this->load->library('upload', $config);
        
        if (!$this->upload->do_upload('pickup_photo')) {
            return '';
            } else {
            $upload_data = $this->upload->data();
            return 'uploads/emergency_exit/' . $upload_data['file_name'];
}
    }

    public function get_emergency_exit_details($id) {
        $this->load->library('filemanager');

        $this->db_readonly->select("asdvs.id as emergency_exit_id,
            concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name,
            CONCAT(sec.class_name, ' ', sec.section_name) as class_section,
            ifnull(asdvs.emergency_exit_remarks, '-') as emergency_exit_remarks,
            asdvs.emergency_exit_pickup_by,
            CASE
                WHEN asdvs.emergency_exit_pickup_by = 'Father' THEN concat(ifnull(p_father.first_name, ''), ' ', ifnull(p_father.last_name, ''))
                WHEN asdvs.emergency_exit_pickup_by = 'Mother' THEN concat(ifnull(p_mother.first_name, ''), ' ', ifnull(p_mother.last_name, ''))
                WHEN asdvs.emergency_exit_pickup_by = 'Other' THEN asdvs.emergency_exit_pickup_by_other_name
                ELSE '-'
            END as pickup_name,
            CASE
                WHEN asdvs.emergency_exit_pickup_by = 'Father' THEN p_father.picture_url
                WHEN asdvs.emergency_exit_pickup_by = 'Mother' THEN p_mother.picture_url
                WHEN asdvs.emergency_exit_pickup_by = 'Other' THEN asdvs.emergency_exit_pickup_by_other_photo
                ELSE NULL
            END as photo_url,
            CASE
                WHEN asdvs.emergency_exit_pickup_by = 'Father' THEN ifnull(p_father.mobile_no, '-')
                WHEN asdvs.emergency_exit_pickup_by = 'Mother' THEN ifnull(p_mother.mobile_no, '-')
                WHEN asdvs.emergency_exit_pickup_by = 'Other' THEN '-'
                ELSE '-'
            END as parent_contact,
            Date_format(asdvs.emergency_exit_time, '%d-%b-%Y %h:%i %p') as emergency_exit_time,
            concat(ifnull(sm.first_name, '-'), ' ', ifnull(sm.last_name, '-')) as allowed_by_name,
            concat(ifnull(p_authorized.first_name, ''), ' ', ifnull(p_authorized.last_name, '')) as authorized_by_name,
            ifnull(tth.thing_name, 'No Transport') as transport_route")
            ->from('attendance_std_day_v2_students asdvs')
            ->where('asdvs.id', $id)
            ->where('asdvs.is_emergency_exit', 1)
            ->join('attendance_std_day_v2_session asdvs_session', 'asdvs_session.id = asdvs.attendance_day_v2_session_id', 'left')
            ->join('student_relation sr_father', 'sr_father.std_id = asdvs.student_admission_id AND sr_father.relation_type = "Father"', 'left')
            ->join('parent p_father', 'p_father.id = sr_father.relation_id', 'left')
            ->join('student_relation sr_mother', 'sr_mother.std_id = asdvs.student_admission_id AND sr_mother.relation_type = "Mother"', 'left')
            ->join('parent p_mother', 'p_mother.id = sr_mother.relation_id', 'left')
            ->join('student_admission sa', 'sa.id = asdvs.student_admission_id', 'left')
            ->join('class_section sec', 'sec.id = asdvs_session.class_section_id', 'left')
            ->join('avatar av', 'av.id = asdvs.emergency_exit_allowed_by and av.avatar_type = 4', 'left')
            ->join('staff_master sm', 'sm.id = av.stakeholder_id', 'left')
            ->join('avatar av_authorized', 'av_authorized.id = asdvs.emergency_exit_authorized_by and av_authorized.avatar_type = 2', 'left')
            ->join('parent p_authorized', 'p_authorized.id = av_authorized.stakeholder_id', 'left')
            ->join('tx_student_journeys tsj', 'tsj.entity_source_id = sa.id AND tsj.entity_type = "Student" AND day=dayname(asdvs.emergency_exit_time) and tsj.journey_type = "DROPPING"', 'left')
            ->join('tx_journeys tj', 'tj.id = tsj.journey_id', 'left')
            ->join('tx_things tth', 'tth.id = tj.thing_id', 'left');
        
        $query = $this->db_readonly->get()->row();
        
        if ($query) {
            // If photo_url is empty, set it to null
            if (empty($query->photo_url)) {
                $query->photo_url = null;
        } else {
                // Get the full file path using filemanager
                $query->photo_url = $this->filemanager->getFilePath($query->photo_url);
                // Add base URL if the path is relative
                if (strpos($query->photo_url, 'http') !== 0) {
                    $query->photo_url = base_url($query->photo_url);
        }
    }
        }
        return $query;
    }

    public function getStdInfo($stdId){
		if (empty($stdId)) {
			return false;
		} else{ 
			return $this->db->select("sd.id,CONCAT(ifnull(sd.first_name,''), ' ', ifnull(sd.last_name,'')) AS std_name,concat(c.class_name, cs.section_name) as csName")
				->from('student_admission sd')
				->join('student_year ss','sd.id = ss.student_admission_id')
				->where('sd.admission_status','2') // Approved 2
				->join('class c','c.id = ss.class_id')
				->join('class_section cs','ss.class_section_id = cs.id','left')
				->where_in('sd.id',$stdId)
				->get()->result();
		}
}

    public function getSummaryDetailsStdWise($studentId){
        $student = $this->db_readonly->select('sy.class_id, sy.class_section_id')
            ->from('student_year sy')
            ->where('sy.student_admission_id', $studentId)
            ->where('sy.acad_year_id', $this->yearId)
            ->get()->row();
        if (!$student) return [
            'present_days' => 0,
            'absent_days' => 0,
            'holidays' => 0,
            'total_days' => 0
        ];
        $classId = $student->class_id;
        $sectionId = $student->class_section_id;

        $calendar = $this->db_readonly->select('cm.start_date, cm.end_date')
          ->from('calendar_v2_master cm')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = cm.id')
            ->where('cea.assigned_section_id', $sectionId)
          ->where('cm.academic_year', $this->yearId)
            ->get()->row();

        if (!$calendar) {
        return [
                'present_days' => 0,
                'absent_days' => 0,
                'holidays' => 0,
                'total_days' => 0
            ];
        }

        $today = date('Y-m-d');
        $calendar_end = ($calendar->end_date > $today) ? $today : $calendar->end_date;

        $attendance = $this->db_readonly->select('s.morning_session_status, s.afternoon_session_status, sess.att_taken_date, sess.id as session_id, s.is_late')
            ->from('attendance_std_day_v2_students s')
            ->join('attendance_std_day_v2_session sess', 'sess.id = s.attendance_day_v2_session_id')
            ->where('s.student_admission_id', $studentId)
            ->where('sess.class_id', $classId)
            ->where('sess.class_section_id', $sectionId)
            ->where('sess.acad_year_id', $this->yearId)
            ->where('sess.att_taken_date >=', $calendar->start_date)
            ->where('sess.att_taken_date <=', $calendar_end)
            ->get()->result();
        $present = 0;
        $absent = 0;
        $late_count = 0;
        $dates = [];
        foreach ($attendance as $row) {
            $date = $row->att_taken_date;
            $dates[$date] = true;
            $dayName = date('l', strtotime($date));
            $sessionCountObj = $this->db_readonly->select('os.session_count')
                ->from('calendar_events_v2_override_sessions os')
                ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = os.calendar_v2_master_id')
                ->where('cea.assigned_section_id', $sectionId)
                ->where('os.day_name', $dayName)
                ->get()->row();
            $sessionCount = $sessionCountObj ? (int)$sessionCountObj->session_count : 2;
            if ($sessionCount == 1) {
                if ($row->morning_session_status == 1) {
                    $present += 1;
                } else {
                    $absent += 1;
                }
            } else {
                $morning = $row->morning_session_status;
                $afternoon = $row->afternoon_session_status;
                if ($morning == 1 && $afternoon == 1) {
                    $present += 1;
                } elseif ($morning == 0 && $afternoon == 0) {
                    $absent += 1;
                } else if (($morning == 1 && $afternoon == 0) || ($morning == 0 && $afternoon == 1)) {
                    $present += 0.5;
                    $absent += 0.5;
                }
            }
            if (isset($row->is_late) && $row->is_late == 1) {
                $late_count++;
            }
        }

        // Calculate all dates in the period (inclusive)
        $period = new DatePeriod(
            new DateTime($calendar->start_date),
            new DateInterval('P1D'),
            (new DateTime($calendar_end))->modify('+1 day')
        );
        $allDates = [];
        foreach ($period as $dt) {
            $allDates[] = $dt->format('Y-m-d');
        }
        $total_days = count($allDates);

        // Calculate holidays in the period
        $holidays = $this->db_readonly->select('ce.from_date, ce.to_date')
            ->from('calendar_events_v2 ce')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
        ->where('cea.assigned_section_id', $sectionId)
            ->where_in('ce.event_type', ['holiday', 'holiday_range'])
        ->where('ce.from_date <=', $calendar_end)
        ->where('ce.to_date >=', $calendar->start_date)
        ->get()->result();

        $holidayDates = [];
        foreach ($holidays as $h) {
            $interval = new DateInterval('P1D');
            $start = new DateTime($h->from_date);
            $end = new DateTime($h->to_date);
            $daterange = new DatePeriod($start, $interval, $end->modify('+1 day'));
            foreach ($daterange as $dt) {
                $holidayDates[$dt->format('Y-m-d')] = true;
            }
        }
        $holidayCount = count($holidayDates); // Ensure $holidayCount is always defined

        // Calculate month-wise breakdown
        $monthlyData = [];
        foreach ($attendance as $row) {
            $date = $row->att_taken_date;
            $monthKey = date('M-y', strtotime($date));

            if (!isset($monthlyData[$monthKey])) {
                $monthlyData[$monthKey] = [
                    'present_sessions' => 0,
                    'absent_sessions' => 0,
                    'total_sessions' => 0,
                    'working_days' => 0
                ];
            }

            $dayName = date('l', strtotime($date));
            $sessionCountObj = $this->db_readonly->select('os.session_count')
                ->from('calendar_events_v2_override_sessions os')
                ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = os.calendar_v2_master_id')
                ->where('cea.assigned_section_id', $sectionId)
                ->where('os.day_name', $dayName)
                ->get()->row();
            $sessionCount = $sessionCountObj ? (int)$sessionCountObj->session_count : 2;

            $monthlyData[$monthKey]['total_sessions'] += $sessionCount;

            if ($sessionCount == 1) {
                if ($row->morning_session_status == 1) {
                    $monthlyData[$monthKey]['present_sessions'] += 1;
                } else {
                    $monthlyData[$monthKey]['absent_sessions'] += 1;
                }
            } else {
                $morning = $row->morning_session_status;
                $afternoon = $row->afternoon_session_status;
                if ($morning == 1 && $afternoon == 1) {
                    $monthlyData[$monthKey]['present_sessions'] += 1;
                } elseif ($morning == 0 && $afternoon == 0) {
                    $monthlyData[$monthKey]['absent_sessions'] += 1;
                } else if (($morning == 1 && $afternoon == 0) || ($morning == 0 && $afternoon == 1)) {
                    $monthlyData[$monthKey]['present_sessions'] += 0.5;
                    $monthlyData[$monthKey]['absent_sessions'] += 0.5;
                }
            }
        }

        // Calculate percentages for each month
        foreach ($monthlyData as $month => &$data) {
            $data['attendance_percentage'] = ($data['total_sessions'] > 0)
                ? round(($data['present_sessions'] / $data['total_sessions']) * 100, 1)
                : 0;
        }

        return [
            'present_days' => $present,
            'absent_days' => $absent,
            'holidays' => $holidayCount,
            'total_days' => $total_days,
            'late_count' => $late_count,
            'monthly_data' => $monthlyData
        ];
    }

    
    public function getAttendanceDataByDateRange($start, $end, $student_id) {
        $start = (new DateTime($start))->format('Y-m-d');
        $end = (new DateTime($end))->format('Y-m-d');
    
      
        $attendance = $this->db_readonly->select('sess.att_taken_date, s.morning_session_status, s.afternoon_session_status')
            ->from('attendance_std_day_v2_students s')
            ->join('attendance_std_day_v2_session sess', 'sess.id = s.attendance_day_v2_session_id')
            ->where('s.student_admission_id', $student_id)
            ->where('sess.att_taken_date >=', $start)
            ->where('sess.att_taken_date <=', $end)
            ->get()->result();
    
      
        $student = $this->db_readonly->select('sy.class_section_id')
            ->from('student_year sy')
            ->where('sy.student_admission_id', $student_id)
            ->where('sy.acad_year_id', $this->yearId)
            ->get()->row();
        $sectionId = $student ? $student->class_section_id : 0;
    

        $holidays = $this->db_readonly->select('ce.from_date, ce.to_date')
            ->from('calendar_events_v2 ce')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
            ->where('cea.assigned_section_id', $sectionId)
            ->where_in('ce.event_type', ['holiday', 'holiday_range'])
            ->where('ce.from_date <=', $end)
            ->where('ce.to_date >=', $start)
            ->get()->result();

        $holidayDates = [];
        foreach ($holidays as $h) {
            $interval = new DateInterval('P1D');
            $startH = new DateTime($h->from_date);
            $endH = new DateTime($h->to_date);
            $daterange = new DatePeriod($startH, $interval, $endH->modify('+1 day'));
            foreach ($daterange as $dt) {
                $holidayDates[$dt->format('Y-m-d')] = true;
            }
        }
    
        $weekoffDays = [];
        $weekoffQuery = $this->db_readonly->select('os.day_name')
            ->from('calendar_events_v2_override_sessions os')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = os.calendar_v2_master_id')
            ->where('cea.assigned_section_id', $sectionId)
            ->where('os.session_count', 0)
            ->get()->result();
    
        foreach ($weekoffQuery as $row) {
            $weekoffDays[] = $row->day_name;
        }
    
       
        $events = [];
        $existingDates = [];
    
        foreach ($attendance as $row) {
            $date = $row->att_taken_date;
            $title = '';
            $className = '';
            if ($row->morning_session_status == 1 && $row->afternoon_session_status == 1) {
                $title = 'Present';
                $className = 'present';
            } elseif ($row->morning_session_status == 0 && $row->afternoon_session_status == 0) {
                $title = 'Absent';
                $className = 'absent';
    } else {
                $title = 'Half Day';
                $className = 'halfday';
            }
            $events[] = [
                'title' => $title,
                'start' => $date,
                'className' => $className,
                'allDay' => true
            ];
            $existingDates[$date] = true;
        }
    
        
        foreach ($holidayDates as $date => $v) {
            if (!isset($existingDates[$date])) {
                $events[] = [
                    'title' => 'Holiday',
                    'start' => $date,
                    'className' => 'holiday',
                    'allDay' => true
                ];
                $existingDates[$date] = true;
            }
        }
    
    
        $period = new DatePeriod(new DateTime($start), new DateInterval('P1D'), (new DateTime($end))->modify('+1 day'));
        foreach ($period as $dt) {
            $date = $dt->format('Y-m-d');
            $dayName = $dt->format('l');
            if (in_array($dayName, $weekoffDays) && !isset($existingDates[$date])) {
                $events[] = [
                    'title' => 'Week - Off',
                    'start' => $date,
                    'className' => 'weekoff',
                    'allDay' => true
                ];
                $existingDates[$date] = true;
        }
    }

        return $events;
        }

    public function getAttendanceMissingReportData($input) {
        $selectedDate = date('Y-m-d', strtotime($input['selected_date']));
        $classsecID = $input['classsecID'];

        $report = [];

        if ($classsecID == 'all') {
            // Get all class sections
            $class_sections = $this->getAllClassSection();
        } else {
            // Get specific class section
            list($class_id, $section_id) = explode('_', $classsecID);
            $class_sections = $this->db_readonly->select('c.id as classID, cs.id as sectionID, c.class_name as class_name, cs.section_name as section_name')
                ->from('class c')
                ->join('class_section cs', 'cs.class_id = c.id')
                ->where('c.id', $class_id)
                ->where('cs.id', $section_id)
                ->where('c.acad_year_id', $this->yearId)
                ->get()->result();
        }

        foreach ($class_sections as $index => $class_section) {
            // Get total students in class
            $total_students = $this->db_readonly->select('COUNT(DISTINCT sy.student_admission_id) as total')
                ->from('student_year sy')
                ->join('student_admission sa', 'sa.id = sy.student_admission_id')
                ->where('sy.class_id', $class_section->classID)
                ->where('sy.class_section_id', $class_section->sectionID)
                ->where('sy.acad_year_id', $this->yearId)
                ->group_start()
                    ->group_start()
                        ->where('sa.admission_status', 2)
                        ->or_where('sa.admission_status', 4)
                        ->or_where('sa.admission_status', 5)
                    ->group_end()
                    ->or_where('sy.promotion_status', 'JOINED')
                ->group_end()
                ->get()->row();

            // Get students with attendance taken on specific date
            $attendance_taken = $this->db_readonly->select('COUNT(DISTINCT asds.student_admission_id) as taken')
                ->from('attendance_std_day_v2_students asds')
                ->join('attendance_std_day_v2_session atses', 'atses.id = asds.attendance_day_v2_session_id')
                ->where('atses.class_id', $class_section->classID)
                ->where('atses.class_section_id', $class_section->sectionID)
                ->where('atses.att_taken_date', $selectedDate)
                ->where('atses.acad_year_id', $this->yearId)
                ->get()->row();

            $report[] = [
                'sr_no' => $index + 1,
                'class_name' => $class_section->class_name . ' ' . $class_section->section_name,
                'class_id' => $class_section->classID,
                'section_id' => $class_section->sectionID,
                'total_students' => $total_students ? $total_students->total : 0,
                'attendance_taken' => $attendance_taken ? $attendance_taken->taken : 0,
                'selected_date' => $input['selected_date']
            ];
        }

        return $report;
    }

    public function getMissingStudentsList($input) {
        $selectedDate = date('Y-m-d', strtotime($input['selected_date']));
        $class_id = $input['class_id'];
        $section_id = $input['section_id'];

        // Get all students in the class
        $all_students = $this->db_readonly->select('sa.id, CONCAT(IFNULL(sa.first_name, ""), " ", IFNULL(sa.last_name, "")) as student_name, sa.admission_no')
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id = sy.student_admission_id')
            ->where('sy.class_id', $class_id)
            ->where('sy.class_section_id', $section_id)
            ->where('sy.acad_year_id', $this->yearId)
            ->group_start()
                ->group_start()
                    ->where('sa.admission_status', 2)
                    ->or_where('sa.admission_status', 4)
                    ->or_where('sa.admission_status', 5)
                ->group_end()
                ->or_where('sy.promotion_status', 'JOINED')
            ->group_end()
            ->get()->result();

        // Get students with attendance taken on specific date
        $students_with_attendance = $this->db_readonly->select('asds.student_admission_id')
            ->from('attendance_std_day_v2_students asds')
            ->join('attendance_std_day_v2_session atses', 'atses.id = asds.attendance_day_v2_session_id')
            ->where('atses.class_id', $class_id)
            ->where('atses.class_section_id', $section_id)
            ->where('atses.att_taken_date', $selectedDate)
            ->where('atses.acad_year_id', $this->yearId)
            ->distinct()
            ->get()->result();

        // Create array of student IDs with attendance
        $attended_student_ids = [];
        foreach ($students_with_attendance as $student) {
            $attended_student_ids[] = $student->student_admission_id;
        }

        // Find missing students (students without attendance on specific date)
        $missing_students = [];
        foreach ($all_students as $student) {
            if (!in_array($student->id, $attended_student_ids)) {
                $missing_students[] = [
                    'student_name' => $student->student_name,
                    'admission_no' => $student->admission_no
                ];
            }
        }

        return $missing_students;
    }

    public function insertEmergencyExitOTP($input, $otp) {
       

        $this->db->where('mobile_number', $input['mobileNumber']);
        $existing_record = $this->db->get('emergency_exit_otp');

        $otp_data = array(
            'parent_id' => isset($input['parent_id']) ? $input['parent_id'] : null,
            'mobile_number' => $input['mobileNumber'],
            'name' => $input['name'],
            'otp' => $otp,
            'created_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+10 minutes')),
            'verified_at' => null,
        );


        if ($existing_record->num_rows() > 0) {
            $existing_row = $existing_record->row();
            $this->db->where('mobile_number', $input['mobileNumber']);
            $this->db->update('emergency_exit_otp', $otp_data);
            $otp_id = $existing_row->id;
        } else {
            $this->db->insert('emergency_exit_otp', $otp_data);
            $otp_id = $this->db->insert_id();
        }

        return $otp_id;
    }

    public function verifyEmergencyExitOTP($input) {
    if (!isset($input['mobileNumber']) || !isset($input['name']) || !isset($input['otpCode'])) {
        return false;
    }

    $this->db->trans_start();

    $this->db->select('*');
    $this->db->where('mobile_number', $input['mobileNumber']);
    $this->db->where('name', $input['name']);
    $this->db->where('otp', $input['otpCode']);
    $this->db->where('expires_at >', date('Y-m-d H:i:s'));

    $this->db->where('verified_at IS NULL', null, false);

    $this->db->order_by('created_at', 'DESC'); // Get the most recent OTP
    $this->db->limit(1);
    $q = $this->db->get('emergency_exit_otp');

    if ($q->num_rows() > 0) {
        $otp_record = $q->row();

        $CI =& get_instance();
        $update_data = array(
            'verified_at' => date('Y-m-d H:i:s'),
            'verified_by_ip' => $CI->input->ip_address()
        );

        $this->db->where('id', $otp_record->id);
        $this->db->where('verified_at IS NULL', null, false); // Double-check it's still unverified
        $this->db->update('emergency_exit_otp', $update_data);

        if ($this->db->affected_rows() > 0) {
            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                return false;
            }

            return $otp_record->id;
        } else {
            $this->db->trans_rollback();
            return false;
        }
    } else {
        $this->db->trans_rollback();
        return false;
    }
}


    
    

    

    

   



}
