<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('communication_dashboard');?>">Communication</a></li>
    <li>Texting Report</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('communication_dashboard'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Texting Report
                    </h3>
                    <div>
                        <a onclick="refreshStatus()" id="initiate_status_refresh" class="btn btn-primary ml-3">Initiate Status Refresh</a>
                        <div id="reportrange" class="dtrange">
                            <span></span><b class="caret"></b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body pt-1 overflow-auto" id="communication-data">
        </div>
    </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script type="text/javascript">
$(function() {
    /* reportrange */
    if ($("#reportrange").length > 0) {
        $("#reportrange").daterangepicker({
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1,
                    'month').endOf('month')]
            },
            opens: 'left',
            buttonClasses: ['btn btn-default'],
            applyClass: 'btn-small btn-primary',
            cancelClass: 'btn-small',
            format: 'MM.DD.YYYY',
            separator: ' to ',
            startDate: moment(),
            endDate: moment()
        }, function(start, end) {
            $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
            // $('#fromDate').val(start.format('D-MM-YYYY'));
            // $('#toDate').val(end.format('DD-MM-YYYY'));
            // get_fee_summary_amount_class(fee_type, start.format('D-MM-YYYY'),end.format('DD-MM-YYYY'));
            getTextReport(start.format('DD-MM-YYYY'), end.format('DD-MM-YYYY'));
        });

        $('#fromDate').val(moment().format('DD-MM-YYYY'));
        $('#toDate').val(moment().format('DD-MM-YYYY'));

        $("#reportrange span").html(moment().format('MMMM D, YYYY') + ' - ' + moment().format('MMMM D, YYYY'));
    }
    /* end reportrange */

    $(".x-navigation-minimize").on("click", function() {
        setTimeout(function() {
            rdc_resize();
        }, 200);
    });
});
</script>

<script type="text/javascript">
$(document).ready(function() {
    var from = moment().format('DD-MM-YYYY');
    var to = moment().format('DD-MM-YYYY');
    getTextReport(from, to);
});

$("#getBtn").click(function() {
    var from = $("#fromdateId").val();
    var to = $("#todateId").val();
    getTextReport(from, to);
});

function refreshStatus() {
    $('#initiate_status_refresh').attr('disabled', true);
    $('#initiate_status_refresh').html('Please Wait...');
    $.ajax({
        url: '<?php echo site_url('communication/texting/refreshStatus'); ?>',
        type: "post",
        success: function(data) {
            $('#initiate_status_refresh').removeAttr('disabled');
            $('#initiate_status_refresh').html('Initiate Status Refresh');
            $(function() {
                new PNotify({
                    title: 'Success',
                    text: 'Refreshing data initiated. Check back in few minutes',
                    type: 'success',
                });
            });
        }
    });
}

function getTextReport(fromDate, toDate) {
    $('#reportrange').css('pointer-events', 'none');
    $("#communication-data").html('<div style="text-align:center;"><i class="fa fa-spinner fa-spin" style="font-size:35px;"></i></div>');
    $.ajax({
        url: '<?php echo site_url('communication/texting/getTextReport'); ?>',
        data: {
            'from': fromDate,
            'to': toDate
        },
        type: "post",
        success: function(data) {
            var report = $.parseJSON(data);
            // console.log(report);
            $('#report-loader').hide();
            let html = '';
            if (report.length == 0) {
                html += '<div class="no-data-display">No data found</div>';
                $("#communication-data").html(html);
            } else {
                html = '<div class="text-center mb-2"><h4>' + fromDate + ' To ' + toDate + '</h4></div>';
                html +=
                    '<table class="table table-bordered" id="report-table"><thead><tr><th>#</th><th>Date</th><th>Message</th><th>Mode</th><th>Sent To</th><th>Sent By</th><th>Notifications</th><th>Sms (Credits)</th><th>Action</th></tr></thead><tbody id="report-content">';
                // $("#report-table").show();
                var j = 1;
                for (var i in report) {
                    var mode = 'Notification/Sms';
                    if (report[i].mode == 'sms') {
                        mode = 'Only Sms';
                    }
                    if (report[i].mode == 'notification') {
                        mode = 'Only Notification';
                    }
                    var statusArr = report[i].status;
                    var notification = 'NA';
                    var sms = 'NA';
                    if (statusArr.notifications != 0) {
                        notification = '<b>Notifications: ' + statusArr.notifications + '</b><br>';
                        notification += 'Sent: ' + statusArr.sent + '<br>';
                        notification += 'Failed: ' + statusArr.failed + '<br>';
                        notification += 'No Token: ' + statusArr.no_token + '<br>';
                    }
                    if (statusArr.sms != 0) {
                        sms = '<b>Sms: ' + statusArr.sms + ' (' + (statusArr.sms * report[i].sms_credits) +
                            ')</b><br>';
                        sms += 'Delivered: ' + statusArr.delivered + '<br>';
                        sms += 'Awaited: ' + statusArr.awaited + '<br>';
                        sms += 'Failed: ' + statusArr.not_delivered + '<br>';
                        sms += 'Not Ack: ' + statusArr.not_acknowledged + '<br>';
                        sms += 'No Number: ' + statusArr.no_number + '<br>';
                    }
                    html += '<tr>';
                    html += '<td>' + (j++) + '</td>';
                    // html += '<td>'+report[i].title+'</td>';
                    html += '<td>' + report[i].sent_date + '</td>';
                    html += '<td>' + report[i].message + '</td>';
                    html += '<td>' + mode + '</td>';
                    html += '<td>' + report[i].reciever + '</td>';
                    html += '<td>' + report[i].sender + '</td>';
                    html += '<td>' + notification + '</td>';
                    html += '<td>' + sms + '</td>';
                    // html += '<td>'+report[i].sms_credits+'</td>';
                    var url = "<?php echo site_url('communication/texting/fullTextReport/'); ?>" + report[i]
                        .id;
                    html += '<td><a href="' + url +
                        '" data-toggle="tooltip" data-original-title="View full report" data-placement="top" class="btn btn-info"><i class="fa fa-eye mr-0"></i></a></td>';
                    html += '</tr>';
                }
                html += '</tbody></table>';
                $("#communication-data").html(html);
                $('#report-table').DataTable({
                    "paging": true,
                    "lengthChange": true,
                    "searching": true,
                    "ordering": false,
                    "info": true,
                    "autoWidth": false,
                    "responsive": true,
                    "dom": 'Bfrtip',
                    "buttons": [
                        {
                            extend: 'print',
                            text: 'Print',
                            className: 'btn btn-danger',
                            title: 'Texting Report',
                            exportOptions: {
                                columns: ':not(:last-child)'
                            }
                        },
                        {
                            extend: 'excel',
                            text: 'Excel',
                            className: 'btn btn-primary',
                            title: 'Texting Report',
                            exportOptions: {
                                columns: ':not(:last-child)'
                            }
                        }
                    ]
                });
            }
            $('#reportrange').css('pointer-events', '');
        },
        error: function(error) {
            console.log(error);
            $('#reportrange').css('pointer-events', '');
        }
    });
}
</script>

<style type="text/css">
.btn-primary {
    color: white !important;
}

.ellipsis{
    display: none;
}

.dt-buttons{
    text-align: end;
    margin-bottom: 5px;
}
</style>