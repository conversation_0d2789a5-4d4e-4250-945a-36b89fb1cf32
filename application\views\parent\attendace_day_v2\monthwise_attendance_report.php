<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('parent_controller/attendance_day_v2') ?>">Attendance</a></li>
  <li>Month-wise Attendance Report</li>
</ul>
<div class="container-fluid">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border d-flex align-items-center justify-content-between">
      <h3 class="card-title panel_title_new_style_staff mb-0">
        <a class="back_anchor" href="<?php echo site_url('parent_controller/attendance_day_v2'); ?>" aria-label="Go back">
          <span class="fa fa-arrow-left"></span>
        </a> 
        Month-wise Attendance Report
      </h3>
    </div>
    <div class="card-body"> 
      <form id="monthwiseAttendanceReportForm" method="post">
        <div class="row g-3">
          <div class="col-md-3">
            <div class="form-group">
              <label for="reportrange">Date Range</label>
              <div id="reportrange" class="dtrange">
                <span></span>
                <input type="hidden" name="from_date" id="from_date">
                <input type="hidden" name="to_date" id="to_date">
              </div>
            </div>
          </div>

          <div class="col-md-2 my-5">
            <button type="button" class="btn btn-primary w-100" id="submitBtn" onclick="getMonthwiseAttendanceReport()" style="margin-top: -4px;" aria-label="Get Attendance Report">Get Report</button>
          </div>
        </div>
      </form>
      <div id="monthWiseSummaryReport" class="mt-4"></div>
    </div>
  </div>
</div>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">

$("#reportrange").daterangepicker({
    ranges: {
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        'Last 3 Months': [moment().subtract(3, 'month').startOf('month'), moment()],
        'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment()],
        'Custom Range': []
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment().startOf('month'),
    endDate: moment(),
    maxDate: moment(),
}, function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
});

// Set initial date range (This Month)
$("#reportrange span").html(moment().startOf('month').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
$('#from_date').val(moment().startOf('month').format('DD-MM-YYYY'));
$('#to_date').val(moment().format('DD-MM-YYYY'));

const loading = `<div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="text-align: center;">
                  <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                </div>`;
                
function getMonthwiseAttendanceReport() {
  $("#submitBtn").prop("disabled", true).text("Please wait...");
  $('#monthWiseSummaryReport').html(loading);

  var formData = $('#monthwiseAttendanceReportForm').serialize();

  $.ajax({
    type: 'POST',
    data: formData,
    url: '<?php echo site_url('parent_controller/get_monthwise_attendance_report') ?>',
    dataType: 'json',
    success: function(response) {
      try {
        if (response.error) {
          $('#monthWiseSummaryReport').html(`<div class="alert alert-danger">${response.error}</div>`);
          return;
        }

        if (!response.student || !response.student.months) {
          $('#monthWiseSummaryReport').html('<div class="alert alert-info">No attendance data found</div>');
          return;
        }

        const student = response.student;
        
        // Get all unique months
        const monthKeys = Object.keys(student.months).sort((a, b) => {
          return new Date(`01-${a}`) - new Date(`01-${b}`);
        });

        // Build the report table
        let html = `
          <div class="row mb-3">
            <div class="col-md-12">
              <h4>Student: ${student.student_name || 'N/A'}</h4>
              <p>Admission No: ${student.admission_no || 'N/A'}</p>
              <p>Report Period: ${$('#reportrange span').text()}</p>
            </div>
          </div>
          <div class="scrolable-table-container">
            <table class="table table-bordered table-striped table-hover scrollable-table" id="monthWiseSummaryTable">
              <thead>
                <tr>
                  <th class="text-center">Month</th>
                  <th class="text-center">Working Days</th>
                  <th class="text-center">Present Sessions</th>
                  <th class="text-center">Absent Sessions</th>
                  <th class="text-center">Monthly %</th>
                </tr>
              </thead>
              <tbody>`;

        // Generate month rows
        monthKeys.forEach(month => {
          const monthData = student.months[month];
          const percentageDisplay = monthData.attendance_percentage !== undefined
                         ? monthData.attendance_percentage.toFixed(1) + '%'
                         : '0.0%';
          
          html += `
            <tr>
              <td class="text-center"><strong>${month}</strong></td>
              <td class="text-center">${monthData.working_days}</td>
              <td class="text-center">${monthData.present_sessions}</td>
              <td class="text-center">${monthData.absent_sessions}</td>
              <td class="text-center">${percentageDisplay}</td>
            </tr>`;
        });

        // Add yearly summary row
        const yearlyPercentage = student.yearly_percentage !== undefined
                               ? student.yearly_percentage.toFixed(1) + '%'
                               : '0.0%';
        html += `
            <tr style="background-color: #f8f9fa; font-weight: bold;">
              <td class="text-center"><strong>Overall (Yearly)</strong></td>
              <td class="text-center">-</td>
              <td class="text-center">${student.yearly_present_sessions || 0}</td>
              <td class="text-center">${(student.yearly_total_sessions || 0) - (student.yearly_present_sessions || 0)}</td>
              <td class="text-center">${yearlyPercentage}</td>
            </tr>`;

        html += `</tbody></table></div>`;
        $('#monthWiseSummaryReport').html(html);

        // Initialize DataTable
        $('#monthWiseSummaryTable').DataTable({
          ordering: false,
          paging: false,
          info: false,
          lengthChange: false,
          searching: false,
          dom: 'lBfrtip',
          buttons: [
            {
              extend: 'excelHtml5',
              text: 'Excel',
              title: 'Month-wise Attendance Report',
              filename: 'monthWiseAttendanceReport',
              className: 'btn btn-info'
            },
            {
              extend: 'print',
              text: 'Print',
              title: 'Month-wise Attendance Report',
              filename: 'monthWiseSummaryReport',
              className: 'btn btn-info'
            }
          ]
        });

      } catch (err) {
        console.error('Error:', err);
        $('#monthWiseSummaryReport').html('<div class="alert alert-danger">An error occurred while processing the data.</div>');
      } finally {
        $("#submitBtn").prop("disabled", false).text("Get Report");
      }
    },
    error: function(xhr, status, error) {
      console.error('AJAX Error:', error);
      $('#monthWiseSummaryReport').html('<div class="alert alert-danger">Failed to load data. Please try again.</div>');
      $("#submitBtn").prop("disabled", false).text("Get Report");
    }
  });
}

</script>

<style>
  .form-group {
    margin: 8px 0px;
  }

  .form-group:last-child {
    margin-bottom: 10px;
  }

  div.dt-buttons,
  .dataTables_wrapper .dt-buttons {
    float: right;
    margin-bottom: 10px;
  }

  .scrolable-table-container {
    position: relative;
    overflow-x: auto;
    width: 100%;
  }

  .scrollable-table {
    border-collapse: collapse;
    width: 100%;
    table-layout: auto;
  }

  .scrollable-table th,
  .scrollable-table td {
    border: 1px solid #ccc;
    padding: 12px;
    text-align: center;
    min-width: 120px;
  }

  .scrollable-table thead th {
    background: #f1f1f1;
    font-weight: bold;
  }

  tr:hover {
    background: #F1EFEF;
  }

  .dt-buttons {
    font-size: 14px;
    background: none;
  }
</style>
