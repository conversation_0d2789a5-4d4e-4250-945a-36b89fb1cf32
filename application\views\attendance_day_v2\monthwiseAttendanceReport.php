<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance V2</a></li>
  <li>Student Month-wise Summary Report</li>
</ul>
<div class="container-fluid">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border d-flex align-items-center justify-content-between">
      <h3 class="card-title panel_title_new_style_staff mb-0">
        <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2'); ?>" aria-label="Go back">
          <span class="fa fa-arrow-left"></span>
        </a> 
        Student Month-wise Summary Report
      </h3>
    </div>
    <div class="card-body"> 
      <form id="monthwiseAttendanceReportForm" method="post">
        <div class="row g-3">
          <div class="col-md-2">
            <div class="form-group">
              <label for="sectionid">Section <span class="text-danger">*</span></label>
              <select name="classsecID" id="sectionID" class="form-control" required>
                <option value="all">Select Section</option>
                <?php foreach ($class_section as $cls_section){ ?>
                  <option value="<?= $cls_section->classID.'_'.$cls_section->sectionID ?>">
                    <?= $cls_section->class_name . ' ' . $cls_section->section_name ?>
                  </option>
                <?php } ?>
              </select>
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label for="reportrange">Date Range</label>
              <div id="reportrange" class="dtrange">
                <span></span>
                <input type="hidden" name="from_date" id="from_date">
                <input type="hidden" name="to_date" id="to_date">
              </div>
            </div>
          </div>

          <div class="col-md-2 my-5">
            <button type="button" class="btn btn-primary w-100" id="submitBtn" onclick="monthwiseAttendanceReport()" style="margin-top: -4px;" aria-label="Get Attendance Report">Get</button>
          </div>
        </div>
      </form>
      <div id="monthWiseSummaryReport" class="mt-4"></div>
    </div>
  </div>
</div>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script type="text/javascript">

$("#reportrange").daterangepicker({
    ranges: {
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        'Last 3 Months': [moment().subtract(3, 'month').startOf('month'), moment()],
        'Last 6 Months': [moment().subtract(6, 'month').startOf('month'), moment()],
        'Custom Range': []
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment().startOf('month'),
    endDate: moment(),
    maxDate: moment(),
}, function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
});

// Set initial date range (This Month)
$("#reportrange span").html(moment().startOf('month').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
$('#from_date').val(moment().startOf('month').format('DD-MM-YYYY'));
$('#to_date').val(moment().format('DD-MM-YYYY'));

const loading = `<div class="col-md-14 text-center d-flex justify-content-center align-items-center" style="text-align: center;">
                  <i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i>
                </div>`;
                
function monthwiseAttendanceReport() {
  const sectionID = $('#sectionID').val();
  const errorContainer = $('#sectionError');

  if (!sectionID || sectionID === 'all') {
    if (!errorContainer.length) {
      $('#sectionID').after('<span id="sectionError" style="color: #b30000; font-size: 12px;">Section is required.</span>');
    }
    return;
  }

  // Remove error message if validation passes
  if (errorContainer.length) {
    errorContainer.remove();
  }

  $("#submitBtn").prop("disabled", true).text("Please wait...");
  $('#monthWiseSummaryReport').html(loading);

  var formData = $('#monthwiseAttendanceReportForm').serialize();

  $.ajax({
    type: 'POST',
    data: formData,
    url: '<?php echo site_url('attendance_day_v2/Attendance_day_v2/ajaxMonthwiseAttendanceReport') ?>',
    dataType: 'json',
    success: function(response) {
      try {
        if (response.error) {
          $('#monthWiseSummaryReport').html(`<div class="no-data-display">${response.error}</div>`);
          return;
        }

        if (!response.students || response.students.length === 0) {
          $('#monthWiseSummaryReport').html('<div class="no-data-display">No attendance data found</div>');
          return;
        }

        // Add date range information
        let html = `
          <div class="scrolable-table-container">
            <table class="table table-bordered table-striped table-hover scrollable-table" id="monthWiseSummaryTable">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Student Name</th>
                  <th class="text-center">Admission No</th>`;

        // Get all unique months from all students
        const monthKeys = [];
        response.students.forEach(student => {
          if (student.months) {
            Object.keys(student.months).forEach(month => {
              if (!monthKeys.includes(month)) {
                monthKeys.push(month);
              }
            });
          }
        });

        // Sort months chronologically
        monthKeys.sort((a, b) => {
  return new Date(`01-${a}`) - new Date(`01-${b}`);
  });


        // Add month columns
        monthKeys.forEach(month => {
          html += `
                  <th class="text-center">${month} (T)</th>
                  <th class="text-center">${month} (P)</th>
                  <th class="text-center">${month} (A)</th>
                  <th class="text-center">${month} (%)</th>`;
        });

        // Add yearly percentage column
        html += `
                  <th class="text-center">Yearly (%)</th>`;

        html += `</tr></thead><tbody>`;

        // Generate student rows
        response.students.forEach((student, index) => {
          html += `
            <tr>
              <td>${index + 1}</td>
              <td>${student.student_name || 'N/A'}</td>
              <td class="text-center">${student.admission_no || 'N/A'}</td>`;

          monthKeys.forEach(month => {
            const monthData = student.months?.[month] || { T: 0, P: 0, A: 0, attendance_percentage: 0 };
            const percentageDisplay = monthData.attendance_percentage !== undefined
                           ? monthData.attendance_percentage.toFixed(1) + '%'
                           : '0.0%';

            html += `
              <td class="text-center">${monthData.working_days}</td>
              <td class="text-center">${monthData.present_sessions}</td>
              <td class="text-center">${monthData.absent_sessions}</td>
              <td class="text-center">${percentageDisplay}</td>`;
          });

          // Add yearly percentage
          const yearlyPercentage = student.yearly_percentage !== undefined
                                 ? student.yearly_percentage.toFixed(1) + '%'
                                 : '0.0%';
          html += `
              <td class="text-center" style="background-color: #f8f9fa; font-weight: bold;">${yearlyPercentage}</td>`;

          html += `</tr>`;
        });

        html += `</tbody></table></div>`;
        $('#monthWiseSummaryReport').html(html);

        // Initialize DataTable
        $('#monthWiseSummaryTable').DataTable({
          ordering: false,
          paging: false,
          info: false,
          lengthChange: false,
          scrollY: '40vh',
          scrollX: true,
          language: {
            search: "",
            searchPlaceholder: "Enter Search..."
          },
          dom: 'lBfrtip',
          buttons: [
            {
              extend: 'excelHtml5',
              text: 'Excel',
              title: 'Month-wise Attendance Report',
              filename: 'monthWiseAttendanceReport',
              className: 'btn btn-info'
            },
            {
              extend: 'print',
              text: 'Print',
              title: 'Month-wise Attendance Report',
              filename: 'monthWiseSummaryReport',
              className: 'btn btn-info'
            },
            {
              extend: 'pdfHtml5',
              text: 'PDF',
              title: 'Month-wise Attendance Report',
              filename: 'monthWiseAttendanceReport',
              className: 'btn btn-info'
            }
          ]
        });

      } catch (err) {
        console.error('Error:', err);
        $('#monthWiseSummaryReport').html('<div class="alert alert-danger">An error occurred while processing the data.</div>');
      } finally {
        $("#submitBtn").prop("disabled", false).text("Get");
      }
    },
    error: function(xhr, status, error) {
      console.error('AJAX Error:', error);
      $('#monthWiseSummaryReport').html('<div class="alert alert-danger">Failed to load data. Please try again.</div>');
      $("#submitBtn").prop("disabled", false).text("Get");
    }
  });
}


</script>

<style>
  .form-group {
    margin: 8px 0px;
  }

  .form-group:last-child {
    margin-bottom: 10px;
  }

  div.dt-buttons,
  .dataTables_wrapper .dt-buttons {
    float: right;
    margin-bottom: 10px;
  }

  .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5vh;
  }

  .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
  }

  .dataTables_filter {
    position: absolute;
    right: 20%;
  }

  @media only screen and (min-width: 1404px) {
    .dataTables_filter {
      right: 15%;
    }
  }

  @media only screen and (min-width: 1734px) {
    .dataTables_filter {
      right: 5vh;
    }
  }

  .dataTables_scrollBody {
    margin-top: -13px;
    overflow-x: auto;
  }

  tr:hover {
    background: #F1EFEF;
  }

  .row_background_color {
    background: #7f848780;
  }

  .dt-buttons {
    font-size: 14px;
    background: none;
  }

  .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;
  }

  td > a > i {
    text-decoration: none;
    font-size: 16px;
    color: #191818;
    padding: 2px 5px;
  }

  .dataTables_filter label input {
    margin-right: -2rem;
  }

  /* === Table Styling === */
  #monthWiseSummaryTable {
    width: 100% !important;
  }

  .scrolable-table-container {
    position: relative;
    overflow-x: auto;
    width: 100%;
  }

  .scrollable-table {
    border-collapse: collapse;
    width: max-content;
    min-width: 100%;
    table-layout: auto;
  }

  .scrollable-table th,
  .scrollable-table td {
    border: 1px solid #ccc;
    padding: 8px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    text-align: center;
    min-width: 120px;
  }

  /* === Sticky Header === */
  .scrollable-table thead th {
    position: sticky;
    top: 0;
    background: #f1f1f1;
    z-index: 4;
  }

  /* === Fixed First 3 Columns === */
  .scrollable-table th:nth-child(1),
  .scrollable-table td:nth-child(1) {
    position: sticky;
    left: 0;
    background: #fff;
    z-index: 3;
    min-width: 100px;
    width: 100px;
  }

  .scrollable-table th:nth-child(2),
  .scrollable-table td:nth-child(2) {
    position: sticky;
    left: 100px;
    background: #fff;
    z-index: 3;
    min-width: 150px;
    width: 150px;
  }

  .scrollable-table th:nth-child(3),
  .scrollable-table td:nth-child(3) {
    position: sticky;
    left: 250px;
    background: #fff;
    z-index: 3;
    min-width: 180px;
    width: 180px;
  }

  /* Ensure header cells in fixed columns appear above all */
  .scrollable-table thead th:nth-child(1),
  .scrollable-table thead th:nth-child(2),
  .scrollable-table thead th:nth-child(3) {
    z-index: 5;
  }

  /* Extra horizontal scroll styling for DataTables container */
  .dataTables_scrollBody {
    overflow-x: auto;
  }
</style>

