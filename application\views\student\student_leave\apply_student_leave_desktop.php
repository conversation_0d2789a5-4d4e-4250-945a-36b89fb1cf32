<div class="col-md-12">
<?php $past_date_enabled=$this->settings->getSetting('permit_past_day_leave_application_by_parent') ? 1 : 0; ?>
        <div class="card cd_border" style="width: 100%">
            <div class="card-header panel_heading_new_style_staff_border">
                 <div class="row" style="margin: 0px">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/Student_leave_controller/index'); ?>" class="control-primary">
                          <span class="fa fa-arrow-left"></span>
                        </a> 
                        Apply Leave
                    </h3>
                </div>
            </div>
            <form enctype="multipart/form-data" autocomplete="off" method="post" id="leave-form" action="<?php echo site_url('student/Student_leave_controller/studentApplyedLeaveStatus'); ?>" data-parsley-validate="" class="form-horizontal" >
                <div class="card-body">
                    <?php $notes=$this->settings->getSetting('student_leave_header_notes');
                    if(!empty($notes)){ ?>
                        <p><strong>Notes:</strong><br><?php echo $notes ?><br></p>
                    <?php } ?>
                        <input type="hidden" value="<?php echo $student_id; ?>" name="student_id" id="student_id">

                        <div class="form-group row">
                            <label class="control-label col-md-3 text-right" for="request_date">Request Date : </label>
                            <div class="col-md-4" >
                                <input type="text" class="form-control" readonly value="<?php echo date('d-m-Y'); ?>">
                            </div>
                            
                            <input type="hidden" value="<?php echo date('d-m-Y'); ?>" class="form-control"  id="requestdateId" name="request_date" >
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="from_date">From Date :<font color="red"> *</font></label>
                            <div class="col-md-4"> 
                                <div class="input-group date" id="start_date_picker"> 
                                    <input placeholder="Enter From Date" required="" type="text"  class="form-control " id="fromdateId" name="from_date" >
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date" >To Date :<font color="red"> *</font></label>
                            <div class="col-md-4">
                                <div class="input-group date" id="end_date_picker">
                                    <input placeholder="Enter To Date" required="" type="text" class="form-control " id="todateId" name="to_date">
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                                <!-- Calendar warning will appear here -->
                                <span class="help-block text-muted calendar-warning" style="display: none;">
                                </span>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="no_of_days">No. of Days :<font color="red"> *</font></label>
                            <div class="col-md-4">
                                <input type="number" step="0.5" min="0.5" class="form-control" required="" placeholder="Number of days" id="noofdaysId" name="noofdays" value="" readonly>
                                <span class="help-block text-muted">Leave days barring holidays and weekly off</span>

                            </div>
                        </div>

                        <!-- Half Day Selection - Only visible when from_date equals to_date -->
                        <div class="form-group row" id="half-day-selection" style="display: none;">
                            <label class="col-md-3 control-label">Leave Duration :<font color="red"> *</font></label>
                            <div class="col-md-4">
                                <label class="radio-inline">
                                    <input type="radio" name="half_day_type" value="full" checked> Full Day
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="half_day_type" value="half"> Half Day
                                </label>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date">Upload File</label>
                            <div class="col-md-4"> 
                                <div class="input-group"> 
                                    <input type="file" class="form-control " id="file_upload" name="file_upload" data-parsley-error-message="Upload Doctor Certificate if  more than 3 days Sick leave !">
                                </div>
                                 <span class="help-block text-muted">Upload Doctor Certificate for more than 3 days leave (Only pdf allowed) </span>
                            </div>
                           <!--  <div class="col-md-2" style="color:black;"><small>Allowed pdf<small></div> -->
                        </div>
                    
                        <!-- <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date">Leave Type</label>
                            <div class="col-md-4">
                                
                            <label class="radio-inline"><input type="radio"  value="Sick"  name="leave_type" required="">Sick</label>
                            <label class="radio-inline"><input checked type="radio"  required="" value="Other" type="radio" name="leave_type" required="">Other</label>
                    
                            </div>
                        </div> -->

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="leave_type">Leave Type: <font color="red">*</font></label>
                            <div class="col-md-4">
                                <select class="form-control" id="leave_type" required="" name="leave_type">
                                <option value="">Select Leave Type</option>
                                <?php if (!empty($leave_types)): ?>
                                    <?php foreach ($leave_types as $leave_type): ?>
                                        <option value="<?php echo ($leave_type->id); ?>">
                                            <?php echo ($leave_type->type_name); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="">No leave types available</option>
                                <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    
                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date">Reason</label>
                            <div class="col-sm-4">  
                                <textarea placeholder="Enter Reason" rows="4" class="form-control" name="reason"></textarea>
                            </div>
                        </div>

                         <div class="form-group row">
                            <label class="col-md-3 control-label">Attendance %</label>
                            <div class="col-sm-4">
                                <p class="form-control-plaintext" style="font-size: 13px; color:<?php echo $studentAttendancePercentage >= 75 ? '#28a745' : ($studentAttendancePercentage >= 50 ? '#ffc107' : '#dc3545'); ?>;margin-top: 1px;">
                                    <?php echo $studentAttendancePercentage ?: 'Loading attendance...'; ?>
                                </p>
                            </div>
                        </div>

                </div>
                </div>
                <center>
                    <div class="card-footer">
                        <input type="submit" style="width: 120px;" class="btn btn-primary" onclick="loader()">
                        <a href="<?php echo site_url('student/Student_leave_controller/index'); ?>" style="width: 120px;" class='btn btn-warning mrg'>Cancel</a>
                    </div>
                </center>
            </form>
        </div>
</div>

<div class="visible-xs">
  <a href="<?php echo site_url('student/Student_leave_controller/index');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script type="text/javascript">
    function convert($str) {
    var date = new Date($str),
        mnth = ("0" + (date.getMonth()+1)).slice(-2),
        day  = ("0" + date.getDate()).slice(-2);

    // Check if the date is invalid
    if(isNaN(date.getFullYear()) || isNaN(date.getMonth()) || isNaN(date.getDate())) {
        return null;
    }

    return [ date.getFullYear(), mnth, day ].join("-");
}

// Function to parse dates flexibly for iOS compatibility
function parseDateFlexible(dateStr) {
    if (!dateStr) return null;

    const parts = dateStr.split("-");
    if (parts.length !== 3) return null;

    if (parts[0].length === 4) {
        // YYYY-MM-DD format
        return new Date(dateStr);
    } else {
        // DD-MM-YYYY format (from datepicker)
        const [day, month, year] = parts.map(Number);
        if (isNaN(day) || isNaN(month) || isNaN(year)) return null;
        return new Date(year, month - 1, day);
    }
}
</script>
<script type="text/javascript">
    var pastDateEnabled = <?= json_encode($past_date_enabled == 1) ?>;
    $(document).ready(function () {
        var startDate = null;
        var endDate = null;

    $('input[name=sick_other]').change(function(){
        var value = $( 'input[name=sick_other]:checked' ).val();
        var days = $('#noofdaysId').val();
        if(value === 'Sick' && days > 3){
           $("#file_upload").prop("required", true);
        }
        else {
            $("#file_upload").prop("required", false);
        }
    });

    $('#start_date_picker').datepicker({
        format: 'd-m-yyyy',
        startDate: pastDateEnabled  ? null : new Date(),
        "autoclose": true
      })
      .on('changeDate', function (selected) {
        startDate = new Date(selected.date.valueOf());
        var date2 = $('#start_date_picker').datepicker('getDate');
        date2.setDate(date2.getDate());
        $('#end_date_picker').datepicker('setDate', date2);
        //sets minDate to dt1 date + 1
        $('#end_date_picker').datepicker('setStartDate', date2);

       if (startDate != null|| endDate !=null){
       var formatedstartdate=convert(startDate);
       var formateendate=convert(endDate);
        myFunc(formatedstartdate, formateendate);
        }
    });
    $('#end_date_picker').datepicker({
        format: 'd-m-yyyy',
        "autoclose": true
    })
    .on('changeDate', function (selected) {
        endDate = new Date(selected.date.valueOf());
    var formatedstartdate=convert(startDate);
    var formateendate=convert(endDate);
    if (startDate != null|| endDate !=null)
        {
        myFunc(formatedstartdate, formateendate);
        } 
    });

    $('#file_upload').on('change', function(){
        if(this.files[0].size < 1000000){
            var file = $(this).val();
            if(file == '') return false;
            var ext = file.substr(file.lastIndexOf('.') + 1).toLowerCase();
            var allowedExt = ['pdf'];
            if(ext.length <= 0){
                alert('Wrong file format, Allowed formats (pdf)');
                $(this).val() = '';
                return false;
            }
            else {
                if(allowedExt.indexOf(ext) === -1){
                    alert('Wrong file format, Allowed formats (pdf)');
                    $(this).val('');
                    return false;
                }
            }
        } else{
            alert("File is too big! Please upload with in 1MB");
            this.value = "";
            return false;
        }
        
    });
});
</script>

 <script type="text/javascript">
    function myFunc(startDate, endDate) {
        if (startDate ==  "1970-01-01" || endDate == "1970-01-01")
            return false;

        var studentId = $("#student_id").val();
        if (!studentId) {
            $("#noofdaysId").val(0);
            return false;
        }

        // The updateDaysBasedOnSelection function now handles all day calculation logic
        updateDaysBasedOnSelection();
    }

    // Function to update days based on half-day selection
    function updateDaysBasedOnSelection() {
        var fromDate = $('#fromdateId').val();
        var toDate = $('#todateId').val();
        var studentId = $('#student_id').val();

        if (!fromDate || !toDate || !studentId) {
            return;
        }

        // Use parseDateFlexible for iOS compatibility
        var startDateObj = parseDateFlexible(fromDate);
        var endDateObj = parseDateFlexible(toDate);

        if (!startDateObj || !endDateObj) {
            console.error('Invalid date format:', fromDate, toDate);
            return;
        }

        var startDate = convert(startDateObj);
        var endDate = convert(endDateObj);

        // Check if from_date equals to_date (same day)
        if (startDate === endDate) {
            // Show half-day selection for same day
            $("#half-day-selection").show();

            var selectedType = $('input[name="half_day_type"]:checked').val();
            if (selectedType === 'half') {
                // Use existing getHolidayCountV2 to validate and get day count
                $.post('<?php echo site_url('student/Student_leave_controller/getHolidayCountV2'); ?>', {
                    startDate: startDate,
                    endDate: endDate,
                    studentId: studentId
                }, function (data) {
                    var dayCount = parseFloat(data);
                    if (dayCount === 0) {
                        // Day is marked as weekoff, show error and reset to full day
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid Date for Half-Day Leave',
                            text: 'Leave cannot be applied on ' + fromDate + ' as it is marked as weekoff.',
                            confirmButtonText: 'OK'
                        }).then(function() {
                            // Reset to full day
                            $('input[name="half_day_type"][value="full"]').prop('checked', true);
                            // Recalculate with full day
                            updateDaysBasedOnSelection();
                        });
                    } else {
                        // Valid for half-day, set 0.5
                        $("#noofdaysId").val(0.5);
                    }
                });
            } else {
                // For single full day, use calendar v2 system for holiday calculation
                $.post('<?php echo site_url('student/Student_leave_controller/getHolidayCountV2'); ?>', {
                    startDate: startDate,
                    endDate: endDate,
                    studentId: studentId
                }, function (data) {
                    var leaveholidayCount = data;
                    $("#noofdaysId").val(parseFloat(leaveholidayCount));
                });
            }
        } else {
            // Hide half-day selection for multiple days
            $("#half-day-selection").hide();
            // Reset to full day
            $('input[name="half_day_type"][value="full"]').prop('checked', true);

            // Use calendar v2 system for holiday calculation
            $.post('<?php echo site_url('student/Student_leave_controller/getHolidayCountV2'); ?>', {
                startDate: startDate,
                endDate: endDate,
                studentId: studentId
            }, function (data) {
                var leaveholidayCount = data;
                $("#noofdaysId").val(parseFloat(leaveholidayCount));
            });
        }
    }

    // Check assigned calendar when page loads
    $(document).ready(function() {
        var studentId = $("#student_id").val();
        if (studentId) {
            checkAssignedCalendar(studentId);
        }
    });

    // Function to check assigned calendar and set date restrictions
    function checkAssignedCalendar(studentId) {
        if (!studentId) return;

        $.post("<?php echo site_url('student/Student_leave_controller/getAssignedCalendarDetails');?>", {studentId: studentId}, function(data) {
            try {
                var calendarData = $.parseJSON(data);
                if (calendarData && calendarData.start_date && calendarData.end_date) {
                    // Update date picker restrictions based on calendar range
                    var startDate = pastDateEnabled ? null : new Date();
                    var endDate = new Date(calendarData.end_date);

                    // Update the date pickers with calendar restrictions
                    $('#start_date_picker').datepicker('setStartDate', startDate);
                    $('#start_date_picker').datepicker('setEndDate', endDate);
                    $('#end_date_picker').datepicker('setStartDate', startDate);
                    $('#end_date_picker').datepicker('setEndDate', endDate);

                    // Show calendar warning to user
                    showCalendarWarning(calendarData);
                } else {
                    // No calendar assigned, remove restrictions
                    var defaultStartDate = pastDateEnabled ? null : new Date();
                    $('#start_date_picker').datepicker('setStartDate', defaultStartDate);
                    $('#start_date_picker').datepicker('setEndDate', null);
                    $('#end_date_picker').datepicker('setStartDate', defaultStartDate);
                    $('#end_date_picker').datepicker('setEndDate', null);
                    hideCalendarWarning();
                }
            } catch (e) {
                console.log('Error parsing calendar data:', e);
            }
        });
    }

    // Function to show calendar warning
    function showCalendarWarning(calendarData) {
        var warningHtml = 'Leave dates must be between <strong>' + formatDate(calendarData.start_date) + '</strong> and <strong>' + formatDate(calendarData.end_date) + '</strong> ';

        $('.calendar-warning').html(warningHtml);
        $('.calendar-warning').show();
    }

    // Function to hide calendar warning
    function hideCalendarWarning() {
        $('.calendar-warning').hide();
    }

    // Function to format date for display
    function formatDate(dateString) {
        var date = new Date(dateString);
        return date.getDate().toString().padStart(2, '0') + '-' +
               (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
               date.getFullYear();
    }

    // Helper to check if a date is a holiday
    function isHoliday(dateStr, holidays) {
        return holidays.indexOf(dateStr) !== -1;
    }

    // Fetch holidays for the student's calendar
    var calendarHolidays = [];
    function fetchCalendarHolidays(studentId, callback) {
        $.post("<?php echo site_url('student/Student_leave_controller/getAssignedCalendarDetails');?>", {studentId: studentId}, function(data) {
            try {
                var calendarData = $.parseJSON(data);
                if (calendarData && calendarData.id) {
                    $.post("<?php echo site_url('student/Student_leave_controller/getCalendarHolidays');?>", {calendarId: calendarData.id}, function(holidaysData) {
                        try {
                            var holidaysArr = JSON.parse(holidaysData);
                            calendarHolidays = holidaysArr;
                            if (callback) callback();
                        } catch (e) { calendarHolidays = []; if (callback) callback(); }
                    });
                } else { calendarHolidays = []; if (callback) callback(); }
            } catch (e) { calendarHolidays = []; if (callback) callback(); }
        });
    }

    // Global flag to prevent multiple holiday validation calls
    var isValidatingHolidays = false;

    // Validate on date change using new holiday validation API
    function validateHolidayOnDateChange() {
        var fromDate = $('#fromdateId').val();
        var toDate = $('#todateId').val();
        var studentId = $('#student_id').val();

        if (!fromDate || !toDate || !studentId || isValidatingHolidays) return;

        isValidatingHolidays = true;

        // Use parseDateFlexible for iOS compatibility
        var startDateObj = parseDateFlexible(fromDate);
        var endDateObj = parseDateFlexible(toDate);

        if (!startDateObj || !endDateObj) {
            console.error('Invalid date format:', fromDate, toDate);
            isValidatingHolidays = false;
            return;
        }

        var formattedStartDate = convert(startDateObj);
        var formattedEndDate = convert(endDateObj);

        // Check if date conversion failed
        if (!formattedStartDate || !formattedEndDate) {
            console.error('Invalid date conversion:', fromDate, toDate);
            isValidatingHolidays = false;
            return;
        }

        $.post('<?php echo site_url('student/Student_leave_controller/validateHolidayDates'); ?>', {
            startDate: formattedStartDate,
            endDate: formattedEndDate,
            studentId: studentId
        }, function(data) {
            try {
                var response = JSON.parse(data);
                if (!response.valid) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Holiday Detected',
                        text: response.message,
                        confirmButtonText: 'OK'
                    }).then(function() {
                        // Clear the date fields after user acknowledges
                        $('#fromdateId').val('');
                        $('#todateId').val('');
                        $('#noofdaysId').val('');
                        $('#half-day-selection').hide();
                        isValidatingHolidays = false;
                    });
                } else {
                    // Validation passed, proceed with leave day calculation
                    isValidatingHolidays = false;
                    updateDaysBasedOnSelection();
                }
            } catch (e) {
                console.error('Error parsing holiday validation response:', e);
                isValidatingHolidays = false;
            }
        }).fail(function() {
            isValidatingHolidays = false;
        });
    }

    $(document).ready(function () {
        var studentId = $('#student_id').val();

        // Date change event handlers
        $('#fromdateId, #todateId').on('change', function() {
            // Add small delay to ensure both dates are set
            setTimeout(function() {
                validateHolidayOnDateChange();
            }, 100);
        });

        // Add event listener for half-day radio buttons
        $('input[name="half_day_type"]').on('change', function() {
            updateDaysBasedOnSelection();
        });
    });


</script>

<style>





/* Mobile responsive */
@media (max-width: 768px) {
    #calendar-warning {
        font-size: 11px !important;
        padding: 6px 8px !important;
    }

    
}


</style>


<script>

function loader(){
    var $form = $('#leave-form');
    if ($form.parsley().validate()){
        // Debug: Log form data before submission
        console.log('Form data before submission:');
        console.log('noofdays:', $('#noofdaysId').val());
        console.log('half_day_type:', $('input[name="half_day_type"]:checked').val());

        document.getElementById('loader').style.display="block";
        document.getElementById('myBtn').style.display="none";
        $('#leave-form').submit();
    }
}
</script>