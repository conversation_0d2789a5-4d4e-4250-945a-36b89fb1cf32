<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li>Gallery</li>
</ul>
<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_staff_border" style="padding-top: 10px;">
            <div class="row d-flex align-items-center">
                <div class="col-9 ">
                    <h3 class="card-title panel_title_new_style m-0">
                        <strong>Gallery</strong>
                    </h3>
                </div>
                <div class="col-3 ">
                    <ul class="panel-controls">
                        <li>
                            <a href="<?php echo site_url('galleries/create_gallery'); ?>" class="new_circleShape_res" style="background-color: #fe970a;">
                                <span class="fa fa-plus" style="font-size: 19px;"></span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card-body pt-0">
            <div class="">
                <table class='table table-bordered' id='list_tab'>
                    <tbody>
                        <?php $i = 1;
                        $i = 1;
                        foreach ($gallery_info as $row) { ?>
                            <tr>
                                <div class="container card" style="padding: 11px;margin: 25px auto 25px;border-radius: 12px;box-shadow: 0px 4px #d9c5c5;background: #f7f1f1;">
                                    <div style="display: flex;justify-content: space-between;margin: 10px 0;">
                                        <div style="background: #a9eba9;border-radius: 5px;padding:5px;">
                                            <?php $date = !empty($row['gallery_date']) ? date('d-m-Y', strtotime($row['gallery_date'])) : "-" ?>
                                            <?php echo $date;  ?>
                                        </div>
                                        <div  style="background: #a9eba9;border-radius: 5px;padding:5px;">
                                            <?php echo !empty($row['gallery_location']) ? character_limiter($row['gallery_location']) : '-'; ?>
                                        </div>
                                    </div>

                                    <div style="text-align: center;font-weight: 600;border-radius: 5px;padding: 5px 9px;margin: auto;font-size: 13px;">
                                        <?php echo $row['create_name']; ?>
                                    </div>

                                    <div style="display: flex;justify-content: space-around;margin: 10px 0 10px 0;">
                                        <div>
                                            <?php
                                            if ($row['visibilitiytCount'] == '') {
                                                echo '<span style="color: #1caf9a;font-weight: 700;" >Visibility to All <span><br>';
                                            } else {
                                                echo '<span style="color: #1caf9a;font-weight: 700;">Limited Visibility<span><br>';
                                            } ?>
                                            <a onclick="view_gallery_visibility('<?php echo $row['gallery_id'] ?>','<?php echo addcslashes($row['gallery_name'],"'") ?>')" data-toggle="modal" data-target="#gallery-visibility" class="btn btn-primary" >Visibility</a>
                                        </div>
                                        <div>
                                            <?php
                                            $disabled = '';
                                            if ($row['image_count'] == 0) {
                                                $disabled = 'disabled';
                                            } ?>
                                            <?php echo '<span style="color: #1caf9a;font-weight: 700;" >Publish ?<span><br>'; ?>
                                            <?php if ($row['publish_status'] == 1) { ?>
                                                <label class="switch">
                                                    <input <?php echo $disabled ?> type="checkbox" id="publish_toggle_<?php echo $row['gallery_id'] ?>" onclick="handlePublishToggle(this, '<?php echo $row['gallery_id'] ?>','0')" checked>
                                                    <span></span>
                                                </label>
                                            <?php } else { ?>
                                                <label class="switch">
                                                    <input <?php echo $disabled ?> type="checkbox" id="publish_toggle_<?php echo $row['gallery_id'] ?>" onclick="handlePublishToggle(this, '<?php echo $row['gallery_id'] ?>','1')">
                                                    <span></span>
                                                </label>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div style="display: flex;justify-content: space-around;margin: 10px 0;">
                                        <div>
                                            <?php if (isset($row['is_external_album']) && $row['is_external_album'] == 1): ?>
                                                <!-- External album - show external link button instead of upload -->
                                                <a style="width: 100px;border-radius: 5px;" href="<?php echo $row['external_album_url']; ?>" target="_blank" rel="noopener noreferrer" class="btn btn-info btn_align" data-placement="top" data-toggle="tooltip" data-original-title="Open External Album"><i class="fa fa-external-link"></i></a>
                                            <?php else: ?>
                                                <!-- Manual album - show upload button -->
                                                <a style="width: 100px;border-radius: 5px;" href="<?php echo site_url('galleries/view_gallery/' . $row['gallery_id']); ?>" class="btn btn-info btn_align" data-placement="top" data-toggle="tooltip" data-original-title="Upload photos"><i class="fa fa-plus-square-o"></i></a>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <a style="width: 100px;border-radius: 5px;margin: 0 4px;" href="<?php echo site_url('galleries/edit_gallery/' . $row['gallery_id']); ?>" class="btn  btn-warning btn_align" data-placement="top" data-toggle="tooltip" data-original-title="Edit Gallery Info">
                                                <i class="fa fa-pencil-square-o"></i></a>
                                        </div>
                                        <div>
                                            <a style="width: 100px;border-radius: 5px;" class="delete_event btn  btn-danger btn_align" id="galleryDeletedId<?php echo $row['gallery_id']; ?>" onclick="delete_gallery(<?php echo $row['gallery_id']; ?>)" data-galleryname="<?php echo $row['gallery_name']; ?>" data-placement="top" data-toggle="tooltip" data-original-title="Delete Gallery">
                                                <i class="fa fa-trash-o"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<form enctype="multipart/form-data" id="gallerVisibility-form" action="<?php echo site_url('galleries/insert_galleries_visibility'); ?>" class="form-horizontal" data-parsley-validate method="post" style="margin-bottom: 100px;">
    <input type="hidden" name="gallery_id" value="" id="gallery_id">
    <div id="gallery-visibility" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <?php
            $width = 'width:48%';
            if ($this->mobile_detect->isMobile()) {
                $width = 'width:100%';
            }
            ?>
            <div class="modal-content" style="<?php echo $width ?>;margin: auto;border-radius: .75rem">
                <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                    <h4 class="modal-title">Visibility For <span id="gallery_name"></span></h4>
                </div>
                <div id="content-body" class="modal-body" style="overflow-y:auto;max-height:500px;">
                    <h4><b>Choose Visibility</b></h4>
                    <div class="row" style="margin-top:1.5rem">
                        <div class="col-4">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" name="is_class_section" type="checkbox" onchange="checkboxFilter()" id="class_section" value="class_section">
                                <label class="form-check-label" for="class_section">Class/Section</label>
                            </div>
                            <div id="class_sectionshow" style="display: none;margin-top: 1.2rem;">
                                <div class="form-group">
                                    <div class="col-md-12">
                                        <select id="multi_class_sections" name="class_section[]" class="form-control" size="10" multiple="multiple">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-4">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" name="is_group" type="checkbox" onchange="checkboxFilter()" id="group" value="group">
                                <label class="form-check-label" for="group">Group</label>
                            </div>
                            <div class="form-group" id="groupShow" style="display: none;margin-top: 1.2rem;">
                                <div class="col-md-12">
                                    <select class="form-control" multiple size="10" name="group_id[]" id="group_id">
                                        <option value="0">Select group</option>
                                        <?php if(!empty($groups)) { ?>
                                            <?php foreach ($groups as $key => $group) {
                                                echo '<option value="'.$group->id.'">'.$group->group_name.'</option>';
                                            } ?>
                                        <?php } else { ?>
                                            <option value="" disabled>No Groups Found</option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-2" style="padding: 0;">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="is_staff" onchange="checkboxFilter()" id="inlineCheckbox2" value="staff">
                                <label class="form-check-label" for="inlineCheckbox2">Staff</label>
                            </div>
                        </div>
                        <div class="col-2" style="padding: 0;">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" name="is_public" onchange="checkboxFilter()" id="inlineCheckbox3" value="public">
                                <label class="form-check-label" for="inlineCheckbox3">Public</label>
                            </div>
                        </div>
                    </div>

                    <script type="text/javascript">
                        function checkboxFilter() {
                            // Get which checkbox was just clicked
                            var clickedCheckbox = event.target.id;

                            // If a checkbox was checked, uncheck all others
                            if (event.target.checked) {
                                // Uncheck all other checkboxes except the one that was clicked
                                if (clickedCheckbox !== 'class_section') {
                                    $('#class_section').prop('checked', false);
                                    $("#class_sectionshow").hide();
                                    $("#multi_class_sections option").prop('selected', false);
                                    $('#multi_class_sections').removeAttr('required');
                                }
                                if (clickedCheckbox !== 'group') {
                                    $('#group').prop('checked', false);
                                    $("#groupShow").hide();
                                    $("#group_id option").prop('selected', false);
                                    $('#group_id').removeAttr('required');
                                }
                                if (clickedCheckbox !== 'inlineCheckbox2') {
                                    $('#inlineCheckbox2').prop('checked', false);
                                }
                                if (clickedCheckbox !== 'inlineCheckbox3') {
                                    $('#inlineCheckbox3').prop('checked', false);
                                }
                            }

                            // Handle showing/hiding dropdowns based on current state
                            if ($('#class_section').is(":checked")) {
                                $("#class_sectionshow").show();
                            } else {
                                $("#class_sectionshow").hide();
                                $("#multi_class_sections option").prop('selected', false);
                                $('#multi_class_sections').removeAttr('required');
                            }

                            if ($('#group').is(":checked")) {
                                $("#groupShow").show();
                            } else {
                                $("#groupShow").hide();
                                $("#group_id option").prop('selected', false);
                                $('#group_id').removeAttr('required');
                            }

                        }

                        function selectAll() {
                            $("#multi_class_sections option").prop('selected', true);
                        }
                    </script>


                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-danger pull-right mr-1" style="width: 10rem;" data-dismiss="modal">Close</button>
                    <button type="button" id="submitbutton" onclick="onclickSubmitValidate()" style="width: 10rem;" class="btn btn-primary pull-right">Add</button>
                </div>

                <div class="col-12" style="margin-top:1rem">
                    <h4><b>Current Visibility</b></h4>

                    <p><span style="font-size: 14px;color: #f08704;">Class/Section</span>
                        <span id="classSectionId"></span>
                    </p>
                    <p><span style="font-size: 14px;color: #f08704;">Group</span>
                        <span id="groupId"></span>
                    </p>
                    <p><span style="font-size: 14px;color: #f08704;">Staff</span>
                        <span id="staffId"></span>
                    </p>
                    <p><span style="font-size: 14px;color: #f08704;">Website</span>
                        <span id="websiteId"></span>
                    </p>
                    <!-- <div class="gallery_list">
					
				</div> -->
                </div>

            </div>


        </div>
    </div>
</form>

<script>
    function onclickSubmitValidate() {

        // Remove required attributes first
        $('#multi_class_sections').removeAttr('required');
        $('#group_id').removeAttr('required');

        // Add required attributes only if checkboxes are checked
        if ($('#class_section').is(":checked")) {
            $('#multi_class_sections').prop('required', 'required');
        }

        if ($('#group').is(":checked")) {
            $('#group_id').prop('required', 'required');
        }

        var $form = $('#gallerVisibility-form');
        if ($form.parsley().validate()) {
            // Allow submission even if no checkboxes are selected (admin only gallery)
            $('#gallerVisibility-form').submit();
        }

    }

    function manageSelectedButton(type) {
        switch (type) {
            case 'Class':
                $("#class_section").show();
                $("#staff_all").hide();
                $("#groupNumbers").hide();
                break;
            case 'Staff':
                $("#staff_all").show();
                $("#class_section").hide();
                $("#groupNumbers").hide();
                break;
            case 'Group':
                $("#groupNumbers").show();
                $("#staff_all").hide();
                $("#class_section").hide();
                break;
        }
    }

    function view_gallery_visibility(gallery_id, gallery_name) {
        $('#gallery_id').val(gallery_id);
        $('#gallery_name').html(gallery_name);
        $(".gallery_list").html('');
        $('input[name="is_class_section"]:checkbox').removeAttr('checked');
        $('input[name="is_group"]:checkbox').removeAttr('checked');
        $('input[name="is_staff"]:checkbox').removeAttr('checked');
        $('input[name="is_public"]:checkbox').removeAttr('checked');
        $('#class_sectionshow').hide();
        $('#groupShow').hide();
        $.ajax({
            url: '<?php echo site_url('galleries/view_gallery_visibility_by_id'); ?>',
            type: "post",
            data: {
                'gallery_id': gallery_id
            },
            success: function(data) {
                var res = $.parseJSON(data);
                construct_gallery_visibility_lists(res.gallery_visibility);
                $('#multi_class_sections').html(construct_class_section_for_gallery(res.class_section));
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function construct_class_section_for_gallery(classSection) {
        var options = '';
        for (var i = 0; i < classSection.length; i++) {
            options += '<option value=' + classSection[i].id + '>' + classSection[i].class_name + '' + classSection[i].section_name + '</option>';
        }
        return options;
    }

    function constructCurrentVisibility(res, key) {
        var html = '';
        var m, l, temparray, chunk = 4;
        html += '<table class="table no-border">';
        for (m = 0, l = res.length; m < l; m += chunk) {
            temparray = res.slice(m, m + chunk);

            html += '<tr>';
            for (var i = 0; i < temparray.length; i++) {
                switch (key) {
                    case 'class_section':
                        var gallerySourceName = temparray[i].classSection;
                        break;
                    case 'group':
                        var gallerySourceName = temparray[i].group_name;
                        break;
                    case 'staff':
                        var gallerySourceName = 'Staff'
                        break;
                    case 'public':
                        var gallerySourceName = 'Public'
                        break;
                }

                html += '<td id="hide_td_' + temparray[i].id + '" >';
                html += '<a onclick="remove_gallery_list(' + temparray[i].id + ')" class="btn btn-lightblue contact-names"><span class="fa fa-times remove" ></span>&nbsp;&nbsp;' + gallerySourceName + '</a>';
                html += '</td>';
            }

            html += '</tr>';

        }
        html += '</table>';
        return html;
    }

    function construct_gallery_visibility_lists(res) {

        if (Object.keys(res).length > 0) {
            for (var key in res) {
                if (key == 'class_section') {
                    var result = constructCurrentVisibility(res[key], key);
                    $('#classSectionId').html(result);
                }
                if (key == 'group') {
                    var result = constructCurrentVisibility(res[key], key);
                    $('#groupId').html(result);
                }
                if (key == 'staff') {
                    $('#staffId').html(constructCurrentVisibility(res[key], key));
                }

                if (key == 'public') {
                    $('#websiteId').html(constructCurrentVisibility(res[key], key));
                }
            }
        } else {
            $('#classSectionId').html('<br><h5>None</h5>');
            $('#groupId').html('<br><h5>None</h5>');
            $('#staffId').html('<br><h5>None</h5>');
            $('#websiteId').html('<br><h5>None</h5>');
        }
    }

    function remove_gallery_list(gallery_list_id) {
        $.ajax({
            url: '<?php echo site_url('galleries/remove_gallery_list_by_id'); ?>',
            type: "post",
            data: {
                'gallery_list_id': gallery_list_id
            },
            success: function(data) {
                if (data) {
                    $('#hide_td_' + gallery_list_id).css('display', 'none');
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    }

    function handlePublishToggle(toggleElement, stngId, value) {
        // Store original state
        var originalState = toggleElement.checked;

        // If trying to publish (value = 1), show confirmation first
        if (value == '1') {
            // Temporarily revert the toggle to show confirmation
            toggleElement.checked = false;

            publishGallery(stngId, value, function(confirmed) {
                if (confirmed) {
                    // User confirmed, proceed with publish
                    toggleElement.checked = true;
                    gallelry_publish_status_check(stngId, value);
                } else {
                    // User cancelled, keep toggle off
                    toggleElement.checked = false;
                }
            });
        } else {
            // Unpublishing - do it directly
            gallelry_publish_status_check(stngId, value);
        }
    }

    function gallelry_publish_status_check(stngId, value) {
        $.ajax({
            url: '<?php echo site_url('galleries/gallery_publish_switch_status'); ?>',
            type: "post",
            data: {
                'stngId': stngId,
                'value': value
            },
            success: function(data) {
                var res = data.trim();
                console.log(res)
                if (res == '1') {
                    if (value == '0') {
                        $(function() {
                            new PNotify({
                                title: 'Success',
                                text: 'Un-published successfully',
                                type: 'success',
                            });
                        });
                    } else {
                        $(function() {
                            new PNotify({
                                title: 'Success',
                                text: 'Published successfully',
                                type: 'success',
                            });
                        });
                    }
                } else {
                    $(function() {
                        new PNotify({
                            title: 'Error',
                            text: 'Something went wrong',
                            type: 'error',
                        });
                    });
                    // Revert toggle on error
                    var toggle = document.getElementById('publish_toggle_' + stngId);
                    if (toggle) {
                        toggle.checked = !toggle.checked;
                    }
                }
            },
            error: function(err) {
                console.log(err);
                $(function() {
                    new PNotify({
                        title: 'Error',
                        text: 'Something went wrong',
                        type: 'error',
                    });
                });
                // Revert toggle on error
                var toggle = document.getElementById('publish_toggle_' + stngId);
                if (toggle) {
                    toggle.checked = !toggle.checked;
                }
            }
        });
    }


    function delete_gallery(gId) {
        var galleryName = $('#galleryDeletedId' + gId).data("galleryname");
        bootbox.confirm({
            title: "Confirm",
            message: "Are you sure about deleting <strong>" + galleryName + "</strong> Gallery?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('galleries/delete_gallery'); ?>',
                        type: 'post',
                        data: {
                            'gId': gId
                        },
                        success: function(data) {
                            if (data == 1) {
                                //alert("Done");
                                location.reload();
                            } else {
                                alert("failed");
                            }
                        }
                    });
                }
            }
        });
    }

    function publishGallery(id, status, callback) {
        var msg = "If the visibility is set to Student or Staff Notifications will be sent, DO you want to proceed?";
        bootbox.confirm({
            title: "Confirm",
            message: msg,
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-success'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-danger'
                }
            },
            callback: function(result) {
                if (result) {
                    $.ajax({
                        url: '<?php echo site_url('galleries/send_notification_for_publish_gallery'); ?>',
                        type: 'post',
                        data: {
                            'gId': id,
                            'status': status
                        },
                        success: function(data) {
                            console.log(data);
                            if (callback) callback(true);
                        }
                    });
                } else {
                    // User clicked "No"
                    if (callback) callback(false);
                }
            }
        });
    }
</script>

<style>
    .btn_align {
        margin-bottom: 4px;

    }

    .sorting:before {
        content: none;
    }

    .sorting_asc:before {
        content: none;
    }

    .table>thead>tr>th {
        vertical-align: top;
    }

    .new_circleShape_res {
        padding: 8px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;

    }

    .new_circleRes {
        padding: .4rem;
        border-radius: 50% !important;
        /* color: white !important; */
        font-size: 17px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
        background-color: white;
    }

    .new_circleResVidAud {
        padding: .4rem .7rem;
        border-radius: 50% !important;
        /* color: white !important; */
        font-size: 17px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
        background-color: white;
    }

    .new_circleShape_res1 {
        padding: 5px 8px;
        border-radius: 50% !important;
        font-size: 16px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 0rem !important;
        background-color: white;
    }

    .loader-background {
        width: 100%;
        height: 100%;
        position: absolute;
        display: none;
        top: 0;
        left: 0;
        opacity: 0.8;
        z-index: 10;
        background-color: #fff;
        border-radius: 8px;
    }

    @media (max-width: 768px) {
        .modal-dialog {
            width: 100%;
            margin: auto;
        }

        .container{
            width: 100%;
        }
    }

    @media (min-width: 768px) {
        .modal-dialog {
            width: 50%;
            margin: auto;
        }

        .container{
            width: 87%;
        }
    }

    .parsley-error
    {
      color: #B94A48 !important;
      background-color: #F2DEDE !important;
      border: 1px solid #EED3D7 !important;
    }

</style>