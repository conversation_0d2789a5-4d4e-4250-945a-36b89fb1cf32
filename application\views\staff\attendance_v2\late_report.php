<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance');?>">Staff Attendance</a></li>
  <li>Late Report</li>
</ul>

<div class="col-md-12">
  	<div class="card cd_border">
	    <div class="card-header panel_heading_new_style_staff_border">
	      <div class="row" style="margin: 0px;">
	        <div class="d-flex justify-content-between" style="width:100%;">
	          <h3 class="card-title panel_title_new_style_staff">
	            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
	              <span class="fa fa-arrow-left"></span>
	            </a> 
	            Late Report
	          </h3>
	        </div>
	      </div>
	    </div>
    	<div class="card-body pt-1">

            <div class="col-md-2 form-group">
                <label for="fromdateId" class="control-label">Attendance range</label>
                <div id="reportrange" class="dtrange" style="width: 100%">                                            
                    <span></span>
                    <input type="hidden" id="from_date">
                    <input type="hidden" id="to_date">
                </div>
            </div>

            <div class="col-md-2 form-group">
                <label class="control-label">Staff Type</label>
                <select class="form-control select" name="selected_staff_type" id="selected_staff_type">
                    <option value="all">All</option>
                    <?php foreach ($staff_types as $key=>$val) {
                            echo "<option value='$key'>$val</option>";
                        }
                    ?>
                </select>
            </div>

            <div class="col-md-2 form-group">
              <label class="control-label">Staff Status Type</label>
              <select class="form-control select" name="staff_status_type" id="staff_status_type" onChange="hideDataArea()">
                <!-- <option value="all">All</option> -->
                  <option value='2'>Approved</option>
                  <option value='4'>Resigned</option>
              </select>
            </div>

            <div class="col-md-3 form-group pt-3">
                <button class="btn btn-primary mt-3" onclick="getLateData()">Get Report</button>
            </div>
    	</div>
        <div class="card-body pt-1">
            <div id="late-attendance-data"></div>
        </div>
  	</div>
</div>


<style>
  /* styles over here */
  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
</style>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script language="javascript">
    $(document).ready(function() {

        $("#reportrange").daterangepicker({
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                // 'This Month': [moment().startOf('month'), moment()],
                // 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            },
            dateLimit: {
            'months': 1,
            'days': 0
            },
            opens: 'right',
            buttonClasses: ['btn btn-default'],
            applyClass: 'btn-small btn-primary',
            cancelClass: 'btn-small',
            format: 'DD-MM-YYYY',
            separator: ' to ',
            startDate: moment().subtract(6, 'days'),
            endDate: moment()            
            },function(start, end) {
            $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
            $('#from_date').val(start.format('DD-MM-YYYY'));
            $('#to_date').val(end.format('DD-MM-YYYY'));
        });

        $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
        $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
        $('#to_date').val(moment().format('DD-MM-YYYY'));
    });

    function hideDataArea(){
      $("#late-attendance-data").html("");
    }

    function getLateData() {
        const msg = `
            <div style="color:red;text-align:center;
              color: black;
              border: 2px solid #fffafa;
              text-align: center;
              border-radius: 6px;
              position: relative;
              margin-left: 14px;
              padding: 10px;
              font-size: 14px;
              margin-top: 14px;
              ">
                Loading...
            </div>`;

	    $("#late-attendance-data").html(msg);

        var from_date = $("#from_date").val();
        var to_date = $("#to_date").val();
        var selected_staff_type = $("#selected_staff_type").val();
        
        var staff_status_type = $("#staff_status_type").val();

        $.ajax({
            url: '<?php echo site_url('staff/attendance/getLateReport'); ?>',
            type: 'post',
            data: {'from_date':from_date, 'to_date': to_date, 'selected_staff_type': selected_staff_type,'staff_status_type':staff_status_type},
            success: function(data) {
                var data = JSON.parse(data);

                output = construct_table (data);
			    $("#late-attendance-data").html(output);

          const reportName=`staff_attendance_late_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

                $('#late_table').DataTable( {
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
						"pageLength": 10,
				dom: 'lBfrtip',
				buttons: [
					{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: reportName,
					className: 'btn btn-info'
					},
					{
					extend: 'csvHtml5',
					text: 'CSV',
					filename: reportName,
					className: 'btn btn-info'
					},
					{
					extend: 'pdfHtml5',
					text: 'PDF',
					filename: reportName,
					className: 'btn btn-info'
					}
				]
        	});
            }
        });
    }

    function construct_table (data) {
        var output = `
            <table class="table table-bordered table-striped" id="late_table">
                <thead>
                    <th width="5%">Sl #</th>
                    <th width="25%">Staff</th>
                    <th width="10%"># Of Days Late</th>
                    <th width="30%">Late days</th>
                    <th width="30%">Total Late time</th>
                </thead>
                `;

        var i = 1;
        data.forEach(staff => {
            const timeInMinutes=+staff.late_dates===0 && "0" || staffMinutesLate(staff.late_time).join(",");

            const lateTimeArray=timeInMinutes.toString().split(",");
            
            let totalLateTime=0;

            lateTimeArray?.forEach(t=>{
              totalLateTime+=+t;
            });

            const lateDatesArr=staff.late_dates.toString().split(",");
           
            output += `
                <tr>
                    <td>${i++}</td>
                    <td>${staff.staff_name}</td>
                    <td>${staff.number_late_days}</td>`;
            
            output +=`<td>`;

            lateDatesArr?.forEach((d,i,arr)=>{
             output +=`${lateDatesArr[i]} (${lateTimeArray[i]} mins)${++i!=arr.length && "," || ""} `;
            });

            output +=`</td>`;
            
            output +=` <td>${totalLateTime} min(s)</td></tr>
            `;
        });

        output += `
                </tbody>
            </table>
        `;

        return output;
    }

    function staffMinutesLate(time){
        const timeArray=time?.toString().split(",");
        const timeInMunute=[];

        timeArray?.forEach((t,i)=>{
            if(t.toString().at(0)==="-"){
                timeInMunute[i]=0;
            }else{
                let [hour, min, sec]=t.toString().split(":");
                hour=+hour*60;
                sec=+sec/60;

                const time=+hour + +min + +sec;
                timeInMunute[i]=`${time.toFixed(0)}`;
            }
        });

        return timeInMunute;
    }

</script>
