<?php
defined('BASEPATH') OR exit('No direct script access allowed');
            
class Texting_model extends CI_Model {
    private $yearId;
    public function __construct() {
      parent::__construct();
    }

    private function __StudentType($members, $send_to) {
      $acadYearId = $this->acad_year->getAcadYearId();
      $this->db_readonly->select("s.id, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS name, p.mobile_no, u.token, u.id, p.email");
      $this->db_readonly->from('student_admission s');
      $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
      $this->db_readonly->join('class_section cs', 'cs.id=sy.class_section_id');
      $this->db_readonly->join('parent p', 'p.student_id=s.id');
      $this->db_readonly->join('student_relation sr', 'sr.relation_id=p.id');
      $this->db_readonly->join('avatar a', 'a.stakeholder_id=p.id');
      $this->db_readonly->join('users u', 'u.id=a.user_id');
      if($send_to != 'Both') {
        $this->db_readonly->where('sr.relation_type',$send_to);
      }
      $this->db_readonly->where('admission_status','2'); // Approved 2
      $this->db_readonly->where('sy.promotion_status!=', '4'); 
      $this->db_readonly->where('sy.promotion_status!=', '5'); 
      $this->db_readonly->where('a.avatar_type', '2');
      $this->db_readonly->where('sy.acad_year_id', $acadYearId);
      $this->db_readonly->where_in('s.id', $members);
      $this->db_readonly->order_by('cs.class_name, cs.section_name,s.first_name', 'ASC');
      $students = $this->db_readonly->get()->result();
      return $students;
    }

    private function __ClassType($members, $send_to, $batch, $acad_year) {
      $this->db_readonly->select("s.id, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS name, p.mobile_no, u.token, u.id, p.email");
      $this->db_readonly->from('student_admission s');
      $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
      $this->db_readonly->join('class_section cs', 'cs.id=sy.class_section_id');
      $this->db_readonly->join('parent p', 'p.student_id=s.id');
      $this->db_readonly->join('student_relation sr', 'sr.relation_id=p.id');
      $this->db_readonly->join('avatar a', 'a.stakeholder_id=p.id');
      $this->db_readonly->join('users u', 'u.id=a.user_id');
      if($send_to != 'Both') {
        $this->db_readonly->where('sr.relation_type',$send_to);
      }
      $this->db_readonly->where('admission_status','2');
      $this->db_readonly->where('sy.promotion_status!=', '4'); 
      $this->db_readonly->where('sy.promotion_status!=', '5'); 
      $this->db_readonly->where('a.avatar_type', '2');
      $this->db_readonly->where_in('sy.class_section_id',$members);
      if($batch != 0)
        $this->db_readonly->where('s.admission_acad_year_id',$batch);
      $this->db_readonly->where('sy.acad_year_id', $acad_year);
      $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
      $students = $this->db_readonly->get()->result();
      return $students;
    }

    private function __StaffType($members) {
      $this->db_readonly->select("st.id, CONCAT(ifnull(st.first_name,''), ' ', ifnull(st.last_name,'')) AS name, st.contact_number as mobile_no, u.token, u.id, u.email");
      $this->db_readonly->from('staff_master st');
      $this->db_readonly->join('avatar a', 'a.stakeholder_id=st.id');
      $this->db_readonly->join('users u', 'u.id=a.user_id');
      $this->db_readonly->where_in('st.id', $members);
      $this->db_readonly->where('a.avatar_type', '4');
      $this->db_readonly->order_by('st.first_name', 'ASC');
      $staff = $this->db_readonly->get()->result();
      return $staff;
    }

    private function __StaffAllType() {
      $this->db_readonly->select("st.id, CONCAT(ifnull(st.first_name,''), ' ', ifnull(st.last_name,'')) AS name, st.contact_number as mobile_no, u.token, u.id, u.email");
      $this->db_readonly->from('staff_master st');
      $this->db_readonly->join('avatar a', 'a.stakeholder_id=st.id');
      $this->db_readonly->join('users u', 'u.id=a.user_id');
      $this->db_readonly->where('st.status', '2');
      $this->db_readonly->where('a.avatar_type', '4');
      $this->db_readonly->order_by('st.first_name', 'ASC');
      $staff = $this->db_readonly->get()->result();
      return $staff;
    }

    public function getPreview($acadYearId){
      $input = $this->input->post();
      // echo "<pre>"; print_r($input);die();
      $userType = $input['selectionType'];
      $custom_str = $input['customNumbers'];
      $send_to = $input['sendTo'];
      $batch = $input['batch'];
      $members = array();
      if(isset($input['members']))
        $members = $input['members'];

      $membersData = $this->constructPreview($members, $userType, $send_to, $batch, $acadYearId);

      if($custom_str != ""){
        $custom_numbers = explode(",", $custom_str);
        foreach ($custom_numbers as $val) {
          $membersData[]=(object) array("id" => "-1", "name" => "Custom Number", "mobile_no" => $val, "token" => null);
        }
      }

      return $membersData;
    }

    public function constructPreview($members, $userType, $send_to='both', $batch=0, $acadYearId=0) {
      if($acadYearId == 0)
        $acadYearId = $this->acad_year->getAcadYearId();
      $membersData = array();
      switch ($userType) {
        case 'Student':
          $membersData = $this->__StudentType($members, $send_to);
          break;
        case 'Class':
          $membersData = $this->__ClassType($members, $send_to, $batch, $acadYearId);
          break;
        case 'Staff':
          $membersData = $this->__StaffType($members);
          break;
        case 'Staff_all':
          $membersData = $this->__StaffAllType();
          break;
        default:
          break;
      }
      return $membersData;
    }

    public function getStudentIdsBySection($members, $send_to, $batch, $acad_year=0) {
      if($acad_year == 0) {
        $acad_year = $this->acad_year->getAcadYearId();
      }
      $this->db_readonly->select("s.id");
      $this->db_readonly->from('student_admission s');
      $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
      $this->db_readonly->join('parent p', 'p.student_id=s.id');
      $this->db_readonly->join('student_relation sr', 'sr.relation_id=p.id');
      if($send_to != 'Both') {
        $this->db_readonly->where('sr.relation_type',$send_to);
      }
      $this->db_readonly->where('s.admission_status','2');
      $this->db_readonly->where('sy.promotion_status!=', '4'); 
      $this->db_readonly->where('sy.promotion_status!=', '5'); 
      $this->db_readonly->where_in('sy.class_section_id',$members);
      if($batch != 0)
        $this->db_readonly->where('s.admission_acad_year_id',$batch);
      $this->db_readonly->where('sy.acad_year_id', $acad_year);
      $ids = $this->db_readonly->get()->result();
      $students = array();
      foreach ($ids as $key => $val) {
        array_push($students, $val->id);
      }
      return $students;
    }

    public function getClassSectionNames($section_ids) {
      $result = $this->db_readonly->select("CONCAT(cs.class_name,cs.section_name) as csName")
      ->from('class_section cs')
      ->where_in('cs.id', $section_ids)
      ->get()->result();
      $sections = array();
      foreach ($result as $key => $value) {
        array_push($sections, $value->csName);
      }
      return $sections;
    }

    public function getIdsWithTokens($stakeholder_ids, $user_type, $send_to) {
      $data = array();
      if(empty($stakeholder_ids))
        return $data;
      if($user_type == 'Student') {
        $this->db_readonly->select("s.id");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->join('parent p', 'p.student_id=s.id');
        $this->db_readonly->join('student_relation sr', 'sr.relation_id=p.id');
        $this->db_readonly->join('avatar a', 'a.stakeholder_id=p.id');
        $this->db_readonly->join('users u', 'u.id=a.user_id');
        if($send_to != 'Both') {
          $this->db_readonly->where('sr.relation_type',$send_to);
        }
        $this->db_readonly->where('admission_status','2'); // Approved 2
        $this->db_readonly->where('sy.promotion_status!=', '4'); 
        $this->db_readonly->where('sy.promotion_status!=', '5'); 
        $this->db_readonly->where('a.avatar_type', '2');
        $this->db_readonly->where_in('s.id', $stakeholder_ids);
        $this->db_readonly->where('u.token is not null');
        $data = $this->db_readonly->get()->result();
      } else {
        $this->db_readonly->select("st.id");
        $this->db_readonly->from('staff_master st');
        $this->db_readonly->join('avatar a', 'a.stakeholder_id=st.id');
        $this->db_readonly->join('users u', 'u.id=a.user_id');
        $this->db_readonly->where_in('st.id', $stakeholder_ids);
        $this->db_readonly->where('a.avatar_type', '4');
        $this->db_readonly->where('u.token is not null');
        $data = $this->db_readonly->get()->result();
      }

      $ids = array();
      foreach ($data as $key => $val) {
        array_push($ids, $val->id);
      }
      return $ids;
    }

    public function saveNotification($stakeholder_ids, $title, $message, $url, $sent_to, $acad_year, $source) {
      $sent_by = $this->authorization->getAvatarId();
      $master = array(
        'title' => $title,
        'content' => $message,
        'sent_to' => $sent_to,
        'sent_by' => $sent_by,
        'url' => $url,
        'acad_year_id' => $acad_year,
        'source' => $source
      );

      $this->db->trans_start();
      $this->db->insert('notification_master', $master);
      $master_id = $this->db->insert_id();

      foreach ($stakeholder_ids as $key => $val) {
        $data[] = array(
          'notification_master_id' => $master_id,
          'stakeholder_id' => $val->id,
          'status' => $val->tokenState
        );
      }

      $this->db->insert_batch('notification_sent_to', $data);
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return 0;
      } else {
        $this->db->trans_commit();
        return 1;
      }
    }

    public function getParentIds($student_ids, $send_to) {
        if(empty($student_ids))
            return array();
        $this->db_readonly->select('p.id, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState')
        ->from('student_admission sa')
        ->join('parent p', 'p.student_id=sa.id')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->join('avatar a', 'a.stakeholder_id=p.id')
        ->join('users u', 'u.id=a.user_id')
        ->where_in('sa.id', $student_ids)
        ->where('a.avatar_type', 2);
        if($send_to != 'Both') {
            $this->db_readonly->where('sr.relation_type', $send_to);
        }
        $users = $this->db_readonly->get()->result();
        return $users;
    }

    public function getStaffIds($staff_ids) {
      $users = $this->db_readonly->select('sm.id, sm.contact_number as mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState')
          ->from('staff_master sm')
          ->join('avatar a', 'a.stakeholder_id=sm.id')
          ->join('users u', 'u.id=a.user_id')
          ->where_in('sm.id', $staff_ids)
          ->where('a.avatar_type', 4)
          ->get()->result();
      return $users;
    }

    public function getRemainingSMSCredits(){
      $this->db_readonly->select("sum(case when action='Load' then sms_credits else 0 end) as loaded, sum(case when action='Unload' then sms_credits else 0 end) as unloaded");
      $result = $this->db_readonly->get("texting_credits_usage")->row();
      if(empty($result)) {
        return 0;
      } else {
        return ($result->loaded - $result->unloaded);
      }
    }

    public function checkCredits($credits) {
      $availableCredits = $this->getRemainingSMSCredits();
        if($availableCredits < $credits) {
          return 0;
        }
        return 1;
    }

    public function _calculateCredits($message, $isUnicode){
      $msg_length = strlen(utf8_decode($message));
      $creditLengths = $this->settings->getSetting('sms_credit_length');
        $credits = 1;
        if($isUnicode) {
          $single = $creditLengths->unicode_single;
          $multi = $creditLengths->unicode_multi;
          if($msg_length > $single) {
            $credits = ceil($msg_length/$multi);
          }
        } else {
          $single = $creditLengths->non_unicode_single;
          $multi = $creditLengths->non_unicode_multi;
          if($msg_length > $single) {
            $credits = ceil($msg_length/$multi);
          }
        }
        return $credits;
    }

    public function saveTexting($masterData, $reciever_ids) {
      $this->db->trans_start();
      $this->db->insert('texting_master', $masterData);
      $master_id = $this->db->insert_id();

      $mode = $masterData['mode'];
      $sms_data = array();
      $staff_tokens = array();
      $student_tokens = array();
      $sms_count = 0;
      foreach ($reciever_ids as $k => $reciever) {
        $subData = array(
          'texting_master_id' => $master_id,
          'stakeholder_id' => $reciever->id,
          'mobile_no' => $reciever->mobile_no,
          'mode' => 1,
          'status' => 'Sent',
          'avatar_type' => $reciever->avatar_type,
          'is_read' => 0
        );
        $isSmsNumber = 0;
        if($mode == 'sms' || ($mode == 'notification_sms' && $reciever->tokenState == 0)) {
          $subData['mode'] = 2;
          $isSmsNumber = 1;
          $subData['status'] = 'AWAITED-DLR';
          if($reciever->mobile_no == '' || $reciever->mobile_no == NULL) {
            $subData['status'] = 'NO-NUMBER';
            $isSmsNumber = 0;
          }
        } 
        else if($mode == 'notification' && $reciever->tokenState == 0) {
          $subData['status'] = 'No Token';
        } 

        $this->db->insert('text_sent_to', $subData);
        $insId = $this->db->insert_id();
        if($isSmsNumber) {
          $sms_count++;
          $sms_data[] = array(
            'text_sent_to_id' => $insId,
            'mobile_no' => $reciever->mobile_no
          );
        }

        if(($mode == 'notification' || $mode == 'notification_sms') && $reciever->tokenState) {
          if($reciever->avatar_type == 4) {
            $staff_tokens[] = array(
              'user_id' => $reciever->user_id,
              'token' => $reciever->token,
              'text_sent_to_id' => $insId
            );
          } else {
            $student_tokens[] = array(
              'user_id' => $reciever->user_id,
              'token' => $reciever->token,
              'text_sent_to_id' => $insId
            );
          }
        }
      }

      // this is causing unnessary sms credit usage counting issue
      // $credits = $sms_count * $masterData['sms_credits'];
      // $texting_credits = array(
      //   'sms_credits' => $credits,
      //   'action' => 'Unload',
      //   'action_by' => $masterData['sent_by'],
      //   'texting_master_id' => $master_id
      // );

      // $this->db->insert('texting_credits_usage', $texting_credits);

      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return array();
      }
      $this->db->trans_commit();
      return array('sms_data' => $sms_data, 'staff_tokens' => $staff_tokens, 'student_tokens' => $student_tokens, 'master_id' => $master_id);
    }

    public function getTextSentCount($master_id) {
      return $this->db_readonly->select("sum(case when mode=1 and avatar_type=2 then 1 else 0 end) as std_notifications, sum(case when mode=1 and avatar_type=4 then 1 else 0 end) as staff_notifications, sum(case when mode=2 and avatar_type=2 then 1 else 0 end) as std_sms,sum(case when mode=2 and avatar_type=4 then 1 else 0 end) as staff_sms")->where('texting_master_id', $master_id)->get('text_sent_to')->row();
    }

    public function updateErrorStatus($id){
      $this->db->where('s.texting_master_id', $id);
      $this->db->where('s.mode', 2);
      return $this->db->update('text_sent_to s', array("status" => 'Unknown'));
    }

    public function updateUniqueTextErrorStatus($ids){
      $this->db->where_in('s.texting_master_id', $ids);
      $this->db->where('s.mode', 2);
      return $this->db->update('text_sent_to s', array("status" => 'Unknown'));
    }


    public function updateTextResponse($res, $master_id){
      $msgId = isset($res['msgid']) ? $res['msgid'] : null;
      $message = isset($res['message']) ? $res['message'] : null;

      $this->db->trans_start();
      $this->db->where('id', $master_id);
      $this->db->update('texting_master', array('msg_id' => $msgId, 'sending_status' => $message));

      $updateData = array();
      foreach($res['data'] as $entry){
        $updateData[] = array(
            'id' => $entry['textsentToId'],
            'response_id' => trim($entry['id']),
            'status' => trim($entry['status'])
        );
      }
      if (!empty($updateData)) {
          $this->db->update_batch('text_sent_to', $updateData, 'id');
      }
      return $this->db->trans_complete();
    }

    private function _formatDate($sDate) {
      $d = strtotime($sDate);
      // if ($d >= strtotime("today"))
      //   return "Today";
      // else if ($d >= strtotime("yesterday"))
      //   return "Yesterday";
      return $sDate;
    }

    private function getStaffNameByAvatarId($avatarId){
      $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$avatarId)
        ->get()->row();
      if (!empty($collected)) {
        return $collected->staffName;
      }else{
        return 'Admin';
      }
    }

    public function getTextReport($from, $to) {
      $result = $this->db_readonly->select("tm.*, DATE_FORMAT(CONVERT_TZ(tm.sent_on, @@session.time_zone, '+05:30'), '%d-%m-%Y %h:%i %p') as sent_date", FALSE)
      ->from('texting_master tm')
      // ->join('avatar a', 'a.id=tm.sent_by')
      // ->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left')
      // ->where('(a.avatar_type=4 OR a.avatar_type=3)')
      ->where("DATE(tm.sent_on) between '$from' and '$to'")
      ->where('tm.visible', 1)
      ->order_by("tm.id", "DESC")
      ->get()->result();
      // echo $this->db_readonly->last_query();
      if(empty($result)) {
        return $result;
      }
      $textData = array();
      $masterIds = array();
      foreach ($result as $key => $res) {
        $masterIds[] = $res->id;
        // $res->sent_date = $this->_formatDate($res->sent_date);
        $res->sender = $this->getStaffNameByAvatarId($res->sent_by);
        $textData[$res->id] = $res;
      }
      // echo "<pre>";print_r($masterIds);die();

      $sent = $this->db_readonly->select("ts.texting_master_id, count(ts.id) as total, ts.mode, ts.status")
      ->from('text_sent_to ts')
      ->where_in('ts.texting_master_id', $masterIds)
      ->group_by('ts.texting_master_id, ts.mode, ts.status')
      ->order_by("ts.texting_master_id", "DESC")
      ->get()->result();

      $sentData = array();
      foreach ($sent as $key => $cs) {
        $msId = $cs->texting_master_id;
        if(!array_key_exists($msId, $sentData)) {
          $sentData[$msId] = array();
          $sentData[$msId]['notifications'] = 0;
          $sentData[$msId]['sms'] = 0;
          $sentData[$msId]['delivered'] = 0;
          $sentData[$msId]['awaited'] = 0;
          $sentData[$msId]['not_delivered'] = 0;
          $sentData[$msId]['no_number'] = 0;
          $sentData[$msId]['no_token'] = 0;
          $sentData[$msId]['sent'] = 0;
          $sentData[$msId]['failed'] = 0;
          $sentData[$msId]['not_acknowledged'] = 0;
        }
        if($cs->mode == 1) {
          $sentData[$msId]['notifications'] += $cs->total;
          if($cs->status == 'No Token') {
            $sentData[$msId]['no_token'] += $cs->total;
          } else if($cs->status == 'Sent') {
            $sentData[$msId]['sent'] += $cs->total;
          } else {
            $sentData[$msId]['failed'] += $cs->total;
          } 
        } else {
          $sentData[$msId]['sms'] += $cs->total;
          if($cs->status == 'AWAITED-DLR') {
            $sentData[$msId]['awaited'] += $cs->total;
          } else if($cs->status == 'DELIVRD' || $cs->status == 'Delivered' || $cs->status == 'DELIVRD_UNLOAD_ADDED') {
            $sentData[$msId]['delivered'] += $cs->total;
          } else if($cs->status == 'NO-NUMBER') {
            $sentData[$msId]['no_number'] += $cs->total;
          } else if(($cs->status == 'Unknown' || $cs->status == 0 || $cs->status == '') && $cs->status != 'Failed') {
            $sentData[$msId]['not_acknowledged'] += $cs->total;
          } else {
            $sentData[$msId]['not_delivered'] += $cs->total;
          }
        }
      }

      // echo "<pre>"; print_r($sentData);die();
      $data = array();
      foreach ($textData as $master_id => $text) {
        if(array_key_exists($master_id, $sentData)) {
          $text->status = $sentData[$master_id];
          $data[] = $text;
        }
      }
      // echo "<pre>"; print_r($textData);die();
      return $data;
    }

    public function getUnDeliveredTexts($data = []) {
      //update sms older than 2days to 'Status not available' if it is awaited delivery
      $date = date('Y-m-d', strtotime('-2 days'));
      $this->db->where("texting_master_id in (select id from texting_master where DATE_FORMAT(sent_on, '%Y-%m-%d')<='$date')");
      $this->db->where("status", 'AWAITED-DLR');
      $this->db->where("mode", 2);
      $this->db->update('text_sent_to', array('status' => 'NO-DLR-OPTR'));

      $this->db->select("ts.response_id as msgId, ts.id as text_sent_to_id");
      $this->db->from('text_sent_to ts');
      if(!empty($data) && !empty($data['master_id'])) {
        $this->db->where('ts.texting_master_id', $data['master_id']);
      } else {
        $this->db->join('texting_master tm', 'tm.id=ts.texting_master_id');
        $this->db->where('date(tm.sent_on)', $date);
      }
      $this->db->where_in('ts.status', ['AWAITED-DLR', 'SUBMITTED']);
      $this->db->where('ts.mode', 2);
      $this->db->where('ts.response_id IS NOT NULL');
      $result = $this->db->get()->result();

      $msgIds = [];
      $responseMap = [];
      foreach ($result as $row) {
          if (!empty($row->msgId)) {
              $msgIds[] = $row->msgId;
              $responseMap[$row->msgId] = $row->text_sent_to_id;
          }
      }
      return [
          'msgIds' => $msgIds,
          'responseMap' => $responseMap
      ];
    }

    public function updateTextStatus($finalResult){
        $data = array();
        foreach ($finalResult as $row) {
            $timezone = new DateTimeZone('Asia/Kolkata');
            $dt = DateTime::createFromFormat('Y-m-d h:i a', $row['delivered_date'], $timezone);
            $deliveredDate = $dt ? $dt->format('Y-m-d H:i:s') : null;
            $data[] = array(
                'id' => $row['text_sent_to_id'],
                'status' => trim($row['updatedStatus']),
                'remarks' => $row['remarks'],
                'delivered_date' => trim($row['updatedStatus']) == 'Delivered' ? $deliveredDate : null
            );
        }
        return $this->db->update_batch('text_sent_to', $data, 'id');
    }

    public function getTextMaster($master_id) {
        return $this->db_readonly->select("tm.*, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name, '')) as sentBy, DATE_FORMAT(CONVERT_TZ(tm.sent_on, @@session.time_zone, '+05:30'), '%d-%m-%Y %h:%i %p') as sent_date", FALSE)
        ->from('texting_master tm')
        ->join('avatar a', 'a.id=tm.sent_by')
        ->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left')
        ->where('tm.id', $master_id)
        ->get()->row();
    }

   public function getSentToStudent($master_id) {
    $result = $this->db_readonly->select("ts.mobile_no, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,''), ' (',sr.relation_type,' Of ' ,ifnull(sa.first_name,''),' ',ifnull(sa.last_name,''), ')') as name, ts.mode, ts.status")
    ->from('text_sent_to ts')
    ->where('ts.texting_master_id', $master_id)
    ->join('parent p', 'p.id=ts.stakeholder_id')
    ->join('student_relation sr', 'sr.relation_id=p.id')
    ->join('student_admission sa', 'sr.std_id=sa.id')
    ->get()->result();
    return $result;
   }

   public function getSentToSatff($master_id) {
    $result = $this->db_readonly->select("ts.mobile_no, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, ts.mode, ts.status")
    ->from('text_sent_to ts')
    ->where('ts.texting_master_id', $master_id)
    ->join('staff_master sm', 'sm.id=ts.stakeholder_id')
    ->get()->result();
    return $result;
   }

   public function getSentToCustom($master_id) {
    $result = $this->db_readonly->select("ts.mobile_no, 'Custom Number' as name, ts.mode, ts.status")
    ->from('text_sent_to ts')
    ->where('ts.texting_master_id', $master_id)
    ->get()->result();
    return $result;
   }

    public function getRecievers($master_id) {
        $acadYearId = $this->acad_year->getAcadYearId();
        $students = $this->db_readonly->select("CONCAT(cs.class_name,cs.section_name,' : ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (Preferred)') AS name,ts.mobile_no, ts.mode, ts.status, ts.remarks, IFNULL(DATE_FORMAT(ts.delivered_date, '%d-%m-%Y %h:%i %p'), '-') as delivered_date")
        ->from('text_sent_to ts')
        ->join('student_admission s', 's.id=ts.stakeholder_id')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->where('sy.acad_year_id', $acadYearId)
        ->where('ts.avatar_type', 1)
        ->where('ts.texting_master_id', $master_id)
        ->order_by('cs.class_name,cs.section_name,s.first_name')
        ->get()->result();

        $parents = $this->db_readonly->select("ts.mobile_no, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,''), ' (',sr.relation_type,' : ' ,ifnull(p.first_name,''),' ',ifnull(p.last_name,''), ')') as name, ts.mode, ts.status, ts.remarks, IFNULL(DATE_FORMAT(ts.delivered_date, '%d-%m-%Y %h:%i %p'), '-') as delivered_date")
        ->from('text_sent_to ts')
        ->join('parent p', 'p.id=ts.stakeholder_id')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->join('student_admission sa', 'sa.id=sr.std_id')
        ->where('ts.texting_master_id', $master_id)
        ->where('ts.avatar_type', 2)
        ->order_by('sa.first_name')
        ->get()->result();

        $staff = $this->db_readonly->select("ts.mobile_no, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,''), ' (Staff)') as name, ts.mode, ts.status, ts.remarks, IFNULL(DATE_FORMAT(ts.delivered_date, '%d-%m-%Y %h:%i %p'), '-') as delivered_date")
        ->from('text_sent_to ts')
        ->join('staff_master sm', 'sm.id=ts.stakeholder_id')
        ->where('ts.texting_master_id', $master_id)
        ->where('ts.avatar_type', 4)
        ->get()->result();

        $custom = $this->db_readonly->select("ts.mobile_no, 'Custom Number' as name, ts.mode, ts.status")
        ->from('text_sent_to ts')
        ->where('ts.texting_master_id', $master_id)
        ->where('ts.avatar_type', 0)
        ->get()->result();

        $result = array();
        if(!empty($students)) {
          $result = $students;
        }
        if(!empty($parents)) {
          $result = array_merge($result, $parents);
        }
        if(!empty($staff)) {
          $result = array_merge($result, $staff);
        }
        if(!empty($custom)) {
          $result = array_merge($result, $custom);
        }
        return $result;
    }

  public function getStudents($members, $send_to, $callFrom = 'Internal') {
      $acadYearId = NULL;
      if($callFrom == 'Internal'){
          $acadYearId = $this->acad_year->getAcadYearId();
      } else {
          $acadYearId = $this->settings->getSetting('academic_year_id');
      }

      if($send_to == 'preferred') {
        //this block is for preferred number
        $this->db_readonly->select("s.id, s.id as std_id, 1 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS Name, s.preferred_contact_no as mobile_no, 'NULL' as user_id, 'NULL' as token, 0 as tokenState,s.email");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->join('class_section cs', 'cs.id=sy.class_section_id');
        $this->db_readonly->where('admission_status','2'); // Approved 2
        $this->db_readonly->where('sy.promotion_status!=', '4'); 
        $this->db_readonly->where('sy.promotion_status!=', '5'); 
        $this->db_readonly->where_in('s.id', $members);
        $this->db_readonly->where('sy.acad_year_id', $acadYearId);
        $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
        $students = $this->db_readonly->get()->result();
        return $students;
        //echo "<pre>"; print_r($students); die();
      }

      if($send_to == 'preferred_parent') {
        //this query gets all the students for which preferred parent is Father/Mother
        $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email, s.preferred_parent")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->join('parent p', 'p.student_id=s.id')
        ->join('student_relation sr', 'sr.relation_id=p.id and sr.relation_type=s.preferred_parent')
        ->join('avatar a', 'a.stakeholder_id=p.id')
        ->join('users u', 'u.id=a.user_id')
        ->where('admission_status','2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where('a.avatar_type','2')
        ->where('sy.acad_year_id', $acadYearId)
        ->where_in('s.id', $members);
        if($this->settings->getSetting('include_guardian_in_student_communications') != 1){
          $this->db_readonly->where_in('sr.relation_type', array('Father', 'Mother'));
        }
        $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
        $students = $this->db_readonly->get()->result();

        //this query gets all the students for which preferred parent is Both
        $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email, s.preferred_parent")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->join('parent p', 'p.student_id=s.id')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->join('avatar a', 'a.stakeholder_id=p.id')
        ->join('users u', 'u.id=a.user_id')
        ->where('admission_status','2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where("(s.preferred_parent='' OR s.preferred_parent='Both')")
        ->where('a.avatar_type','2')
        ->where('sy.acad_year_id', $acadYearId)
        ->where_in('s.id', $members);
        if($this->settings->getSetting('include_guardian_in_student_communications') != 1){
          $this->db_readonly->where_in('sr.relation_type', array('Father', 'Mother'));
        }
        $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
        $stds = $this->db_readonly->get()->result();

        $students = array_merge($students, $stds);
        // usort($students, function($a, $b) { return strcmp($a->Name, $b->Name); });
        return $students;        
      }

      // $this->db->select("s.id, 1 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email")
      $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email")
      ->from('student_admission s')
      ->join('student_year sy', 'sy.student_admission_id=s.id')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->join('parent p', 'p.student_id=s.id')
      ->join('student_relation sr', 'sr.relation_id=p.id')
      ->join('avatar a', 'a.stakeholder_id=p.id')
      ->join('users u', 'u.id=a.user_id');
      if($send_to != 'Both') {
        $this->db_readonly->where('sr.relation_type',$send_to);
      } else {
        if($this->settings->getSetting('include_guardian_in_student_communications') != 1){
          $this->db_readonly->where_in('sr.relation_type', array('Father', 'Mother'));
        }
      }
      $this->db_readonly->where('admission_status','2'); // Approved 2
      $this->db_readonly->where('sy.promotion_status!=', '4'); 
      $this->db_readonly->where('sy.promotion_status!=', '5'); 
      $this->db_readonly->where('a.avatar_type','2');
      $this->db_readonly->where('sy.acad_year_id', $acadYearId);
      $this->db_readonly->where_in('s.id', $members);
      $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
      $students = $this->db_readonly->get()->result();
      return $students;
  }

    public function getStudentsBySections($members, $send_to, $acad_year, $batch='all') {
      if($send_to == 'preferred') {
        $this->db_readonly->select("s.id, s.id as std_id, 1 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS Name, s.preferred_contact_no as mobile_no, 'NULL' as user_id, 'NULL' as token, 0 as tokenState, s.email");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->join('class_section cs', 'cs.id=sy.class_section_id');
        $this->db_readonly->where('admission_status','2');
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->where_in('sy.class_section_id',$members);
        if($batch != 0)
          $this->db_readonly->where('s.admission_acad_year_id',$batch);
        $this->db_readonly->where('sy.acad_year_id', $acad_year);
        $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
        $students = $this->db_readonly->get()->result();
        return $students;
      }

      if($send_to == 'preferred_parent') {
        $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email, s.preferred_parent")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->join('parent p', 'p.student_id=s.id')
        ->join('student_relation sr', 'sr.relation_id=p.id and sr.relation_type=s.preferred_parent')
        ->join('avatar a', 'a.stakeholder_id=p.id')
        ->join('users u', 'u.id=a.user_id');
        $this->db_readonly->where('admission_status','2');
        $this->db_readonly->where('sy.promotion_status!=', '4'); 
        $this->db_readonly->where('sy.promotion_status!=', '5'); 
        $this->db_readonly->where('a.avatar_type','2');
        $this->db_readonly->where_in('sy.class_section_id',$members);
        if($this->settings->getSetting('include_guardian_in_student_communications') != 1){
          $this->db_readonly->where_in('sr.relation_type', array('Father', 'Mother'));
        }
        if($batch != 0)
          $this->db_readonly->where('s.admission_acad_year_id',$batch);
        $this->db_readonly->where('sy.acad_year_id', $acad_year);
        $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
        $students = $this->db_readonly->get()->result();

        $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email, s.preferred_parent")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->join('parent p', 'p.student_id=s.id')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->join('avatar a', 'a.stakeholder_id=p.id')
        ->join('users u', 'u.id=a.user_id')
        ->where("(s.preferred_parent='' OR s.preferred_parent='Both')")
        ->where('admission_status','2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where('a.avatar_type','2')
        ->where_in('sy.class_section_id',$members);
        if($this->settings->getSetting('include_guardian_in_student_communications') != 1){
          $this->db_readonly->where_in('sr.relation_type', array('Father', 'Mother'));
        }
        if($batch != 0)
          $this->db_readonly->where('s.admission_acad_year_id',$batch);
        $this->db_readonly->where('sy.acad_year_id', $acad_year);
        $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
        $stds = $this->db_readonly->get()->result();

        $students = array_merge($students, $stds);
        return $students;
        // echo "<pre>"; print_r($students);die();
        
      }

      // $this->db->select("s.id, 1 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email")
      $this->db_readonly->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email")
      ->from('student_admission s')
      ->join('student_year sy', 'sy.student_admission_id=s.id')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->join('parent p', 'p.student_id=s.id')
      ->join('student_relation sr', 'sr.relation_id=p.id')
      ->join('avatar a', 'a.stakeholder_id=p.id')
      ->join('users u', 'u.id=a.user_id');
      if($send_to != 'Both') {
        $this->db_readonly->where('sr.relation_type',$send_to);
      } else {
        if($this->settings->getSetting('include_guardian_in_student_communications') != 1){
          $this->db_readonly->where_in('sr.relation_type', array('Father', 'Mother'));
        }
      }
      $this->db_readonly->where('admission_status','2');
      $this->db_readonly->where('sy.promotion_status!=', '4'); 
      $this->db_readonly->where('sy.promotion_status!=', '5'); 
      $this->db_readonly->where('a.avatar_type','2');
      $this->db_readonly->where_in('sy.class_section_id',$members);
      if($batch != 0)
        $this->db_readonly->where('s.admission_acad_year_id',$batch);
      $this->db_readonly->where('sy.acad_year_id', $acad_year);
      $this->db_readonly->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
      $students = $this->db_readonly->get()->result();
      return $students;
    }

    public function getStaff($members) {
      if(empty($members)) return array();
      $staff =  $this->db_readonly->select("sm.id, 4 as avatar_type, CONCAT(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) AS Name, sm.contact_number as mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, u.email")
      ->from('staff_master sm')
      ->join('avatar a', 'a.stakeholder_id=sm.id')
      ->join('users u', 'u.id=a.user_id')
      ->where_in('sm.id', $members)
      ->where('a.avatar_type', 4)
      ->order_by('sm.first_name', 'ASC')
      ->get()->result();
      return $staff;
    }

    public function getCustomNumbers($members) {
      $custom = array();
      foreach ($members as $key => $member) {
        $custom[] = (Object) array('id' => -1, 'avatar_type' => 0, 'Name' => 'Custom Number', 'mobile_no' => $member, 'user_id' => NULL, 'token' => NULL, 'tokenState' => 0);
      }
      return $custom;
    }

    public function updateNotificationStatus($statusData) {
      return $this->db->update_batch('text_sent_to', $statusData, 'id');
    }

    public function saveUniqueTexting($masterData, $msg_data, $id_messages) {
      $this->db->trans_start();
      $mode = $masterData['mode'];
      $sms_data = array();
      $staff_tokens = array();
      $student_tokens = array();
      $master_ids = array();
      $sms_count = 0;
      $masterData['text_count'] = 1;
      foreach ($id_messages as $ids => $msg) {
        list($id, $avatar_type) = explode("_", $ids);
        $masterData['message'] = $msg;
        $masterData['sms_credits'] = $this->_calculateCredits($msg, $masterData['is_unicode']);

        $this->db->insert('texting_master', $masterData);
        $master_id = $this->db->insert_id();

        array_push($master_ids, $master_id);

        $reciever = $msg_data[$ids];
        $subData = array(
          'texting_master_id' => $master_id,
          'stakeholder_id' => $reciever->id,
          'mobile_no' => $reciever->mobile_no,
          'mode' => 1,
          'status' => 'Sent',
          'avatar_type' => $reciever->avatar_type,
          'is_read' => 0
        );

        $isSmsNumber = 0;
        if($mode == 'sms' || ($mode == 'notification_sms' && $reciever->tokenState == 0)) {
          $subData['mode'] = 2;
          $isSmsNumber = 1;
          $subData['status'] = 'AWAITED-DLR';
          if($reciever->mobile_no == '' || $reciever->mobile_no == NULL) {
            $subData['status'] = 'NO-NUMBER';
            $isSmsNumber = 0;
          }
        } 
        else if($mode == 'notification' && $reciever->tokenState == 0) {
          $subData['status'] = 'No Token';
        } 

        $this->db->insert('text_sent_to', $subData);
        $insId = $this->db->insert_id();
        if($isSmsNumber) {
          $sms_count++;
          $sms_data[] = array(
            'text_sent_to_id' => $insId,
            'message' => $msg,
            'mobile_no' => $reciever->mobile_no
          );
        }
        // $used_sms = 1;
        $used_sms = !($mode == 'notification');
        if(($mode == 'notification' || $mode == 'notification_sms') && $reciever->tokenState) {
          $used_sms = 0;
          if($reciever->avatar_type == 4) {
            $staff_tokens[] = array(
              'user_id' => $reciever->user_id,
              'token' => $reciever->token,
              'body' => $msg,
              'text_sent_to_id' => $insId
            );
          } else {
            $student_tokens[] = array(
              'user_id' => $reciever->user_id,
              'token' => $reciever->token,
              'body' => $msg,
              'text_sent_to_id' => $insId
            );
          }
        }

        // this is causing unnessary sms credit usage counting issue
        // if($used_sms) {
        //   $credits = $masterData['sms_credits'];
        //   $texting_credits = array(
        //     'sms_credits' => $credits,
        //     'action' => 'Unload',
        //     'action_by' => $masterData['sent_by'],
        //     'texting_master_id' => $master_id
        //   );
        //   $this->db->insert('texting_credits_usage', $texting_credits);
        // }

      }

      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return array();
      }
      $this->db->trans_commit();
      return array('sms_data' => $sms_data, 'staff_tokens' => $staff_tokens, 'student_tokens' => $student_tokens, 'master_ids' => $master_ids);
    }

  //08-04-2025 Mohan 
  // $updateUniqueTextResponse modiefied new api key requirments. the following commented code is old api key requirements.

    // public function updateUniqueTextResponse($res, $master_ids) {
    //   $this->db->trans_start();
    //   foreach($res as $key => $value){
    //     $msgIds = explode(":", $value->id);
    //     $msg_id = $msgIds[0];
    //     $response_id = $msgIds[1];

    //     $this->db->query("update texting_master set msg_id='$msg_id' where id in (select distinct(texting_master_id) from text_sent_to where id=$value->customid)");

    //     $data[] = array('response_id' => $response_id, 'id' => $value->customid, 'status' => $value->status);
    //   }
    //   $this->db->update_batch('text_sent_to', $data, 'id');
    //   return $this->db->trans_complete();
    // }

    public function updateUniqueTextResponse($res, $master_ids) {
      $msg_id = $res['msgid'];
      $message = $res['message'];
      $this->db->trans_start();
      $data = [];
      $updatedMasters = [];
      foreach ($res['data'] as $value) {
          if (
              !isset($value['id'], $value['textSentToId'], $value['status']) ||
              empty($value['id']) || empty($value['textSentToId'])
          ) {
              continue;
          }

          $textSentToId = $value['textSentToId'];
          $query = $this->db->select('texting_master_id')
                          ->from('text_sent_to')
                          ->where('id', $textSentToId)
                          ->get();
          if ($query->num_rows() > 0) {
              $texting_master_id = $query->row()->texting_master_id;

              // Update texting_master only once per unique ID
              if (!in_array($texting_master_id, $updatedMasters)) {
                  $this->db->where('id', $texting_master_id)
                            ->update('texting_master', ['msg_id' => $msg_id, 'sending_status' => $message]);
                  $updatedMasters[] = $texting_master_id;
              }
          }
          $data[] = [
              'id' => $textSentToId,
              'response_id' => trim($value['id']),
              'status' => $value['status']
          ];
      }

      if (!empty($data)) {
          $this->db->update_batch('text_sent_to', $data, 'id');
      }

      return $this->db->trans_complete();
    }


    public function getStaffTexts($staffId, $from_date, $to_date) {
      $this->db_readonly->select("distinct(tm.id),date_format(sent_on,'%M %d %Y') as date ,sent_on as smsDate, message as sms_content,stakeholder_id as staff_id,ts.is_read, ts.mode,tm.mode as message_type");
      $this->db_readonly->from('texting_master tm');
      $this->db_readonly->where("date_format(tm.sent_on,'%Y-%m-%d') BETWEEN '$from_date' and '$to_date'");
      $this->db_readonly->join('text_sent_to ts','ts.texting_master_id=tm.id');
      $this->db_readonly->where('ts.stakeholder_id',$staffId);
      $this->db_readonly->where('ts.avatar_type',4);
      $this->db_readonly->order_by('tm.sent_on','desc');
      return $this->db_readonly->get()->result();
    }

    public function getTextsInfo_un_read_count($staffId){
      $acadYearId = $this->acad_year->getAcadYearId();
      $sql = "SELECT count(tst.id) as textCount 
        from text_sent_to tst 
        JOIN texting_master tm on tst.texting_master_id=tm.id
        where tst.stakeholder_id=$staffId 
        and tst.avatar_type=4
        and tst.is_read=0
        and tm.acad_year_id = $acadYearId";
      return $this->db_readonly->query($sql)->row();

    }

    public function get_recent_five_messages($staffId){
      $recentFiveMessages=$this->db_readonly->select("tm.message")
      ->from("text_sent_to tst")
      ->join("texting_master tm","tst.texting_master_id=tm.id")
      ->where("tst.stakeholder_id=$staffId")
      ->where("tst.avatar_type=4")
      ->order_by("tst.id desc")
      ->limit(5)
      ->get()->result();
      return $recentFiveMessages;
    }

    public function update_read_by_staff_id($staffId){
      $sql = "update text_sent_to ts set is_read = 1 where stakeholder_id=$staffId and avatar_type=4 and is_read=0";
      return $this->db->query($sql);
    }

    public function getTextDetail($id){
      $this->db_readonly->select("tm.id,date_format(sent_on,'%d-%m-%Y') as date ,sent_on as smsDate, message as sms_content");
      $this->db_readonly->where('tm.id',$id);
      $result = $this->db_readonly->get('texting_master tm')->row();
      $result->smsDate = local_time($result->smsDate, 'Y-m-d H:i:s');
      return $result;
    }

    public function getAllTexts() {
      return $this->db_readonly->select("id, message")->get('texting_master')->result();
    }

    public function saveGroup($group_name, $group_json) {
      $data = array(
        'group_name' => $group_name,
        'group_json' => $group_json
      );

      return $this->db->insert('texting_groups', $data);
    }

    public function getTextingGroups() {
      $acadYearId = $this->acad_year->getAcadYearId();
      return $this->db_readonly->select('id, group_name')->where('status',1)->where('acad_year_id', $acadYearId)->get('texting_groups')->result();
    }

    public function getGroupMembers($group_id) {
      $acadYearId = $this->acad_year->getAcadYearId();
      $group_json = $this->db_readonly->select('group_json')->where('id', $group_id)->get('texting_groups')->row()->group_json;
      
      $data['students'] = array();
      $data['staff'] = array();
      $data['sections'] = array();
      $data['custom'] = array();

      if($group_json == '')
        return array();
      $group_data = json_decode($group_json);
      if(!empty($group_data->students)) {
        $data['students'] = $this->db_readonly->select("sa.id as student_id, CONCAT(cs.class_name,cs.section_name,': ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, p.first_name as parent_name")
        ->from('student_admission sa')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->join('class_section cs','cs.id=sy.class_section_id')
        ->join('student_relation sr',"sr.std_id=sa.id and relation_type = 'Father'")
        ->join('parent p','p.id=sr.relation_id')
        ->where_in('sa.id', $group_data->students)
        ->where('sy.acad_year_id', $acadYearId)
        ->get()->result();
      }
      if(!empty($group_data->class_section)) {
        $data['sections'] = $this->db_readonly->select("cs.id as section_id, CONCAT(cs.class_name,cs.section_name) as csName")
        ->from('class_section cs')
        ->where_in('cs.id', $group_data->class_section)
        ->get()->result();
      }
      if(!empty($group_data->staff)) {
        $data['staff'] = $this->db_readonly->select("sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName")
        ->from('staff_master sm')
        ->where_in('sm.id', $group_data->staff)
        ->where("status",2)
        ->get()->result();
      }
      if(!empty($group_data->custom)) {
        $data['custom'] = $group_data->custom;
      }
      return $data;
      // echo '<pre>'; print_r($data); die();
    }
    public function makeTextRead($id, $staff_id)
  {
    return $this->db->where('texting_master_id', $id)
    ->where('stakeholder_id', $staff_id)
    ->where('avatar_type', 4)
    ->update('text_sent_to', array('is_read' => 1));
  }

  public function getTextingTemplates() {
    $result = $this->db_readonly->select("tt.id as template_id, template_name, template_content, DATE_FORMAT(tt.created_on, '%d-%m-%Y %h:%i %p') as created_on, CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name,'')) as created_by, is_approved, (case when is_approved=1 then 'Approved' else 'Not Approved' end) as status, DATE_FORMAT(tt.modified_on, '%d-%m-%Y %h:%i %p') as modified_on")
    ->from('texting_templates tt')
    ->join('avatar a', 'a.id=tt.created_by', 'left')
    ->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left')
    ->get()->result();
    $data = array();
    foreach ($result as $key => $res) {
      $data[$res->template_id] = $res;
    }
    return $data;
  }

  public function getApprovedTextingTemplates() {
    $result = $this->db_readonly->select('id, template_name, template_content')->where('is_approved', 1)->get('texting_templates')->result();
    $data = array();
    foreach ($result as $key => $res) {
      $data[$res->id] = $res;
    }
    return $data;
  }

  public function addTextingTemplate() {
    $input = $this->input->post();
    $data = array(
      'template_name' => $input['template_name'],
      'template_content' => $input['template_content'],
    );
    // echo '<pre>'; print_r($data); die();
    if($input['template_id']) {
      return $this->db->where('id', $input['template_id'])->update('texting_templates', $data);
    } else {
      $data['created_by'] = $this->authorization->getAvatarId();
      return $this->db->insert('texting_templates', $data);
    }
  }

  public function template_approval() {
    $template_id = $_POST['template_id'];
    $is_approved = $_POST['is_approved'];
    return $this->db->where('id', $template_id)->update('texting_templates', array('is_approved' => $is_approved));
  }

  public function get_groups_details(){
    $acadYearId = $this->acad_year->getAcadYearId();
    $logged_in_id = $this->authorization->getAvatarStakeHolderId();
    $this->db_readonly->select("tg.*, CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name,'')) as created_by")
    ->from('texting_groups tg')
    // ->join('avatar a', 'a.id=tg.created_by', 'left')
    ->join('staff_master sm', 'sm.id=tg.created_by', 'left')
    ->where('tg.acad_year_id', $acadYearId);
    if(!empty($logged_in_id)){
      $this->db_readonly->where('tg.created_by', $logged_in_id);
    }
    $result = $this->db_readonly->get()->result();
    
    $data = [];
    foreach ($result as $key => $val) {
      $object = new stdClass;
      $decodeJson = json_decode($val->group_json);
      $object->student_count = count($decodeJson->students);
      $object->class_section = count($decodeJson->class_section);
      $object->staff = count($decodeJson->staff);
      $object->group_name = $val->group_name;
      $object->status = $val->status;
      $object->created_by = $val->created_by == ' ' ? 'Admin' : $val->created_by;
      $object->id = $val->id;
      array_push($data, $object);
    }
    return $data;
  }

  public function save_texts($text_master) {
    $this->db->insert('texting_master', $text_master);
    return $this->db->insert_id();
  }

  public function getTextingMaster($master_id) {
    return $this->db_readonly->select("*")
    ->from('texting_master tm')
    ->where('tm.id', $master_id)
    ->get()->row();
  }

  public function updateTextCount($texting_master_id, $count) {
    return $this->db->query("update texting_master set text_count=text_count+$count where id=$texting_master_id");
  }

  public function save_notifications($recievers) {
    $token_data = [];
    foreach ($recievers as $key => $rec) {
      $user_id = $rec['user_id'];
      $token = $rec['token'];
      unset($rec['user_id']);
      unset($rec['token']);
      $this->db->insert('text_sent_to', $rec);
      $insert_id = $this->db->insert_id();
      $token_data[] = array(
        'user_id' => $user_id,
        'token' => $token,
        'text_sent_to_id' => $insert_id
      );
    }
    return $token_data;
  }

  public function save_sms($recievers) {
    $sms_data = [];
    foreach ($recievers as $key => $rec) {
      $this->db->insert('text_sent_to', $rec);
      $insert_id = $this->db->insert_id();
      $sms_data[] = array(
        'text_sent_to_id' => $insert_id,
        'mobile_no' => $rec['mobile_no']
      );
    }
    return $sms_data;
  }

  public function saveTextingCredtits($texting_credits) {
    return $this->db->insert('texting_credits_usage', $texting_credits);
  }

  public function save_text($masterData) {
    $this->db->insert('texting_master', $masterData);
    return $this->db->insert_id();
  }

  public function update_text_status($master_id) {
    return $this->db->where('id', $master_id)->update('texting_master', ['sending_status' => 'Completed', 'sender_list' => NULL]);
  }

  public function getTextingSummary($master_id) {
    $sql = "SELECT SUM(CASE WHEN status='DELIVRD' OR status='Sent' THEN 1 ELSE 0 END) as success, SUM(CASE WHEN status='DELIVRD' OR status='Sent' OR status='AWAITED-DLR' THEN 0 ELSE 1 END) as failed, SUM(CASE WHEN status='AWAITED-DLR' THEN 1 ELSE 0 END) as awaited, CASE WHEN mode=1 THEN 'Notification' ELSE 'sms' END AS mode FROM text_sent_to where texting_master_id=$master_id GROUP BY mode";
    $result = $this->db_readonly->query($sql)->result();
    $data = [
      'Notification' => ['success' => 0, 'failed' => 0, 'sent' => 0, 'awaited' => 0],
      'sms' => ['success' => 0, 'failed' => 0, 'sent' => 0, 'awaited' => 0]
    ];
    foreach ($result as $key => $value) {
      $data[$value->mode]['success'] = $value->success;
      $data[$value->mode]['failed'] = $value->failed;
      $data[$value->mode]['sent'] = $value->success + $value->failed;
      $data[$value->mode]['awaited'] = $value->awaited;
    }
    return $data;
  }

  public function getSMSDeliveryData($acadYearId){
    return $this->db_readonly->select("st.status, if(st.mode=2, 'SMS', 'Notification') as mode, count(st.id) as status_count")
    ->from("texting_master tm")
    ->join("text_sent_to st", "tm.id=st.texting_master_id")
    ->where("tm.acad_year_id", $acadYearId)
    ->group_by("st.status, st.mode")
    ->order_by('st.status')
    ->get()->result();
  }

  public function save_forgt_user_otop($input){

    $this->db->where('user_name', $input['user_name']);
    $query = $this->db->get('user_forgot_recovery');
    $this->db->reset_query();
    if ($query->num_rows() > 0 ) 
    {
        $forgotData = array (
            'otp' => $input['otp']
        );
      return $this->db->where('user_name', $input['user_name'])->update('user_forgot_recovery', $forgotData);
    } else {
        $forgotData = array(
          'recovery_type' => $input['recovery_type'],
          'user_name' => $input['user_name'],
          'otp' => $input['otp']
        );

      return $this->db->insert('user_forgot_recovery', $forgotData);
    }
  }
  public function get_acad_year_id_from_text(){

    $sql = "SELECT DISTINCT acad_year_id FROM texting_master where acad_year_id !=0 and acad_year_id is not null ORDER BY acad_year_id desc";

    $result = $this->db_readonly->query($sql)->result();
          
    return $result;
  }

  public function get_texting_templates() {
    $result = $this->db_readonly->query("SELECT t.id, t.name, tmp.content from sms_template_new t join sms_templates_acad_year tmp on tmp.sms_templates_id=t.id where t.category='Texting' order by tmp.id desc")->result();

    $cat = [];
    foreach ($result as $key => $res) {
      if(!in_array($res->id, $cat)) {
        $cat[] = $res->id;
        continue;
      }
      unset($result[$key]);
    }

    return $result;
  }

  public function getStaffsByStaffType($data){
		if(!empty(strlen($data["staffType"])) && $data["staffType"]!=""){
			return $this->db_readonly->select("id, concat(ifnull(first_name,''),' ', ifnull(last_name,'')) as staff_name, ifnull(designation, '') as designation, ifnull(department, '') as department, ifnull(staff_type, '') as staff_type")
			->from("staff_master")
			->where("status",2)
			->where_in("staff_type",$data["staffType"])
      ->order_by('staff_name')
      ->get()->result();
		}else{
			return $this->db_readonly->select("id, concat(ifnull(first_name,''),' ', ifnull(last_name,'')) as staff_name, ifnull(designation, '') as designation, ifnull(department, '') as department, ifnull(staff_type, '') as staff_type")
				->from("staff_master")
				->where("status", 2)
        ->order_by('staff_name')
        ->get()->result();
		}
	}

  public function filter_out_resigned_staffs($staff_list = []){
    $activeStaffsList = [];

    if (empty($staff_list)) {
      return $activeStaffsList;
    }

    $get_staffs = $this->db_readonly->select("id")
      ->from("staff_master")
      ->where_in("id", $staff_list)
      ->where("status", "2")
      ->get()->result();

    if(empty($get_staffs)){
      return $activeStaffsList;
    }

    foreach($get_staffs as $key => $val){
      $activeStaffsList[]=$val->id;
    }
      
    return $activeStaffsList;
  }
}