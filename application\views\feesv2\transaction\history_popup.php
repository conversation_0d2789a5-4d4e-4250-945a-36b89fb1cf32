<div class="modal fade" id="student_history" role="dialog" style="padding-right: 4px;">
  <div class="modal-dialog">
    <div class="modal-content" style="width:80%;margin: auto;border-radius: .75rem">
        <div class="card-header panel_heading_new_style_staff_border">
          <div class="row" style="margin: 0px;">
            <div class="d-flex justify-content-between" style="width:100%;">
              <h3 class="card-title panel_title_new_style_staff">
                Fees History for <?php echo '<strong>' . $student->stdName . '</strong> (Class: ' . $student->className . ') (Admission No: '. $student->admission_no.')'?>
              </h3>
              <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div> 
          </div>
        </div>
        <div class="modal-body" >
          <div class="col-md-12">
            <!-- <input type="checkbox" id="allTransactions" name="allTransactions" onclick="getData()" style="width: 25px; height: 15px;">
            <span class="card-title panel_title_new_style_staff"> All Transactions</span> -->
          </div>
        </div>
      <div class="modal-body" id="append_content" style="overflow:scroll; ">
        
      </div>               
    </div>
  </div>
</div>


<script type="text/javascript">
   $(function(){
        var heightScreen = $(window).height();
        $('#append_content').css({'height':heightScreen - 150});
    });

  var std_id = '<?php echo $std_id?>'
  // $(document).ready(function () {
  //   getData();
  // })
  let deleteConfig = '<?php echo $delete?>';
  function getData() {
    $("#append_content").html('<div class="text-center"><i style="font-size:40px;" class="fa fa-spinner fa-spin"></i></div>');
    var flag = 0
    if($("#allTransactions").prop('checked') == true){
      var flag = 1
    }
    $.ajax({
        url:'<?php echo site_url('feesv2/fees_collection/history_ajax') ?>',
        type:'post',
        data: {'std_id':std_id},
        success : function(data){
            var data = JSON.parse(data);
            var fee_history = data.fee_history;
            console.log(fee_history);
            // var student = data.student;
            // var std_id = data.std_id;
            var delete_authorization = data.delete_authorization;
            var refund = data.refund;
            var fee_adjustment_amount = data.fee_adjustment_amount;
            var fee_fine_amount = data.fee_fine_amount;
            var fee_refund_amount = data.fee_refund_amount;
            var html_content = '';
            var concession=0;
            var adjustment=0;
            var total_feeAmount=0;
            var balance=0;
            for (var i = 0; i < fee_history.length; i++) {
              html_content +=`<div class="col-md-12">`;
              if (fee_history[i].std_fee==null && fee_history[i].std_fee==undefined) {
              }
              else{

                concession = parseFloat(fee_history[i].concession_adjustment.total_concession_amount) + parseFloat(fee_history[i].concession_adjustment.total_concession_amount_paid);

                adjustment = parseFloat(fee_history[i].concession_adjustment.total_adjustment_amount) + parseFloat(fee_history[i].concession_adjustment.total_adjustment_amount_paid);

                total_feeAmount = (fee_history[i].std_fee.total_fee_paid == null ? 0 : parseFloat(fee_history[i].std_fee.total_fee_paid));

                balance = (fee_history[i].std_fee.total_fee == null ? 0 : parseFloat(fee_history[i].std_fee.total_fee))  - total_feeAmount - concession;
                if (fee_history[i].std_fee.stdSchId!=null && fee_history[i].std_fee!=null && fee_history[i].std_fee!=undefined){
                  html_content +='<div class="card cd_border mb-2">';
                  html_content +=`<div class="card-header panel_heading_new_style_staff_border">
                                  <div class="row" style="margin: 0px;">
                                  <div class="d-flex justify-content-between" style="width:100%;">`;
                  html_content +='<h3 class="card-title panel_title_new_style_staff" ><b>'+fee_history[i].name+'</b></h3>';
                  html_content +='<div>';
                }
                // Download consolidate fee receipt download if fee balance is 0;
                if (fee_history[i].consolidated_receipt_html != null && fee_history[i].consolidated_receipt_html != '' &&  balance == 0) {
                  if (fee_history[i].std_fee.pdf_status == 1) {
                    var fullReceiptUrl = '<?php echo base_url() ?>feesv2/fees_collection/consolidated_receipt_pdf_download/'+fee_history[i].std_fee.stdSchId;
                    html_content +='<a class="new_circleShape_res mr-2" style="margin-left: 8px; background-color: #fe970a;" data-placement="top" data-toggle="tooltip" title="Download PDF" data-original-title="Download PDF" href="'+fullReceiptUrl+'"><i class="fa fa-cloud-download" style="font-size: 19px;"></i></a>';
                  }else{
                    html_content += '<ul class="panel-controls"> <a class="new_circleShape_res mr-2" id="pdf_generate" style="background-color: #fe970a;" onclick="generate_pdf_consolidate_feeReceipt('+fee_history[i].std_fee.stdSchId+')" data-toggle="tooltip" data-placement="top" title="Generate Fee Receipt" data-original-title="Generate Fee Receipt " href="javascript:void(0)"><i class="fa fa-file-text-o" style="font-size: 19px;"></i></a></ul>';
                  }
                }
                html_content +='</div></div></div></div>';
                html_content +='<div class="card-body">';
                
                if (fee_history[i].history && (fee_history[i].history!=null || fee_history[i].history!=undefined)) {
                    html_content +='<table class="table table-bordered" id="mytable">';
                    html_content +=`<thead>
                                      <tr>
                                        <th>Paid date</th>
                                        <th>Receipt number</th>
                                        <th>Amount Paid</th>
                                        <th class="authorizeDispaly">Concession Applied</th>`;
                                      if(fee_history[i].std_fee.discount != null && fee_history[i].std_fee.discount != 0){
                                            html_content +='<th class="authorizeDispaly">Discount</th>';   
                                      }
                                      if (fee_fine_amount) {
                                        html_content +=`<th class="authorizeDispaly">Fine</th>`;        
                                      }
                                      if(fee_adjustment_amount) {
                                        html_content +=`<th class="authorizeDispaly">Adjustment Applied</th>`;         
                                      }  
                                      if(fee_refund_amount) {
                                        html_content +=`<th class="authorizeDispaly">Refund Amount</th>`;         
                                      } 
                                        html_content +=`<th>Payment Type</th><th>Status</th><th style="width: 10vw">Remarks</th>
                                        <th style="width: 15vw" >Action</th>
                                      </tr>
                                    </thead>`;
                    html_content +='<tbody>';
                    var history = fee_history[i].history;
                    console.log(history);
                    for (var h = 0; h < history.length; h++) {
                      visibility = 'style="display:none"';
                      if (flag == 0 && history[h].status == 'SUCCESS') {
                        visibility="style='display:'''";
                      }
                      if (flag == 1) {
                        visibility="style='display:'''";
                      }
                      
                      html_content +='<tr '+visibility+' >';
                      html_content +='<td>'+history[h].paid_date+'</td>';
                      html_content +='<td>'+history[h].receipt_number+'</td>';
                      html_content +='<td>'+numberToCurrency(history[h].total_amount - history[h].discount_amount)+'</td>';
                      html_content +='<td class="authorizeDispaly" > ( '+numberToCurrency(history[h].concession_amount)+' )</td>';

                      if(fee_history[i].std_fee.discount != null && fee_history[i].std_fee.discount != 0){
                        html_content +='<td class="authorizeDispaly">'+numberToCurrency(history[h].discount_amount)+'</td>';
                      }
                      if (fee_fine_amount) {
                        html_content +='<td class="authorizeDispaly">'+numberToCurrency(history[h].fine_amount)+'</td>';
                      }
                      if(fee_adjustment_amount) {
                        html_content +='<td class="authorizeDispaly"> ( '+numberToCurrency(history[h].adjustment_amount)+' )</td>';
                      }  
                      if(fee_refund_amount) {
                        html_content +='<td class="authorizeDispaly"> ( '+numberToCurrency(history[h].refund_amount)+' )</td>';
                      }
                      let paymentDetails = '';
                      if(history[h].cheque_dd_nb_cc_dd_number!=''){
                        var cheque_or_dd_date = '<br>Bank Date : '+history[h].cheque_or_dd_date+'';
                        if(history[h].payment_type == '2' || history[h].payment_type == '3'){
                          cheque_or_dd_date = '';
                        }
                        paymentDetails = 'Bank name : '+history[h].bank_name+''+cheque_or_dd_date+'<br>Ref : '+history[h].cheque_dd_nb_cc_dd_number+'';
                      }
                      html_content +='<td>'+history[h].paymentValue+'<br>'+paymentDetails+'</td>';
                      html_content +='<td>'+history[h].status+'</td>';
                      html_content +='<td>'+history[h].remarks+'</td>';
                      html_content +='<td>';

                      var url = '<?php echo base_url() ?>feesv2/fees_collection/fee_reciept_viewv1/'+history[h].id;

                      html_content +='<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #6893ca;color:white;" disabled target="_blank" data-placement="top" data-toggle="tooltip" data-original-title="Print Receipt" href="'+url+'"><i class="fa fa-print" ></i></a>';

                      html_content +='<span data-toggle="modal" data-target="#receipt_view_model" data-original-title="View"><a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px ;background-color: #89ad4d; color:white;" disabled data-toggle="tooltip" data-placement="top" data-original-title="View" title="View"  data-placement="top" href="javascript:void(0)" onclick="fee_receipt_view('+history[h].id +')"><i class="fa fa-eye"></i></a></span>';

                      if (history[h].pdf_status == '1' && history[h].reconciliation_status != '3') {
                        var url = '<?php echo base_url() ?>feesv2/fees_collection/receipt_pdf_download/'+history[h].id;
                        html_content +='<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #31b0d5; color:white;" data-placement="top" data-toggle="tooltip" data-original-title="Download PDF " href="'+url+'"><i class="fa fa-cloud-download"></i></a>';
                      }
                      var disableOnline = '';
                      if (deleteConfig != 1 && history[h].paymentValue == 'Online Payment') {
                        disableOnline = 'pointer-events: none; opacity: 0.6;';
                      }
                      if (delete_authorization == 1 && history[h].reconciliation_status != '3' && history[h].reconciliation_status != '1') {
                        html_content +='<a onclick="fee_soft_delete('+history[h].id+',\''+history[h].receipt_number.trim()+'\')" class="circleButton_noBackColor_actionBtn" style="margin-left: 8px ;background-color: #e04b4a; color:white;'+disableOnline+'"  data-placement="top" data-toggle="tooltip" data-original-title="Delete"><i class="fa fa-trash-o"></i></a>';
                      }
                      if(history[h].reconciliation_status == '3'){
                        html_content +='<b style="float: right;" ><span style="color: red">Reconciliation Failed </span></b>';
                      }
                      else if(history[h].reconciliation_status == '1'){
                        html_content +='<b style="float: right;" ><span style="color: red">Reconciliation Pending </span></b>';
                      }

                      if (refund == 1 && history[h].reconciliation_status != '3' && history[h].reconciliation_status != '1' && history[h].paymentValue != 'Online Challan Payment'){
                        html_content += '<span data-toggle="modal" data-target="#refund_fees" data-original-title="Refund"> <a onclick="fee_refund('+std_id+','+history[h].id+','+fee_history[i].std_fee.blueprint_id+',\''+history[h].paymentValue+'\')" class="circleButton_noBackColor_actionBtn" style="background-color: #6c757d;"  data-toggle="tooltip" data-placement="top" title="Refund" data-original-title="Refund" data-id='+history[h].id+'  ><i style="color:#fff;font-size: 19px;" class="glyphicon glyphicon-registration-mark"></i></a></span>';
                      }

                      html_content +='</td>';
                    } 
                }
                   
                if (fee_history[i].std_fee.stdSchId!=null && fee_history[i].std_fee!=null && fee_history[i].std_fee!=undefined){
                  html_content +='<div class="panel panel-default new-panel-style_3 mb-0">';
                  html_content +='<table class="table table-bordered authorizeDispaly"><thead>'; 
                  html_content +='<tr>';
                  html_content +=`<th rowspan="2" style="vertical-align: middle; text-align: center;">Fee Summary</th>
                                  <th>Total Fee Amount</th>
                                  <th>Total Fee Amount paid</th>
                                  <th>Total Concession</th>`; 

                  if(fee_adjustment_amount){
                      html_content +='<th>Total Adjustment</th>';   
                  }
                  if(fee_history[i].std_fee.discount != null && fee_history[i].std_fee.discount != 0){
                      html_content +='<th>Discount</th>';   
                  }
                  if(fee_fine_amount){
                    html_content +='<th>Total Fine</th>';
                  }
                  if(fee_history[i].std_fee.total_card_charge_amount != null && fee_history[i].std_fee.total_card_charge_amount != 0){
                      html_content +='<th>Card Charge Amount</th>';
                  }
                  if(fee_history[i].std_fee.loan_provider_charges != null && fee_history[i].std_fee.loan_provider_charges != 0){
                      html_content +='<th>Loan Provider Charges</th>';
                  }
                  html_content +='<th>Balance Amount</th>';
                  if(refund){
                      html_content +='<th>Refund Amount</th>';
                  }
                  html_content +='</tr>';
                  html_content +='<tr>';
                  html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.total_fee)+'</td>';
                  html_content +='<td>'+numberToCurrency((fee_history[i].std_fee.total_fee_paid == null ? 0 : fee_history[i].std_fee.total_fee_paid - fee_history[i].std_fee.discount))+'</td>';
                  html_content +='<td> ( '+numberToCurrency(concession)+' )</td>';
                  if(fee_adjustment_amount){
                    html_content +='<td> ( '+numberToCurrency(adjustment)+' ) </td>';
                  }
                  if(fee_history[i].std_fee.discount != null && fee_history[i].std_fee.discount != 0){
                      html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.discount)+'</td>';
                  }
                  if(fee_fine_amount){
                      html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.total_fine_amount)+'</td>';
                  }
                  if(fee_history[i].std_fee.total_card_charge_amount != null && fee_history[i].std_fee.total_card_charge_amount != 0){
                    html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.total_card_charge_amount)+'</td>';
                  }
                  if(fee_history[i].std_fee.loan_provider_charges != null && fee_history[i].std_fee.loan_provider_charges != 0){
                    html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.loan_provider_charges)+'</td>';
                  }
                  html_content +='<td>'+numberToCurrency(balance)+'</td>';
                  if(refund){
                    if(fee_history[i].std_fee.refund_amount!= null){
                      html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.refund_amount)+'</td>';
                    }
                    else{
                      html_content +='<td></td>';
                    }

                  }
                  html_content +='</tr>';                
                  html_content +='</thead></table>'; 
                  html_content +='</div></div>'; 
                }
                html_content +='</div>';
              }
               
              html_content +='</div></div>';
            }
            $("#append_content").html(html_content);
        }
    });
  }
</script>
<script type="text/javascript">

function fee_soft_delete(feeId, receipt_number) {
  var student_id = '<?php echo $std_id ?>';
  bootbox.prompt({
      inputType:'text',
      placeholder: 'Enter Remarks',
      className:'widthadjust',
      buttons: {
          confirm: {
              label: 'Yes',
              className: 'btn-success'
          },
          cancel: {
              label: 'No',
              className: 'btn-danger'
          }
      },
      title: "Deleting Fee Receipt: " + receipt_number +". Are you Sure?", 
      callback: function (remarks) {
        if (remarks=='') {
          return false;
        }
        if(remarks) {        
          $.ajax({
            url: '<?php echo site_url('feesv2/fees_collection/fee_soft_delete'); ?>',
            type: 'post',
            data: {'feeId' : feeId,'remarks':remarks,'student_id':student_id,'receipt_number':receipt_number},
            success:function(data){
              if(data){
                 location.reload();
              new PNotify({
                  title: 'Success',
                  text: 'Successfully deleted',
                  type: 'success',
                });
              } else {
               new PNotify({
                  title: 'Error',
                  text: 'Something went wrong',
                  type: 'Error',
                });
              }
            }
          });
        }
      }
  });
}

function generate_pdf_consolidate_feeReceipt(stdSchId) {
  $('#pdf_generate').html('<i style="font-size:25px;" class="fa fa-spinner fa-spin"></i>').attr('disabled','disabled');
    $.ajax({
      url: '<?php echo site_url('feesv2/fees_collection/get_full_receipt_pdf') ?>',
      type: 'post',
      data: {'stdSchId' : stdSchId},
      success:function(data){
        var res =  checkConsolidted_pdf_status(stdSchId);
        if (res == 1) {
          $('#pdf_generate').removeAttr('disabled');
          location.reload();
        }else{
          setTimeout(function() {
            var res1 = checkConsolidted_pdf_status(stdSchId);
            if (res1 ==1) {
              $('#pdf_generate').removeAttr('disabled');
              location.reload();
            }
          }, 10000);
        }
        setTimeout(function() {
          location.reload();
        }, 10000);

      }
    });
}


function checkConsolidted_pdf_status(stdSchId) {
  $.ajax({
    url: '<?php echo site_url('feesv2/fees_collection/check_consolidate_pdf_status'); ?>',
    type: 'post',
    data: {'stdSchId' : stdSchId},
    success :function(res) {
      return res;
    }
  });
}

function fee_refund(stdId, transId, bpId, paymentType) {
  $('#paymentModes option[value="888_0"]').prop('disabled', paymentType != 'Online Payment')
  $('#stdId').val(stdId);
  $('#trans_id').val(transId);
  $('#refund_blueprint_id').val(bpId);
  $('#submitbutton').attr('submitbutton',transId);
  $.ajax({
    url: '<?php echo site_url('feesv2/refund_controller/get_refund_databy_txId'); ?>',
    type: 'post',
    data: {'stdId':stdId,'transId':transId},
    success :function(res) {
      var feeData = JSON.parse(res);
      console.log(feeData);
      $('#ajax_construct').html(construct_table(feeData.insData, transId));
      $('#construct_trans_history').html(construct_retund_history(feeData.refundTrns));
    }
  });
}

function construct_retund_history(refund_data) {
  var redata = '';
  redata +='<table class="table table-bordered">';
  redata +='<h3>Refund History</h3>';
  if (refund_data.length == '') {
    redata +='<h6>No refund history found</h6>';
  }
  
  if (refund_data!='') {
      redata +='<tr>';
      redata +='<th>Refund Date</th>';
      redata +='<th>Amount</th>';
      redata +='<th>Payment Type</th>';
      redata +='<th>Remarks</th>';
      redata +='<th>Refunded By</th>';
      redata +='<th>Status</th>';
      redata +='</tr>';
    for(var key in refund_data){
      redata +='<tr>';
      redata +='<th>'+refund_data[key].refund_date+'</th>';
      redata +='<th>'+numberToCurrency(refund_data[key].amount)+'</th>';
      redata +='<th>'+payment_mode(refund_data[key].payment_type)+'</th>';
      redata +='<th>'+refund_data[key].remarks+'</th>';
      redata +='<th>'+refund_data[key].created_by+'</th>';
      var display ='style="display:none"';
      if(refund_data[key].payment_type == '888'){
        display ='style="display:block"';
      }
      redata +='<th>'+refund_data[key].refund_status+'<a '+display+' href="javascript:void(0)" onclick="check_status_of_refund('+refund_data[key].refund_id+','+refund_data[key].ftId+','+refund_data[key].student_id+','+refund_data[key].blueprint_id+')"> Refresh</a></th>';
      redata +='</tr>';
    }
  }

  redata +='</table>';
  return  redata; 
}

function payment_mode(payment_type) {
  var value='';
  switch (payment_type){
    case "9":
    value ='Cash';
    break;
    case "4":
    value ='Cheque';
    break;
    case "1": 
    value ='DD';
    break;
    case "8": 
    value ='Net Banking';
    break;
    case "888": 
    value ='Online Payment';
    break;
    default: 
    value='';
  }
  return value;
}
function construct_table(feeData, transId){
  // console.log(feeData);
  var feedata = '';
  feedata +='<table class="table table-bordered">';
  feedata +='<h3>Fee Collection Details</h3>';
  var final_amount = 0;
  var final_concession = 0;
  var j=1;
  var found = 0;
  for(var ins_name in feeData ){
    for(var key in feeData[ins_name]){
      if (feeData[ins_name][key].reconciliation_status == '1') {
        found = 1;
        break;
      }
    }
  }
  if (found) {
    feedata +='<h3>Reconciliation pending</h3>';
    $('.disabled').css('display','none');
    $('#submitbutton').prop('disabled',true).attr('submitbutton',transId);
  }else{
    var k = 1;
    for(var ins_name in feeData ){
      feedata +='<tr>';
      feedata +='<th colspan="6">'+ins_name+'</th>';
      feedata +='</tr>';
      feedata +='<tr>';
      feedata +='<th>Component</th>';
      feedata +='<th>Paid Date</th>';
      feedata +='<th>Amount Paid</th>';
      feedata +='<th>Concession</th>';
      feedata +='<th>Refunded</th>';
      feedata +='<th>Refund Amount</th>';
      feedata +='</tr>';
      var comp_total_paid = 0;
      var total_con = 0;
    
      var total_refund = 0;
      // console.log(feeData[ins_name]);
      
      for(var key in feeData[ins_name]){

        comp_total_paid += parseInt(feeData[ins_name][key].amount_paid);
        total_con += parseInt(feeData[ins_name][key].concession_amount);
        total_refund += parseInt(feeData[ins_name][key].refund_amount);
        feedata +='<tr>';
        feedata +='<th>'+feeData[ins_name][key].comp_name+'</th>';
        feedata +='<th>'+feeData[ins_name][key].paid_date+'</th>';
        feedata +='<th>'+numberToCurrency(feeData[ins_name][key].amount_paid)+'</th>';
        feedata +='<th>'+numberToCurrency(feeData[ins_name][key].concession_amount)+'</th>';
        feedata +='<th>'+numberToCurrency(feeData[ins_name][key].refund_amount)+'</th>';

        var readonly = '';
        if (feeData[ins_name][key].amount_paid == feeData[ins_name][key].refund_amount) {
          readonly ='readonly';
        }

        feedata +='<input type="hidden" class="form-control" id="PreviousRefundAmount_'+k+'" name="refund_amount_previous['+feeData[ins_name][key].fee_student_installments_id+']['+feeData[ins_name][key].fticId+']" value="'+feeData[ins_name][key].refund_amount+'">';

        feedata +='<th><input type="text" '+readonly+' class="form-control" onkeyup="refund_validate('+k+','+feeData[ins_name][key].amount_paid+')" id="refundAmount_enter_'+k+'" name="comp_amount['+feeData[ins_name][key].fee_student_installments_id+']['+feeData[ins_name][key].fticId+']" value=""></th>';
        feedata +='</tr>';
      k++; 
     }
      $('#submitbutton').prop('disabled',false).attr('submitbutton',transId);
      if (comp_total_paid <= total_refund) {
        $('#submitbutton').prop('disabled',true).attr('submitbutton',transId);
      }
    }
  }
  
  feedata +='</table>';
  return  feedata;    
}

function refund_validate(k, paidAmount) {
  var rfAmount = $('#refundAmount_enter_'+k).val();
  var rPreviousfAmount = $('#PreviousRefundAmount_'+k).val();
  var previouPaid = paidAmount - rPreviousfAmount;  
  if (previouPaid < rfAmount) {
    $('#refundAmount_enter_'+k).val(0);
  }
 
}
</script>

<div id="refund_fees" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 65%;margin: auto;margin-top: 5%;border-radius: .75rem;">

      <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
        <h4 class="modal-title" id="exampleModalLabel">Refund</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
        </button>
      </div>
    
      <div class="modal-body">
        <div class="col-md-12">
        <form enctype="multipart/form-data" method="post" class="form-horizontal" id="refund_fees_update" action="<?php echo site_url('feesv2/refund_controller/update_refund_fees');?>" data-parsley-validate="">
          <input type="hidden" name="std_id" id="stdId">
          <input type="hidden" name="trans_id" id="trans_id">
          <input type="hidden" name="blueprint_id" id="refund_blueprint_id">
          <input type="hidden" name="notification_flag" id="notification_flag">
          <div id="construct_trans_history" style="max-height: 350px; overflow-y: scroll;">

          </div>
          <hr>
          <div id="ajax_construct" style="max-height: 350px; overflow-y: scroll;">
          </div>
          <div class="form-group disabled">
            <ul class="panel-controls" style="float: left;">
              <label class="control-label col-md-5">Mode of Payment <font color="red"> *</font></label>
              <div class="col-md-7">
               <select class="form-control" required="" style="width: 180px" id="paymentModes" name="payment_type" >
                  <option value="">Select Payment Method</option>
                  <option value="9_0">Cash</option>
                  <option value="4_0">Cheque</option>
                  <option value="1_0">DD</option>
                  <option value="8_0">Net Banking</option>
                  <?php 
                    if ($this->authorization->isAuthorized('FEESV2.EXCESS_AMOUNT')) { ?>
                      <option value="999_0">Add Excess Amount</option>
                    <?php }
                  ?>
                  <?php 
                    if ($this->authorization->isAuthorized('FEESV2.ONLINE_PAYMENT_REFUND')) { ?>
                      <option value="888_0">Online payment refund</option>
                    <?php }
                  ?>
                </select>
              </div>
            </ul>
            <ul class="panel-controls" style="float: right; ">
              <label class="control-label col-md-5">Refund Date</label>
              <div class="col-md-7">
                <input type="text" name="refund_date" value="<?php echo date('d-m-Y') ?>" id="refund_date" class="form-control">
              </div>
            </ul>
          </div>
          <div class="panel-body disabled">
            <div class="col-md-8 col-offset-4">

              <div class="form-group refund_hideshow" id="bankname" style="display: none" >
                 <label class="control-label col-sm-5" for="bank">Bank Name</label>
                 <div class="col-md-7 control-label">
                  <select class="form-control" name="bank_name" id="bank">
                    <option value="">Select Bank</option>
                    <?php $bank_names = $this->config->item('bank_names');
                      sort($bank_names); 
                    foreach ( $bank_names as $bNames) { ?>
                      <option value="<?php echo $bNames ?>"><?php echo $bNames ?></option>
                    <?php } ?>
                      <option value="other">Others</option>
                  </select>
                 </div>
              </div>
              <div class="form-group" id="bank_show_others" style="display: none" >
                 <label class="control-label col-sm-5" for="bank_name">Other Name</label>
                 <div class="col-md-7 control-label">
                   <input class="form-control" id="other_bank" disabled="true" name="bank_name">
                 </div>
              </div>
              <div class="form-group refund_hideshow" style="display: none" >
                 <label class="control-label col-sm-5" for="branch">Branch Name</label>
                 <div class="col-md-7 control-label">
                   <input class="form-control" id="branch" name="branch_name" >
                 </div>
              </div>
               <div class="form-group refund_hideshow1" style="display: none" >
                 <label class="control-label col-sm-5" for="chq_no">Cheque Number<font color='RED'>*</font></label>
                 <div class="col-md-7 control-label">
                   <input data-parsley-type="number" class="form-control" id="chq_no" name="cheque_dd_nb_cc_dd_number" >
                 </div>
              </div>
              
              <div class="form-group refund_hideshow2" style="display: none" >
                <label class="control-label col-sm-5" for="dd_no">DD Number</label>
                 <div class="col-md-7 control-label">
                    <input class="form-control" id="dd_no" name="dd_number" >
                 </div>
              </div>
              <div class="form-group refund_hideshow date" style="display: none" >
                 <label class="control-label col-sm-5" for="datetimepicker1"> Date</label>
                 <div class="col-md-7 control-label">
                    <div class="input-group date" id="datetimepicker1"> 
                      <input type="text"  class="form-control" id="currentdate" name="bank_date" value="<?php echo date('d-m-Y'); ?>">
                      <span class="input-group-addon">
                      <span class="glyphicon glyphicon-calendar"></span>
                      </span>
                    </div> 
                 </div>
              </div>
              <div class="form-group refund_card" style="display: none" >
                <label class="control-label col-sm-5" for="card_reference_no">Card Reference Number</label>
                 <div class="col-md-7 control-label">
                    <input class="form-control" id="cd_no" data-parsley-error-message="This value is required." name="cc_number" >
                 </div>
              </div>
             
              <div class="form-group netbanking" style="display: none" >
                <label class="control-label col-sm-5" for="nb_rn">Net Banking Reference Number</label>
                 <div class="col-md-7 control-label">
                    <input class="form-control" id="nb_rn" name="nb_number" >
                 </div>
              </div>

         
              <div class="form-group">
                <label class="control-label col-sm-5" for="class">Remarks <font color="red"> *</font></label>
                <div class="col-sm-7"> 
                  <input class="form-control" required id="class" name="remarks" >
                </div>
              </div>
            </div>
          </div>
          <center>
            <button type="button" id="submitbutton" onclick="updated_refund()"  style="width: 9rem; border-radius: .45rem;" class="btn btn-primary" disabled>Submit</button>     
            <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
          </center>

        </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('#refund_fees').on('shown.bs.modal', function() {
  $('#refund_date').datetimepicker({
    format: 'DD-MM-YYYY'
  });
  $('#currentdate').datetimepicker({
    format: 'DD-MM-YYYY'
  });
});

 
  function updated_refund(){
    var flag  = '<?php echo $this->settings->getSetting('refund_trigger_notification_parent')?>';
    if(flag == 1){
      bootbox.confirm({
        title: "Send Refund Notification",
        message: "<h5><center><b>If you click on yes, notification will be sent to the parent</b>.<br> Are you sure you want to proceed?</center></h5>",
        className: "bootbox-small dialogWide",
        buttons: {
          confirm: {
            label: 'Yes',
            className: 'btn-success'
          },
          cancel: {
            label: 'No',
            className: 'btn-danger'
          }
        },
        callback: function (result) {
          if(result) {
              $('#notification_flag').val('yes');           
          }else{
              $('#notification_flag').val('no');
          }
          $('#refund_fees_update').submit();
        }
      })
    }else{
      $('#notification_flag').val('no');
      $('#refund_fees_update').submit();
    }
  }

  $(document).ready(function(){
    $('#paymentModes').val();
      payment_modes();
  });

  $('#paymentModes').change(function(){
    payment_modes();
  });

  function payment_modes() {
    var modes = ($('#paymentModes').val()).split('_',1);
    if(modes =='1'){
      $('.refund_hideshow1').hide();
      $('.refund_hideshow').show();
      $('.refund_hideshow').show();
      $('.refund_hideshow').show();
      $('.refund_hideshow2').show();
      $('.netbanking').hide();
      $("#cardCharge").prop('disabled',true);
      $("#cd_no").attr('required',false);
      $("#nb_rn").attr('required',false);
      $("#currentdate").attr('required',false);
      $("#chq_no").attr('required',false);
      $("#dd_no").attr('required',true);
    }else if (modes=='4'){
      $('.refund_hideshow2').hide();
      $('.refund_hideshow').show();
      $('.refund_hideshow').show();
      $('.refund_hideshow').show();
      $('.refund_hideshow1').show();
      $('.netbanking').hide();
      $('.refund_card').hide();
      $('.date').show();
      $('.cardCharge').hide();
      $("#cardCharge").prop('disabled',true);
      $("#cd_no").attr('required',false);
      $("#nb_rn").attr('required',false);
      $("#currentdate").attr('required',true);
      $("#chq_no").attr('required',true);
      $("#dd_no").attr('required',false);
    }else if(modes=='7'){
      $('.refund_hideshow2').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow1').hide();
      $('.refund_card').show();
      $('.cardCharge').show();
      $('.netbanking').hide();
      $('.date').hide();
      $("#cd_no").attr('required',true);
      $("#nb_rn").attr('required',false);
      $("#currentdate").attr('required',false);
      $("#cardCharge").prop('disabled',false);
      $("#chq_no").attr('required',false);
      $("#dd_no").attr('required',false);
    }else if(modes=='8'){
      $('.refund_hideshow2').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow1').hide();
      $('.netbanking').show();
      $('#bankname').show();
      $('.refund_card').hide();
      $('.date').show();
      $('.cardCharge').hide();
      $("#cardCharge").prop('disabled',true);
      $("#cd_no").attr('required',false);
      $("#nb_rn").attr('required',true);
      $("#currentdate").attr('required',true);
      $("#chq_no").attr('required',false);
      $("#dd_no").attr('required',false);
    }else{
      var amount="";
      $('.refund_hideshow').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow').hide();
      $('.refund_hideshow1').hide();
      $('.refund_hideshow2').hide();
      $('.refund_card').hide();
      $('.netbanking').hide();
      $('.cardCharge').hide();
      $("#cardCharge").prop('disabled',true);
      $("#cd_no").attr('required',false);
      $("#nb_rn").attr('required',false);
      $("#currentdate").attr('required',false);
      $("#chq_no").attr('required',false);
      $("#dd_no").attr('required',false);
    }
}

function fee_receipt_view(transId) {
 $.ajax({
    url: '<?php echo site_url('feesv2/fees_collection/receipt_view_history'); ?>',
    type: 'post',
    data: {'transId':transId},
    success: function(data) {
      var resData = JSON.parse(data);
      console.log(resData);
      $("#receipt_view").html(prepare_receipt_table(resData));
    }
  });
}

function prepare_receipt_table(resData) {
  var refund = '<?php echo $refund ?>';
  var fee_adjustment_amount = '<?php echo $fee_adjustment_amount ?>';
  var res = '';
  res += '<table class="table table-bordered" id="mytable">';
  res += '<thead>';
  res += '<tr>';
  res += '<th>Installment</th>';
  res += '<th>Fee Name</th>';
  res += '<th>Fee Amount</th>';
  res += '<th>Amount Paid</th>';
  res += '<th>Concession Amount</th>';
  if (fee_adjustment_amount) {
    res += '<th>Adjustment Amount</th>';
  }
  if (refund) {
    res += '<th>Refund Amount</th>';
  }
  res += '</tr>'
  res += '</thead>';
  res += '<tbody>';
  var total_amount = 0;
  var total_concession = 0;
  var total_adjustment = 0;
  var total_paid = 0;
  var total_component_paid = 0;
  var refundAmount = 0;
  for(var key in resData){
    total_amount += resData[key].component_amount;
    total_paid += resData[key].amount_paid + resData[key].concession_amount;
    total_component_paid += parseFloat(resData[key].amount_paid) ;
    total_concession += parseFloat(resData[key].concession_amount);
    total_adjustment += parseFloat(resData[key].adjustment_amount);
    refundAmount += parseFloat(resData[key].refund_amount);
    res += '<tr>';
    res += '<td>'+resData[key].insName+'</td>';
    res += '<td>'+resData[key].compName+'</td>';
    res += '<td>'+numberToCurrency(resData[key].component_amount)+'</td>';
    res += '<td>'+numberToCurrency(resData[key].amount_paid)+'</td>';
    res += '<td> ( '+numberToCurrency(resData[key].concession_amount)+' ) </td>';
    if (fee_adjustment_amount) {
      res += '<td> ( '+numberToCurrency(resData[key].adjustment_amount)+' ) </td>';
    }
    if (refund) {
      res += '<td>'+numberToCurrency(resData[key].refund_amount)+'</td>';
    }
    res += '</tr>';
  }
  res += '<tr>';
  res += '<th style="text-align: right"  colspan="3">Total</th>';
  res += '<th>'+numberToCurrency(total_component_paid)+'</th>';
  res += '<th>'+numberToCurrency(total_concession)+'</th>';
  if (fee_adjustment_amount) {
    res += '<th>'+numberToCurrency(total_adjustment)+'</th>';
  }
  if (refund) {
    res += '<th>'+numberToCurrency(refundAmount)+'</th>';
  }
  
  res += '</tr>';
  res += '</tbody>';

  res += '</table>';
  return res;
}

function check_status_of_refund(refund_id, trans_id, stdId, bpId,  paymentType='Online Payment'){
  $.ajax({
    url: '<?php echo site_url('feesv2/refund_controller/check_status_of_refund_by_id'); ?>',
    type: 'post',
    data: {'refund_id':refund_id},
    success: function(data) {
      location.reload();
      if(data){
        new PNotify({
          title: 'Success',
          text: 'Successfully Refunded',
          type: 'success',
        });
      }else{
        new PNotify({
          title: 'Error',
          text: 'Something went wrong',
          type: 'error',
        });
    }
    }
    
  });
}

</script>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
</script>
<div id="receipt_view_model" class="modal fade" role="dialog">
  <div class="modal-dialog" style="margin: auto;width: 80%">
    <div class="panel panel-default new-panel-style_3">
      <div class="panel-heading new-panel-heading">
        <h3 class="panel-title">Receipt Details</h3>
      </div>
      <div class="panel-body">
        <div id="receipt_view" style="max-height: 350px; overflow-y: scroll;"></div> 
      </div>
      <div class="panel-footer">
        <button type="button" class="btn btn-warning pull-right" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}
.widthadjust{
  width: 48%;
  margin: auto;
}

.bootbox .modal-dialog {
  max-width: 600px;
  margin: auto;
}
</style>