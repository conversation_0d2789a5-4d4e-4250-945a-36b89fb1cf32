<?php 
    $colors = array(
        1 => 'color: #ce800dd6;font-style:normal;',
        2 => 'color: #ce800dd6;font-style:normal;',
        3 => 'color: #ce800dd6;font-style:normal;',
        4 => 'color: #ce800dd6;font-style:normal;',
        5 => 'color: #ce800dd6;font-style:normal;',
        6 => 'color:#118b9ac7;font-style:italic;',
    );
    $boards = $this->settings->getSetting('board');
?>
<div class="panel panel-default">
	<div class="panel-body" style="border-top:2px solid black;padding:0px 10px;">
		<input type="hidden" name="selectedMonth" id="selectedMonth" value="<?php echo $date ?>">
		<div style="width:100%;margin-bottom: 2%;">
			<div style="display: inline-block;width: 9%;margin-top: 2%;" onclick="getEvents('prev')">
				<span class="fa fa-arrow-circle-left" style="color:#00701a;font-size: 36px;"></span>
          		<span class="sr-only">Previous</span>
          	</div>
			<div id="month" style="display: inline-block;width: 80%;text-align: center;font-weight: 700;font-size: 20px;"><?php echo date('M Y', strtotime($date)); ?></div>
			<div style="display: inline-block;width: 9%;margin-top: 2%;" onclick="getEvents('next')">
				<span class="fa fa-arrow-circle-right" style="color:#00701a;font-size: 36px;"></span>
          		<span class="sr-only">Next</span>
      		</div>
		</div>
	</div>
	<div class="panel-body" style="border-top:1px solid black;border-bottom:1px solid black;padding:5px 5px 5px 20px;">
		<?php if(count($boards) > 1) {
	            foreach ($boards as $id => $name) {
	                echo '<p style="'.$colors[$id].'display: inline;"><span class="fa fa-square"></span>&nbsp;'.$name.'</p>&nbsp;&nbsp;';
	            }
            }
        ?>
	</div>
	<div class="panel-body" style="max-height: 600px;overflow-y: scroll;">
		<div class="profile-image" id="calendar-events">
			
		</div>
	</div>
</div>

<div class="visible-xs visible-sm visible-md">
  <a href="<?php echo site_url('dashboard');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style type="text/css">
	.event-tile{
		padding: 3px 5px;
		margin-bottom: 3%;
		min-height: 70px;
		border-radius: 5px;
	}
	.event-date{
		font-weight: 500;
		font-size: 15px;
	}
	.event-date>small {
		font-weight: 200;
	}
	.event-name {
		font-size: 12px;
		line-height: 17px;
	}
</style>

<script type="text/javascript">
	var colors = [];
	var calendarStartDate = null;
	var calendarEndDate = null;

	$(document).ready(function(){
		colors = JSON.parse('<?php echo json_encode($colors); ?>');

		// Get calendar date range if available
		<?php if(isset($calendar_start_date) && isset($calendar_end_date)): ?>
		calendarStartDate = '<?php echo $calendar_start_date; ?>';
		calendarEndDate = '<?php echo $calendar_end_date; ?>';
		<?php endif; ?>

		getEvents('current');
	});

	function getEvents(state) {
		var cDate = $("#selectedMonth").val();

		// Handle navigation with calendar date range validation
		if (state === 'next') {
			var nextMonth = new Date(cDate + '-01');
			nextMonth.setMonth(nextMonth.getMonth() + 1);
			var nextMonthStr = nextMonth.getFullYear() + '-' + String(nextMonth.getMonth() + 1).padStart(2, '0');

			// Check if next month is within calendar range
			if (calendarEndDate) {
				var calendarEnd = new Date(calendarEndDate);
				var calendarEndMonth = calendarEnd.getFullYear() + '-' + String(calendarEnd.getMonth() + 1).padStart(2, '0');
				if (nextMonthStr > calendarEndMonth) {
					return; // Don't navigate beyond calendar end date
				}
			}
			cDate = nextMonthStr;
		} else if (state === 'prev') {
			var prevMonth = new Date(cDate + '-01');
			prevMonth.setMonth(prevMonth.getMonth() - 1);
			var prevMonthStr = prevMonth.getFullYear() + '-' + String(prevMonth.getMonth() + 1).padStart(2, '0');

			// Check if previous month is within calendar range
			if (calendarStartDate) {
				var calendarStart = new Date(calendarStartDate);
				var calendarStartMonth = calendarStart.getFullYear() + '-' + String(calendarStart.getMonth() + 1).padStart(2, '0');
				if (prevMonthStr < calendarStartMonth) {
					return; // Don't navigate before calendar start date
				}
			}
			cDate = prevMonthStr;
		}

		// Check which calendar module is enabled and use appropriate endpoint
		<?php if ($this->authorization->isModuleEnabled('CALENDAR_EVENTS_V2')): ?>
			// Use Calendar V2 endpoint
			$.ajax({
	            url: '<?php echo site_url('calendar_events_v2/Calendar_events_v2/getEventsV2'); ?>',
	            type: 'post',
	            data: {'date':cDate,'state':'current', 'applicable_to':1},
	            success: function(data) {
	                console.log('Calendar V2 response (Mobile):', data);
	                try {
	                    var parsedData = JSON.parse(data);
	                    var events = parsedData.events;
	                    var displayDate = parsedData.displayDate;
	                    var date = parsedData.date;

	                    $("#month").html(displayDate);
	                    $("#selectedMonth").val(date);

	                    if(events.length == 0) {
	                    	$("#calendar-events").html('<h3 class="text-center">No Events</h3>' +
	                    	    '<p style="font-size:12px;color:#666;">Source: Calendar V2</p>');
	                    } else {
	                    	var html = '';
	                    	for (var i = 0; i < events.length; i++) {
	                    		var border = 'border-left:5px solid #e96d6d;box-shadow:0px 0px 5px #e96d6d;';
	                    		var eventType = '(Holiday)';
	    						if(events[i].event_type == 1 || events[i].event_type == 4) {
	    							eventType = '(Event)';
	    							border = 'border-left:5px solid #00701a;box-shadow:0px 0px 5px #00701a;';
	    						} else if(events[i].event_type == 5 || events[i].event_type == 6) {
	    							eventType = '(Info)';
	    							border = 'border-left:5px solid #ffb366;box-shadow:0px 0px 5px #1caf9a;';
	    						}
	    						html += '<div class="event-tile" style="'+border+'">';
	    						html += '<p class="event-date">' +events[i].event_on+ '<small class="pull-right">'+eventType+'</small></p>';
	    						var names = events[i].names;
	    						var boards = events[i].board;
	    						for (var j = 0; j < names.length; j++) {
	    							html +='<p class="event-name" style="'+colors[boards[names[j]]]+';"><span style="font-weight:900;font-size:18px;">'+names[j].substring(0, 1)+'</span>'+ names[j].substring(1) +'</p>';
	    						}
	    						html += '</div>';
	                    	}
	                    	$("#calendar-events").html(html);
	                    }
	                } catch (e) {
	                    console.error('Error parsing Calendar V2 JSON (Mobile):', e);
	                    console.log('Raw Calendar V2 response (Mobile):', data);
	                    $("#calendar-events").html('<h3 class="text-center">Error loading Calendar V2 events</h3>');
	                }
	            },
	            error: function(xhr, status, error) {
	                console.error('Calendar V2 AJAX Error (Mobile):', status, error);
	                console.log('Calendar V2 Response (Mobile):', xhr.responseText);
	                $("#calendar-events").html('<h3 class="text-center">Error loading Calendar V2 events</h3>');
	            }
	        });
		<?php else: ?>
			// Use Calendar V1 endpoint
			$.ajax({
	            url: '<?php echo site_url('calender_events/Calender_controller/getEvents'); ?>',
	            type: 'post',
	            data: {'date':cDate,'state':'current', 'applicable_to':1},
	            success: function(data) {
	                console.log('Calendar V1 response (Mobile):', data);
	                var parsedData = JSON.parse(data);
	                var events = parsedData.events;
	                var displayDate = parsedData.displayDate;
	                var date = parsedData.date;

	                $("#month").html(displayDate);
	                $("#selectedMonth").val(date);

	                if(events.length == 0) {
	                	$("#calendar-events").html('<h3 class="text-center">No Events</h3><p style="font-size:12px;color:#666;">Source: Calendar V1</p>');
	                } else {
	                	var html = '';
	                	for (var i = 0; i < events.length; i++) {
	                		var border = 'border-left:5px solid #e96d6d;box-shadow:0px 0px 5px #e96d6d;';
	                		var eventType = '(Holiday)';
						if(events[i].event_type == 1 || events[i].event_type == 4) {
							eventType = '(Event)';
							border = 'border-left:5px solid #00701a;box-shadow:0px 0px 5px #00701a;';
						} else if(events[i].event_type == 5 || events[i].event_type == 6) {
							eventType = '(Info)';
							border = 'border-left:5px solid #ffb366;box-shadow:0px 0px 5px #1caf9a;';
						}
						html += '<div class="event-tile" style="'+border+'">';
						html += '<p class="event-date">' +events[i].event_on+ '<small class="pull-right">'+eventType+'</small></p>';
						var names = events[i].names;
						var boards = events[i].board;
						for (var j = 0; j < names.length; j++) {
							html +='<p class="event-name" style="'+colors[boards[names[j]]]+';"><span style="font-weight:900;font-size:18px;">'+names[j].substring(0, 1)+'</span>'+ names[j].substring(1) +'</p>';
						}
						html += '</div>';
	                	}
	                	$("#calendar-events").html(html);
	                }
	            },
	            error: function(xhr, status, error) {
	                console.error('Calendar V1 AJAX Error (Mobile):', status, error);
	                $("#calendar-events").html('<h3 class="text-center">Error loading Calendar V1 events</h3>');
	            }
	        });
		<?php endif; ?>
	}


</script>