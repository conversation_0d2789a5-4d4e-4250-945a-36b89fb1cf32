<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2') ?>">Attendance</a></li>

  <li><a href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/AttendanceReport'); ?>">Day-wise Attendance Report</a></li>
  
</ul>

<style>
  #studentAttendanceTable th,
  #studentAttendanceTable td {
    white-space: nowrap;
  }

  /* Custom holiday styling */
  .table-holiday {
    background-color: #fff7e0 !important;
    color: #e1a100 !important;
    font-weight: 600;
  }

</style>

<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('attendance_day_v2/Attendance_day_v2/AttendanceReport'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            Day-wise Attendance Report
          </h3>
          <ul class="panel-controls">
            <li>
              <input type='hidden' name='from' value=''>
              <input type='hidden' name='to' value=''>
              <input type='hidden' name='section' value="">
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="card-body" style="overflow: auto;height:700px">
    <?php if (!empty($details)) { ?>
    <?php
    // Replace the original date range generation
    // Original code:
    // $orig_dates = [];
    // foreach ($details as $date => $students) {
    //     $orig_dates[] = $date;
    // }
    // $orig_dates = array_unique($orig_dates);
    // sort($orig_dates);
    // $start_date = date('Y-m-d', strtotime($orig_dates[0]));
    // $end_date = date('Y-m-d', strtotime(end($orig_dates)));
    // $all_dates = [];
    // $current = strtotime($start_date);
    // $end = strtotime($end_date);
    // while ($current <= $end) {
    //     $all_dates[] = date('Y-m-d', $current);
    //     $current = strtotime('+1 day', $current);
    // }
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $start_date = date('Y-m-d', strtotime($from_date));
    $end_date = date('Y-m-d', strtotime($to_date));
    $all_dates = [];
    $current = strtotime($start_date);
    $end = strtotime($end_date);
    while ($current <= $end) {
        $all_dates[] = date('Y-m-d', $current);
        $current = strtotime('+1 day', $current);
    }
    ?>
    <table id="studentAttendanceTable" class="table table-bordered" style="margin-top:1rem" >
    <thead>
        <tr>
            <th class="text-center">Sl#</th>
            <th class="text-center">Student Name</th>
            <th class="text-center">Admission No</th>
            <th class="text-center">Roll No</th>
            <th class="text-center">Enrollment No</th>
            <th class="text-center">Stats No</th>
            <th class="text-center">PEN No</th>
            <th class="text-center">Class</th>
            <?php
            foreach ($all_dates as $date) {
                $displayDate = date('d-M-Y', strtotime($date));
                $isHoliday = in_array($date, $holidays);
                $isWeekend = in_array(date('N', strtotime($date)), [6, 7]);
                $td_class = $isHoliday ? 'table-holiday' : ($isWeekend ? 'table-warning' : '');
                echo "<th class='text-center $td_class'>{$displayDate}</th>";
            }
            ?>
            <th class="text-center">Present Days</th>
            <th class="text-center">Total Days</th>
            <th class="text-center">Attendance %</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $sl = 1;
        $student_data = [];
        foreach ($details as $date => $students) {
            foreach ($students as $student) {
                $id = $student['admission_no'];
                if (!isset($student_data[$id])) {
                    $student_data[$id] = [
                        'std_name' => $student['std_name'],
                        'admission_no' => $student['admission_no'],
                        'csName' => $student['csName'],
                        'attendance' => [],
                        'present_days' => 0,
                        'total_days' => $student['total_days'],
                    ];
                }

                $fmtDate = $date;
                // Get session count from the data or use a default
                $sessionCount = isset($student['session_count']) ? $student['session_count'] :
                               (isset($student['total_days']) && $student['total_days'] == 1 ? 1 : 2);

                // Store attendance data with session count
                $student_data[$id]['attendance'][$fmtDate] = [
                    'morning' => $student['morning'],
                    'afternoon' => $student['afternoon'],
                    'present' => $student['present'],
                    'session_count' => $sessionCount
                ];

                // Calculate present days based on session count
                if ($sessionCount == 1) {
                    // For single session, it's either 1 (present) or 0 (absent)
                    $student_data[$id]['present_days'] += ($student['morning'] == 1) ? 1 : 0;
                } else {
                    // For two sessions, use the existing 'present' value (0, 0.5, or 1)
                    $student_data[$id]['present_days'] += $student['present'];
                }
            }
        }

        foreach ($student_data as $student) { ?>
            <tr>
                <td class="text-center"><?= $sl++ ?></td>
                <td><?= $student['std_name'] ?></td>
                <td class="text-center"><?= $student['admission_no'] ?></td>
                <td class="text-center"><?= isset($student['roll_no']) ? $student['roll_no'] : '-' ?></td>
                <td class="text-center"><?= isset($student['enrollment_number']) ? $student['enrollment_number'] : '-' ?></td>
                <td class="text-center"><?= isset($student['sts_number']) ? $student['sts_number'] : '-' ?></td>
                <td class="text-center"><?= isset($student['pen_number']) ? $student['pen_number'] : '-' ?></td>
                <td><?= $student['csName'] ?></td>
                <?php
                foreach ($all_dates as $date) {
                    $key = date('d-M-Y', strtotime($date));
                    $isHoliday = in_array($date, $holidays);
                    $isWeekend = in_array(date('N', strtotime($date)), [6, 7]);

                    if ($isHoliday) {
                        $status = 'H';
                        $td_class = 'table-holiday';
                    } elseif (isset($student['attendance'][$key])) {
                        $att = $student['attendance'][$key];
                        // Determine session count - use the session_count from our stored data
                        $sessions = isset($att['session_count']) ? $att['session_count'] : (($isWeekend) ? 1 : 2);

                        if ($sessions == 1) {
                            // For single session (full day), treat as full day present or absent
                            if ($att['morning'] == 1) {
                                $status = 'P'; // Full day present
                                $td_class = 'table-success';
                            } else {
                                $status = 'A'; // Full day absent
                                $td_class = 'table-danger';
                            }
                        } else {
                            // For two sessions (morning and afternoon)
                            $morning = $att['morning'];
                            $afternoon = $att['afternoon'];

                            if ($morning == 1 && $afternoon == 1) {
                                $status = 'P'; // Full day present
                                $td_class = 'table-success';
                            } elseif ($morning == 1 || $afternoon == 1) {
                                $status = 'H'; // Half day
                                if ($morning == 1) {
                                    $status .= '(M)';
                                }
                                if ($afternoon == 1) {
                                    $status .= '(A)';
                                }
                                $td_class = 'table-warning';
                            } else {
                                $status = 'A'; // Full day absent
                                $td_class = 'table-danger';
                            }
                        }
                    } else {
                        $status = '-';
                        $td_class = ($isWeekend) ? 'table-warning' : 'table-danger';
                    }
                    ?>
                    <td class="text-center <?= $td_class ?>"><?= $status ?></td>
                <?php } ?>
                <td class="text-center"><?= number_format($student['present_days'], 1) ?></td>
                <td class="text-center"><?= $student['total_days'] ?></td>
                <td class="text-center">
                <?= ($student['total_days'] > 0) ? number_format(($student['present_days'] / $student['total_days']) * 100, 1) . '%' : 'N/A' ?>
                </td>
            </tr>
        <?php } ?>
    </tbody>
</table>

<?php } else { ?>
    <div class="no-data-display">No data found for the selected date range.</div>
<?php } ?>


</div>

  </div>
</div>



<script>
$(document).ready(function() {
    $('#studentAttendanceTable').DataTable({
        dom: 'Bfrtip',
        buttons: [
            { extend: 'excel', className: 'btn btn-info' }
        ],
        ordering: false,
        searching: false,
        paging: false,
        scrollX: true
    });
});
</script>



