<style>
    

    .container {
        max-width: 900px;
        margin: 40px auto;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 32px 40px 40px 40px;
    }

    .attendance-header {
        font-size: 1.6rem;
        font-weight: 600;
        margin-bottom: 16px;
        text-align: center;
    }

   

 

    .summary {
        display: flex;
        justify-content: space-between;
        align-items: stretch;
        gap: 0;
        margin-bottom: 32px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.03);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .summary-card {
        flex: 1;
        background: #fff;
        border-right: 1px solid #f0f0f0;
        border-radius: 0;
        padding: 24px 0 16px 0;
        text-align: center;
    }

    .summary-card:last-child {
        border-right: none;
    }

    .summary-card .label {
        font-size: 1.05rem;
        color: #888;
        margin-bottom: 10px;
    }

    .summary-card .value {
        font-size: 2rem;
        font-weight: 700;
        letter-spacing: 1px;
    }

    .summary-card .value.attendance { color: #5b6dfa; }
    .summary-card .value.present { color: #2ca66f; }
    .summary-card .value.absent { color: #e14c4c; }
    .summary-card .value.holiday { color: #e1a100; }

    /* Calendar container */
    #calendar {
        background: #fff;
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        border: 1px solid #eee;
    }

    /* Day numbers */
    .fc .fc-daygrid-day-number {
        font-size: 0.95rem;
        padding: 6px;
        color: #333;
    }

    /* Today highlight */
    .fc .fc-day-today {
        background: #f5f7ff !important;
        border: 1px solid #c7d1ff !important;
    }

    .fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
        background-color: #4c6ef5;
        color: #fff;
        border-radius: 50%;
        padding: 4px 8px;
        display: inline-block;
    }

    /* Weekends (optional light gray) */
    .fc .fc-day-sat, .fc .fc-day-sun {
        background-color: #fafafa;
    }

    /* Event pills */
    .fc-event {
        border: none !important;
        padding: 2px 12px !important;
        border-radius: 5px !important; /* Rectangle with slightly rounded corners */
        font-size: 0.97rem !important;
        font-weight: 500 !important;
        margin: 2px 0 !important;
        display: inline-flex !important;
        align-items: center;
        justify-content: flex-start;
        white-space: nowrap;
        box-shadow: none !important;
        gap: 6px;
        letter-spacing: 0.01em;
    }

    .fc-event.present {
        background: #2ca66f !important;
        color: #fff !important;
        border: none !important;
        font-weight: 600 !important;
        position: relative;
    }
    .fc-event.present .event-label {
        color: #fff !important;
    }
    .fc-event.absent {
        background: #e14c4c !important;
        color: #fff !important;
        border: none !important;
        font-weight: 600 !important;
        position: relative;
    }
    .fc-event.absent .event-label {
        color: #fff !important;
    }
    .fc-event.holiday {
        background: #ffe7a0 !important;
        color: #b68400 !important;
        border: none !important;
        font-weight: 600 !important;
    }
    .fc-event.holiday .event-label {
        color: #b68400 !important;
    }
    .fc-event.late {
        background: #fffbe6 !important;
        color: #b68400 !important;
        border: 1px solid #ffe58f !important;
        font-weight: 600 !important;
    }
    .fc-event.late .event-label {
        color: #b68400 !important;
    }
    .fc-event.halfday {
        background: #fffbe6 !important;
        color: #b68400 !important;
        border: 1px solid #ffe58f !important;
        font-weight: 600 !important;
    }
    .fc-event.halfday .event-label {
        color: #b68400 !important;
    }
    .fc-event.weekoff {
        background: #f5f5f5 !important;
        color: #888 !important;
        border: none !important;
        opacity: 0.7;
    }
    .fc-event.weekoff .event-label {
        color: #888 !important;
    }

    /* Optional: wrap multiple events per day as rows */
    .fc-daygrid-event-harness {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 2px;
    }

    /* Clean up event text (remove bullet icon if used before) */
    .fc-event .fc-event-title::before {
        /* Overridden above for each type */
    }

    /* Adjust spacing inside each day */
    .fc .fc-daygrid-day-frame {
        padding: 4px;
    }

    .fc-event .event-icon {
        display: inline-block;
        font-size: 1.1em;
        margin-right: 6px;
        vertical-align: middle;
        font-weight: bold;
    }
    .fc-event .event-icon i {
        font-size: 14px;
    }
    .fc-event .event-label {
        display: inline-block;
        font-size: 1em;
        vertical-align: middle;
        font-weight: 600;
        letter-spacing: 0.01em;
    }
</style>


<?php 
$present_days= isset($getOverallDetails['present_days']) ? $getOverallDetails['present_days'] : '--';
$absent_days= isset($getOverallDetails['absent_days']) ? $getOverallDetails['absent_days'] : '--';
$holidays= isset($getOverallDetails['holidays']) ? $getOverallDetails['holidays'] : '--';
$total_days= isset($getOverallDetails['total_days']) ? $getOverallDetails['total_days'] : '--';

// Calculate percentage correctly: (present_days / (total_days - holidays)) * 100
if (is_numeric($present_days) && is_numeric($total_days) && is_numeric($holidays) && ($total_days - $holidays) > 0) {
    $percentage = round(($present_days / ($total_days - $holidays)) * 100, 2);
} else {
    $percentage = '--';
}
?>

<div class="container" style="margin-top:6rem;">
    <div class="attendance-header" style="text-align:center; margin-bottom:12px;">Attendance</div>

    <div class="summary">
        <div class="summary-card">
            <div class="label">Overall Attendance</div>
            <div class="value attendance" style="color: #5b6dfa; font-size: 2.1rem;">
                <?php echo is_numeric($percentage) ? $percentage.'%' : '--'; ?>
            </div>
        </div>
        <div class="summary-card">
            <div class="label">Present Days</div>
            <div class="value present" style="color: #2ca66f; font-size: 2.1rem;">
                <?php echo is_numeric($present_days) ? $present_days : '--'; ?>
            </div>
        </div>
        <div class="summary-card">
            <div class="label">Absent Days</div>
            <div class="value absent" style="color: #e14c4c; font-size: 2.1rem;">
                <?php echo is_numeric($absent_days) ? $absent_days : '--'; ?>
            </div>
        </div>
        <div class="summary-card">
            <div class="label">Late Count</div>
            <div class="value holiday" style="color: #b68400; font-size: 2.1rem;">
                <?php echo isset($getOverallDetails['late_count']) && is_numeric($getOverallDetails['late_count']) ? $getOverallDetails['late_count'] : '--'; ?>
            </div>
        </div>
    </div>

    <!-- Month-wise Breakdown -->
    <?php if (isset($getOverallDetails['monthly_data']) && !empty($getOverallDetails['monthly_data'])): ?>
    <div class="monthly-breakdown" style="margin: 30px 0; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); padding: 24px; border: 1px solid #f0f0f0;">
        <h4 style="margin-bottom: 20px; color: #333; font-weight: 600; text-align: center;">Month-wise Attendance Breakdown</h4>
        <div class="table-responsive">
            <table class="table table-bordered table-striped" style="margin-bottom: 0;">
                <thead style="background: #f8f9fa;">
                    <tr>
                        <th class="text-center" style="padding: 12px; font-weight: 600;">Month</th>
                        <th class="text-center" style="padding: 12px; font-weight: 600;">Present Sessions</th>
                        <th class="text-center" style="padding: 12px; font-weight: 600;">Absent Sessions</th>
                        <th class="text-center" style="padding: 12px; font-weight: 600;">Total Sessions</th>
                        <th class="text-center" style="padding: 12px; font-weight: 600;">Monthly %</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $monthlyData = $getOverallDetails['monthly_data'];
                    // Sort months chronologically
                    uksort($monthlyData, function($a, $b) {
                        return strtotime("01-$a") - strtotime("01-$b");
                    });

                    foreach ($monthlyData as $month => $data):
                    ?>
                    <tr>
                        <td class="text-center" style="padding: 10px; font-weight: 500;"><?php echo $month; ?></td>
                        <td class="text-center" style="padding: 10px; color: #2ca66f;"><?php echo number_format($data['present_sessions'], 1); ?></td>
                        <td class="text-center" style="padding: 10px; color: #e14c4c;"><?php echo number_format($data['absent_sessions'], 1); ?></td>
                        <td class="text-center" style="padding: 10px;"><?php echo $data['total_sessions']; ?></td>
                        <td class="text-center" style="padding: 10px; font-weight: 600; color: #5b6dfa;"><?php echo $data['attendance_percentage']; ?>%</td>
                    </tr>
                    <?php endforeach; ?>

                    <!-- Overall Summary Row -->
                    <tr style="background: #f8f9fa; font-weight: bold;">
                        <td class="text-center" style="padding: 12px; font-weight: 700;">Overall (Yearly)</td>
                        <td class="text-center" style="padding: 12px; color: #2ca66f;"><?php echo is_numeric($present_days) ? $present_days : '--'; ?></td>
                        <td class="text-center" style="padding: 12px; color: #e14c4c;"><?php echo is_numeric($absent_days) ? $absent_days : '--'; ?></td>
                        <td class="text-center" style="padding: 12px;">
                            <?php
                            $total_sessions = 0;
                            if (isset($getOverallDetails['monthly_data'])) {
                                foreach ($getOverallDetails['monthly_data'] as $data) {
                                    $total_sessions += $data['total_sessions'];
                                }
                            }
                            echo $total_sessions > 0 ? $total_sessions : '--';
                            ?>
                        </td>
                        <td class="text-center" style="padding: 12px; font-weight: 700; color: #5b6dfa;"><?php echo is_numeric($percentage) ? $percentage.'%' : '--'; ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <div id="att_calendar"></div>

    <!-- Calendar Legend -->
    <div class="calendar-legend" style="display: flex; flex-wrap: wrap; justify-content: center; gap: 15px 20px; margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
        <div style="display: flex; align-items: center; gap: 6px; min-width: 100px;">
            <span style="color:#2ca66f; font-size: 16px;"><i class="fa fa-check"></i></span>
            <span style="font-size: 14px; color: #2ca66f; font-weight: 500;">Present</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 100px;">
            <span style="color:#e14c4c; font-size: 16px;"><i class="fa fa-times"></i></span>
            <span style="font-size: 14px; color: #e14c4c; font-weight: 500;">Absent</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 100px;">
            <span style="color:#e1a100; font-size: 16px;"><i class="fa fa-calendar"></i></span>
            <span style="font-size: 14px; color: #e1a100; font-weight: 500;">Holiday</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 100px;">
            <span style="color:#756969; font-size: 16px;"><i class="fa fa-bed"></i></span>
            <span style="font-size: 14px; color: #756969; font-weight: 500;">Weekoff</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 100px;">
            <span style="color:#b68400; font-size: 16px;"><i class="fa fa-clock-o"></i></span>
            <span style="font-size: 14px; color: #b68400; font-weight: 500;">Halfday</span>
        </div>
        <div style="display: flex; align-items: center; gap: 6px; min-width: 100px;">
            <span style="color:#b68400; font-size: 16px;"><i class="fa fa-exclamation-triangle"></i></span>
            <span style="font-size: 14px; color: #b68400; font-weight: 500;">Late</span>
        </div>
    </div>
</div>

<!-- FullCalendar CSS and JS -->
<link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js'></script>

<script>
function fetchAttendanceEvents(startDate, endDate) {
    var student_id = <?php echo isset($studentId) ? json_encode($studentId) : 'null'; ?>;
    return fetch('<?php echo base_url("parent_controller/get_attendance_events"); ?>', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({
            start: startDate,
            end: endDate,
            student_id: student_id
        })
    }).then(response => response.json());
}

document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('att_calendar');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: ''
        },
        events: function(fetchInfo, successCallback, failureCallback) {
            fetchAttendanceEvents(fetchInfo.startStr, fetchInfo.endStr)
                .then(data => successCallback(data))
                .catch(() => failureCallback([]));
        },
        eventDidMount: function(info) {
            // Show icon and label for present, absent, holiday, halfday, late
            let icon = '';
            let label = info.event.title;
            if (info.event.classNames.includes('present')) {
                icon = '<span class="event-icon" style="color:#2ca66f;"><i class="fa fa-check"></i></span>';
            } else if (info.event.classNames.includes('absent')) {
                icon = '<span class="event-icon" style="color:#e14c4c;"><i class="fa fa-times"></i></span>';
            } else if (info.event.classNames.includes('holiday')) {
                icon = '<span class="event-icon" style="color:#e1a100;"><i class="fa fa-calendar"></i></span>';
            } else if (info.event.classNames.includes('halfday')) {
                icon = '<span class="event-icon" style="color:#b68400;"><i class="fa fa-clock-o"></i></span>';
            } else if (info.event.classNames.includes('late')) {
                icon = '<span class="event-icon" style="color:#b68400;"><i class="fa fa-exclamation-triangle"></i></span>';
            } else if (info.event.classNames.includes('weekoff')) {
                icon = '<span class="event-icon" style="color:#756969;"><i class="fa fa-bed"></i></span>';
            }
            // Set both icon and label
            if (info.el.querySelector('.fc-event-title')) {
                info.el.querySelector('.fc-event-title').innerHTML = icon + '<span class="event-label">' + label + '</span>';
            }
            info.el.title = label;
        }
    });
    calendar.render();
});
</script>

