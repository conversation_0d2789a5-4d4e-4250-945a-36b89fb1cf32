<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo isset($page_title) ? $page_title : 'Parent Dashboard'; ?> - School Management</title>
      <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#7b5cff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Parent Dashboard">
    <!-- Latest Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Latest Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Parent Dashboard CSS - Mobile -->
    <!-- <link href="<?php // echo base_url('assets/css/parent/common/variables.css'); ?>" rel="stylesheet">
    <link href="<?php // echo base_url('assets/css/parent/common/base.css'); ?>" rel="stylesheet">
    <link href="<?php // echo base_url('assets/css/parent/common/components.css'); ?>" rel="stylesheet">
    <link href="<?php // echo base_url('assets/css/parent/mobile/mobile_dashboard.css'); ?>" rel="stylesheet">
    <link href="<?php // echo base_url('assets/css/parent/mobile/mobile_navigation.css'); ?>" rel="stylesheet"> -->

    <!-- Dynamic Primary Color -->
    <style>
        <?php
        $primary_color = $this->settings->getSetting('parent_dashboard_primary_color');
        if (empty($primary_color)) {
            $primary_color = '#7b5cff';
        }
        ?>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Nunito', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--light-bg);
            margin: 0;
            padding: 0 0 80px 0;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Mobile-first responsive design */
        .container-mobile {
            max-width: 100%;
            margin: 0 auto;
            padding: 0;
        }

        /* Remove default Bootstrap margins and paddings for mobile */
        .container-fluid {
            padding: 0;
        }

        /* Hide desktop elements on mobile */
        .desktop-only {
            display: none;
        }

        /* Mobile navigation styles */
        .mobile-nav {
            display: block;
        }

        /* Responsive breakpoints */
        @media (min-width: 600px) {
            .container-mobile {
                max-width: 440px;
            }
        }

        @media (min-width: 768px) {
            .desktop-only {
                display: block;
            }

            .mobile-nav {
                display: none;
            }
        }

        /* Loading spinner */
        .loading-spinner {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            display: none;
        }

        .spinner-border-custom {
            width: 3rem;
            height: 3rem;
            border: 0.3em solid rgba(123, 92, 255, 0.2);
            border-top: 0.3em solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Utility classes */
        .text-primary-custom {
            color: var(--primary-color) !important;
        }

        .bg-primary-custom {
            background-color: var(--primary-color) !important;
        }

        .border-primary-custom {
            border-color: var(--primary-color) !important;
        }

        /* Safe area for iOS devices */
        @supports (padding: max(0px)) {
            body {
                padding-bottom: max(0px, env(safe-area-inset-bottom));
            }
        }
    </style>

    <style>
    body {
        background: #F9F9FB;
        font-family: 'Inter', 'Nunito', Arial, sans-serif;
        margin: 0;
        padding: 0;
    }
    .dashboard-header {
        padding: 1rem;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-bottom: 1px solid #f0f0f0;
        box-shadow: 0 4px 12px rgba(123, 92, 255, 0.15);
    }
    .dashboard-header .school-logo {
        height: 32px;
        width: auto;
        margin-right: 0.75rem;
    }
    .school-name {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        flex: 1;
    }
    /* Flash News Marquee */
    .flash-news-container {
        margin: 0.5rem 1rem;
    }

    .flash-news-banner {
        background: #f8f9ff;
        border-radius: 12px;
        border: 1px solid #e8ebff;
        overflow: hidden;
        display: flex;
        align-items: center;
        min-height: 48px;
    }

    .flash-news-left {
        background: #e8ebff;
        padding: 0.75rem 1rem;
        border-right: 1px solid #d1d9ff;
        flex-shrink: 0;
    }

    .flash-news-label {
        color: #6366f1;
        font-size: 0.85rem;
        font-weight: 500;
        text-transform: capitalize;
    }

    .flash-news-divider {
        width: 1px;
        height: 24px;
        background: #d1d9ff;
        margin: 0 0.5rem;
    }

    .flash-news-right {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
        overflow: hidden;
    }

    .flash-news-icon {
        color: #6366f1;
        font-size: 1rem;
        flex-shrink: 0;
        margin-left: 0.75rem;
    }

    .flash-news-marquee {
        background: #CEC3F8;
        color: #161327;
        padding:0.5rem;
        border: none;
        outline: none;
        display: none;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        font-size: 16px;
        font-weight: 500;
        font-style: normal;
        letter-spacing: -0.48px;
}
    }

    .flash-news-item {
        color: #161327;
        display: inline;
        margin-right: 3rem;
        white-space: nowrap;
    }
    .flash-news-separator {
        color: rgba(255, 255, 255, 0.8);
        margin: 0 1.5rem;
        font-weight: bold;
        font-size: 1.2rem;
    }

    /* Fee Status Cards */
    .fee-status-cards {
        display: flex;
        gap: 1rem;
        white-space: nowrap;
    }
    .fee-card {
        min-width: 280px;
        border-radius: 12px;
        padding: 1rem;
        color: white;
        position: relative;
        flex-shrink: 0;
        margin: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: inline-block;
        white-space: normal;
    }
    .fee-card-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    .fee-card-icon {
        font-size: 1.2rem;
        opacity: 0.9;
    }
    .fee-card-text {
        flex: 1;
    }
    .fee-card-title {
        font-size: 0.9rem;
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: 0.25rem;
    }
    .fee-card-amount {
        font-size: 0.8rem;
        font-weight: 600;
        opacity: 0.9;
    }

    /* Welcome Section */
    .welcome-section {
        /* padding: 1.5rem 1rem; */
        /* background: var(--primary-color) !important; */
        /* margin: 0 1rem; */
        /* border-radius: 20px; */
        position: relative;
        overflow: hidden;
    }

    /* Header Gradient Background SVG */
    .welcome-bg-gradient {
        position: absolute;
        inset: 0;
        pointer-events: none;
        z-index: 1;
        overflow: hidden;
        border-radius: inherit;
    }

    .welcome-bg-gradient svg {
        width: 100%;
        height: 100%;
        display: block;
        object-fit: cover;
    }

    /* Device-specific welcome section styles */
    .welcome-section.mobile {
        /* padding: 0.85rem 0.85rem; */
        margin: 0rem 1rem;
        background: var(--primary-color) !important; 
        border-radius: 16px;
    }

    .welcome-section.tablet {
        padding: 1.75rem 1.25rem;
        margin: 0 1.25rem;
        border-radius: 22px;
    }



    .welcome-section.desktop {
        padding: 2rem 1.5rem;
        margin: 0 1.5rem;
        border-radius: 24px;
    }
    .welcome-content {
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 2;
        margin: 0.3rem 0.3rem;
    }
    .dashboard-avatar.studentphoto {
        width: 65px;
        height: 65px;
        border-radius: 14px;
        object-fit: cover;
        border: 2px solid rgba(255,255,255,0.3);
        cursor: pointer;
        margin:0.3rem;
    }

    /* Initials Avatar */
    .initials-avatar {
        width: 65px;
        height: 65px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        border: 2px solid rgba(255,255,255,0.3);
        text-transform: uppercase;
        letter-spacing: 1px;
        flex-shrink: 0;
    }
    .welcome-text {
        flex: 1;
    }
    .welcome-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: white;
    }
    .welcome-sub {
        color: rgba(255,255,255,0.8);
        font-size: 0.85rem;
    }

    /* Bootstrap Transport Carousel Section */
    .transport-carousel-section {
        padding: 0.5rem 1rem;
        /* margin-bottom: 0.5rem; */
        position: relative;
    }

    .transport-carousel-section .carousel {
        position: relative;
    }

    /* Carousel Indicators Styling */
    .transport-carousel-section .carousel-indicators {
        bottom: -35px;
        margin-bottom: 0;
        gap: 0.5rem;
        justify-content: center;
    }

    .transport-carousel-section .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: rgba(123, 92, 255, 0.3);
        opacity: 1;
        border: none;
        margin: 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .transport-carousel-section .carousel-indicators button.active {
        background-color: var(--primary-color);
        transform: scale(1.2);
        box-shadow: 0 3px 8px rgba(123, 92, 255, 0.4);
    }

    /* Carousel Controls Styling */
    .transport-carousel-section .carousel-control-prev,
    .transport-carousel-section .carousel-control-next {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color) 0%, #4e8cff 100%);
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0.9;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(123, 92, 255, 0.25);
        border: 2px solid rgba(255,255,255,0.2);
    }

    .transport-carousel-section .carousel-control-prev {
        left: -50px;
    }

    .transport-carousel-section .carousel-control-next {
        right: -50px;
    }

    .transport-carousel-section .carousel-control-prev:hover,
    .transport-carousel-section .carousel-control-next:hover {
        opacity: 1;
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 16px rgba(123, 92, 255, 0.35);
    }

    .transport-carousel-section .carousel-control-prev-icon,
    .transport-carousel-section .carousel-control-next-icon {
        width: 18px;
        height: 18px;
        filter: brightness(0) invert(1);
    }

    /* Transport Card Container */
    .transport-card-container {
        display: flex;
        justify-content: center;
    }

    .transport-card {
        background: white;
        border-radius: 16px;
        padding: 0.8rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        width: 100%;
        max-width: 320px;
        min-height: 80px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        border: 1px solid rgba(123, 92, 255, 0.1);
    }

    .transport-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(123, 92, 255, 0.15);
        text-decoration: none;
        color: inherit;
        border-color: rgba(123, 92, 255, 0.2);
    }

    .transport-card:focus {
        text-decoration: none;
        color: inherit;
        outline: none;
    }

    /* Hide controls on mobile */
    @media (max-width: 767px) {
        .transport-carousel-section .carousel-control-prev,
        .transport-carousel-section .carousel-control-next {
            display: none;
        }
    }
    .transport-icon {
        display: flex;
        width: 56px;
        height: 56px;
        padding: 16px 17px;
        align-items: center;
        gap: 10px;
        border-radius: 12px;
        background: #EFECFD;
    }

    .transport-content {
        flex: 1;
        min-width: 0;
    }

    .transport-title {
        font-size: 0.95rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.2rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .transport-subtitle {
        font-size: 0.8rem;
        color: #161327;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .transport-action {
        color: var(--primary-color);
        font-weight: 600;
        font-size: 0.9rem;
        flex-shrink: 0;
    }

    .action-btn {
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    .action-btn:hover {
        background-color: rgba(123, 92, 255, 0.1);
    }

    /* Section Title */
    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        padding: 0.5rem 0;
        margin: 0 1rem;
        /*     
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        padding: 1rem 1rem 0.5rem 1rem;
        margin-bottom: 0; */
    }
    .fee-card {
        background: linear-gradient(90deg, #7b5cff 0%, #4e8cff 100%);
        color: #fff;
        border-radius: 16px;
        padding: 1rem;
        margin: 1rem;
        box-shadow: 0 4px 16px rgba(123,92,255,0.08);
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: transform 0.2s;
    }
    .fee-card:hover {
        transform: translateY(-2px);
    }
    .fee-card .fa {
        font-size: 2rem;
        opacity: 0.2;
    }
    .fee-card.no-due {
        background: linear-gradient(90deg, #00b894 0%, #00cec9 100%);
    }
    .fee-card.overdue {
        background: linear-gradient(90deg, #e74c3c 0%, #fd79a8 100%);
    }
    /* Feature Grid - Now using Bootstrap Grid */
    .feature-grid-container {
        padding: 0 0 1rem 0;
    }
    .feature-item {
        text-decoration: none;
        color: inherit;
        text-align: center;
    }
    .feature-tile {
        background: #fff;
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.2rem 0.8rem;
        position: relative;
        min-height: 100px;
        aspect-ratio: 1;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        border: 1px solid rgba(123, 92, 255, 0.1);
        height: 100%;
    }

    .feature-tile:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(123, 92, 255, 0.15);
        border-color: rgba(123, 92, 255, 0.2);
    }
    .feature-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        /* margin-bottom: 0.5rem; */
        width: 48px;
        height: 48px;
        /* margin: 0 auto 0.5rem auto; */
    }

    /* SVG icons in feature tiles */
    .feature-icon-wrapper svg {
        width: 32px !important;
        height: 32px !important;
        fill: var(--primary-color) !important;
    }

    /* Fallback icons in feature tiles */
    .fallback-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .fallback-icon span {
        color: white;
        font-weight: bold;
        font-size: 18px;
        letter-spacing: 1px;
    }
    .feature-badge {
        position: absolute;
        top: 26px;
        right: 27px;
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
        color: white;
        border-radius: 6px;
        min-width: 20px;
        height: 16px;
        padding: 0 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.65rem;
        font-weight: 700;
        box-shadow: 0 2px 6px rgba(255, 71, 87, 0.3);
    }
    .welcome-title{
        font-size: 18px;
    }
    #studentName{
        color: #fff;
        font-family: Inter;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 110%; /* 28.6px */
    }
    .feature-tile:hover {

        transform: translateY(-2px);
    }
    .feature-tile .fa, .feature-tile img {
        font-size: 1.7rem;
        margin-bottom: 0.3rem;
    }
    .feature-grid a{
        text-decoration: none;
    }
    .feature-tile svg {
        width: 30px !important;
        height: 30px !important;
    }
    .feature-tile svg path,
    .feature-tile svg circle,
    .feature-tile svg rect,
    .feature-tile svg polygon {
        /* fill: var(--primary-color) !important;
        stroke: var(--primary-color) !important; */
    }
    .feature-label {
        color: #333;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 0.75rem;
        font-style: normal;
        font-weight: 500;
        line-height: 1.2;
        /* margin-top: 0.5rem; */
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;
        hyphens: auto;
    }

.feature-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}

   
    .badge-notify {
        position: absolute;
        top: 8px;
        right: 8px;
        background: linear-gradient(135deg, #ff3b30 0%, #ff2d20 100%);
        color: #fff;
        font-size: 0.7rem;
        border-radius: 6px;
        padding: 0.2em 0.6em;
        font-weight: 700;
        min-width: 20px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        box-shadow: 0 3px 8px rgba(255,59,48,0.25);
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        background: #fff;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 62px;
        z-index: 100;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
    }
    .bottom-nav .nav-item {
        flex: 1;
        text-align: center;
        color: #666;
        font-size: 0.65rem;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: color 0.2s ease;
        padding: 0.5rem 0.25rem;
    }
    .bottom-nav .nav-item.active {
        color: var(--primary-color);
        font-weight: 600;
    }
    .bottom-nav .nav-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }
    .bottom-nav .nav-icon .fa {
        font-size: 1rem;
    }
    .bottom-nav .dashboard-avatar-footer {
        width: 26px;
        height: 26px;
        border-radius: 50%;
        object-fit: cover;
        border: 1px solid #e0e0e0;
    }

    /* Bottom nav initials avatar */
    .bottom-nav .initials-avatar {
        width: 20px;
        height: 20px;
        font-size: 0.6rem;
        border: 1px solid #e0e0e0;
        letter-spacing: 0.5px;
    }
    /* Mobile First - Base styles above are for mobile */

    /* Small devices (landscape phones, 576px and up) */
    @media (min-width: 576px) {
        .feature-tile {
            padding: 1.4rem 0.9rem;
            min-height: 110px;
        }
        .feature-label {
            font-size: 0.85rem;
        }
        .transport-card {
            min-height: 85px;
        }
    }

    /* Medium devices (tablets, 768px and up) */
    @media (min-width: 768px) {
        .dashboard-header, .welcome-section, .flash-news-marquee, .transport-carousel-section, .section-title {
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        .feature-tile {
            padding: 1.6rem 1.2rem;
            min-height: 120px;
        }
        .welcome-section {
            padding: 2rem 1.5rem;
        }
        .transport-card {
            min-height: 90px;
            padding: 1.5rem;
        }
    }

    /* Large devices (desktops, 992px and up) */
    @media (min-width: 992px) {
        .dashboard-header, .welcome-section, .flash-news-marquee, .transport-carousel-section, .section-title {
            max-width: 600px;
        }
    }

    /* Tablet specific styles */
    @media (min-width: 768px) and (max-width: 1024px) {
        .feature-grid {
            grid-template-columns: repeat(4, 1fr);
        }
        .feature-tile {
            padding: 1rem 0.5rem;
        }
        .welcome-section {
            padding: 0.75rem;
        }
        .transport-carousel-section {
            padding: 0.5rem 0.75rem;
        }
        .transport-card {
            max-width: 280px;
            padding: 0.75rem;
        }
        .transport-carousel-section .carousel-control-prev {
            left: -35px;
        }
        .transport-carousel-section .carousel-control-next {
            right: -35px;
        }
    }

    /* Mobile specific styles */
    @media (max-width: 599px) {
        .transport-card {
            max-width: 100%;
            min-height: 75px;
        }

        .transport-carousel-section .carousel-indicators {
            bottom: -30px;
        }

        .feature-tile {
            min-height: 95px;
            padding: 1rem 0.6rem;
        }
    }

    /* Loading Overlay */
    #loadingOverlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        max-width: 200px;
    }

    .loading-text {
        margin-top: 1rem;
        color: #333;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .spinner-border {
        width: 1rem;
        height: 1rem;
        color: var(--primary-color) !important;
    }

    /* Footer Profile Loading Styles */
    .footer-profile-container {
        position: relative;
        display: inline-block;
        width: 32px;
        height: 32px;
    }

    .footer-profile-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        padding: 4px;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .footer-initials-avatar {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 12px;
        letter-spacing: 0.5px;
        margin: 0 auto;
    }

    /* Profile photo container styles */
    .profile-photo-container {
        position: relative;
        display: inline-block;
    }

    .profile-loading-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        padding: 8px;
        z-index: 10;
        display: none;
    }

    /* Welcome content loading styles */
    .inline-loading-spinner {
        display: inline-block;
        margin-left: 8px;
    }

    .inline-loading-spinner .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.1em;
    }
</style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border-custom"></div>
    </div>

    <!-- Main Container -->
    <div class="container-mobile">

