<!-- Profile Detail Page - Mobile UI -->

<div class="mobile-container">
    <!-- Header with back button and title -->
    <div class="profile-header">
        <button class="back-btn" onclick="window.history.back()">
            <i class="fa fa-chevron-left"></i>
        </button>
        <div class="profile-header-title">Profile Detail</div>
        <div style="width:32px;"></div>
    </div>

    <!-- Profile Status Alert -->
    <?php if ($studentData['profile_status'] == 'Unlock') { ?>
    <div class="profile-alert">
        <div class="alert-content">
            <div class="alert-icon">
                <?php $this->load->view('svg_icons/parent/warning_profile.svg'); ?>
            </div>
            <div class="alert-body">
                <div class="alert-title">Please confirm your profile.</div>
                <div class="alert-description">
                    Please review all details carefully before confirming your profile.
                </div>
                <div class="alert-warning">
                    Once confirmed, edits will no longer be possible.
                </div>
                <button class="alert-button"
                        style="background: #ffffff; color: #404040; border: 0px solid #d0d0d0; border-radius: 8px; padding: 0.75rem 3rem; font-size: 0.9rem; font-weight: 300; cursor: pointer; width: 100%; max-width: 300px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1);"
                        onclick="update_profile_confirmedbyuser('<?php echo $studentData['stdYearId'] ?>')">
                    Confirm Profile
                </button>
            </div>
        </div>
    </div>
    <?php } else { ?>
        <?php if ($studentData['profile_confirmed'] == 'Yes') { ?>
        <div class="profile-alert confirmed">
            <div class="alert-content">
                <div class="alert-text">
                    <i class="fa fa-check-circle me-2"></i>
                    <?php if ($studentData['profile_confirmed_date']) { ?>
                        You confirmed your profile information on <?php echo date('d-M-Y', strtotime($studentData['profile_confirmed_date'])) ?>
                    <?php } else { ?>
                        You have confirmed your profile information
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php } else { ?>
        <div class="profile-alert locked">
            <div class="alert-content">
                <div class="alert-text">
                    <i class="fa fa-lock me-2"></i>
                    <?php if ($studentData['profile_status_changed_date']) { ?>
                        Profile locked on <?php echo date('d-M-Y', strtotime($studentData['profile_status_changed_date'])) ?>
                    <?php } else { ?>
                        Profile has been locked to prevent further edits
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php } ?>
    <?php } ?>

    <!-- Student Profile Photo Section -->
    <!-- <div class="profile-photo-section">
        <div style="position: relative; display: inline-block;">
            <img src="<?php // echo base_url('assets/img/icons/profile.png'); ?>"
                 alt="Student Photo"
                 class="profile-photo"
                 id="student-photo">
            <div class="camera-icon">
                <i class="fa fa-camera"></i>
            </div>
        </div>
       
        <div class="profile-class" id="student-class">Grade 2-A</div>

    </div> -->

    <!-- Dynamic Profile Sections -->
    <div id="dynamic-profile-sections">
        <?php
        $headerName = '';
        foreach ($displayList as $list => $value) {
                switch ($list) {
                    case 'student_info':
                        $headerName = 'Student Profile';
                        break;
                    case 'father_info':
                         $headerName = 'Father Profile';
                        break;
                    case 'mother_info':
                         $headerName = 'Mother Profile';
                        break;
                    case 'guardian_info':
                        $headerName = 'Guardian Profile';
                        break;
                    case 'family_info':
                        $headerName = 'Family Profile';
                        break;
                    case 'Electives':
                        $headerName = 'Electives';
                        break;
                    default:
                        $headerName = 'Profile';
                        break;
                }
            ?>
            <div class="profile-section" data-section="<?php echo $list; ?>">
                <div class="section-header"><?php echo $headerName ?></div>
                <div class="section-content">
                    <?php
                    foreach ($value as $val) {
                        if ($val['data_input'] == 'file') { ?>
                            <div class="student-photo-section">

                                <?php if ($list === 'student_info'): ?>
                                        <!-- Old Student UI -->
                                <div style="position: relative; display: inline-block;">
                                    <img src="<?php echo base_url('assets/img/icons/profile.png'); ?>" alt="<?php echo $list; ?> Photo"
                                        class="student-photo"
                                        data-column="<?php echo $val['table_column_name']; ?>"
                                        data-field="<?php echo $val['column_name']; ?>" data-header_list="<?php echo $list; ?>">
                                    <div class="camera-icon">
                                        <i class="fa fa-camera"></i>
                                    </div>
                                </div>

                                    <?php else: ?>
                                        <!-- New Card UI for others -->
                                        <div class="parent-photo-section">
                                            <div class="photo-card">
                                                <img src="<?php echo base_url('assets/img/icons/profile.png'); ?>" 
                                                    alt="<?php echo $list; ?> Photo"
                                                    class="parent-photo"
                                                    data-column="<?php echo $val['table_column_name']; ?>"
                                                    data-field="<?php echo $val['column_name']; ?>" 
                                                    data-header_list="<?php echo $list; ?>">

                                                <div class="photo-actions">
                                                    <button class="action-btn">
                                                        <i class="fa fa-trash"></i> Delete Photo
                                                    </button>
                                                    <button class="action-btn">
                                                        <i class="fa fa-refresh"></i> Change Photo
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                <?php
                                $names = '';
                                $ClassSection = '';
                                $contactInfo = '';

                                switch ($list) {
                                    case 'student_info':
                                        $names = '<div class="profile-name" data-field="STUDENT_NAME" data-header_list="' . $list . '">Loading...</div>';
                                        $ClassSection = '<div class="profile-class" data-field="CLASS_SECTION" data-header_list="' . $list . '">Loading...</div>';
                                        break;
                                    case 'father_info':
                                        $names = '<div class="parent-name" data-field="FATHER_NAME" data-header_list="' . $list . '">Loading...</div>';
                                        break;
                                    case 'mother_info':
                                        $names = '<div class="parent-name" data-field="MOTHER_NAME" data-header_list="' . $list . '">Loading...</div>';
                                        break;
                                    case 'guardian_info':
                                        $names = '<div class="parent-name" data-field="GUARDIAN_NAME" data-header_list="' . $list . '">Loading...</div>';
                                        break;
                                    case 'family_info':
                                        $names = '<div class="parent-name">Family Information</div>';
                                        break;
                                    case 'Electives':
                                        $names = '<div class="parent-name">Student Electives</div>';
                                        break;
                                    default:
                                        $names = '<div class="parent-name">Not Available</div>';
                                        break;
                                }
                                ?>
                                <?php echo $names; ?>
                                <?php echo $ClassSection; ?>

                            </div>
                        <?php } else { ?>
                                <div class="detail-item"
                                    data-field="<?php echo $val['column_name']; ?>"
                                    data-table-column="<?php echo $val['table_column_name']; ?>" data-header_list="<?php echo $list; ?>">
                                    <span class="detail-label"><?php echo $val['display_name']; ?></span>
                                    <div class="detail-value-container">
                                        <span class="detail-value"
                                            data-column="<?php echo $val['table_column_name']; ?>">
                                            Loading...
                                        </span>
                                        <?php if ($studentData['profile_status'] == 'Unlock') { ?>
                                        <button class="edit-btn" data-field="<?php echo $val['column_name']; ?>" title="Edit <?php echo $val['display_name']; ?>">
                                            <?php $this->load->view('svg_icons/edit_profile.svg'); ?>
                                        </button>
                                        <?php } ?>
                                    </div>
                                </div>
                        <?php }
                    }
                    ?>
                </div>
            </div>
        <?php } ?>
    </div>

</div>

<script type="text/javascript">
    let student_id = '<?php echo $student_id ?>';
    // loadStudentProfilcolumns();

    loadStudentProfileData();
   
    function loadStudentProfileData(){
        var listDataInput = [];

        // Collect field data from detail items with header_list
        $('.detail-item').each(function () {
            var field = $(this).data('field');
            var tableColumn = $(this).data('table-column');
            var header_list = $(this).data('header_list');

            if (field && tableColumn && header_list) {
                listDataInput.push({
                    field: field,
                    table_column: tableColumn,
                    header_list: header_list
                });
            }
        });

        // Also collect photo fields with header_list
        $('img[data-field]').each(function () {
            var field = $(this).data('field');
            var tableColumn = $(this).data('column');
            var header_list = $(this).data('header_list');

            if (field && tableColumn && header_list) {
                listDataInput.push({
                    field: field,
                    table_column: tableColumn,
                    header_list: header_list
                });
            }
        });
        
        console.log('Sending data to server:', listDataInput);

        $.ajax({
            url: '<?php echo site_url('parent/parent_dashboard_controller/get_student_profile_data'); ?>',
            type: 'post',
            data: {'student_id' : student_id,'listDataInput':listDataInput},
            dataType: 'json',
            beforeSend: function() {
                showLoadingState();
            },
            success: function(response) {
                console.log('Server response:', response);
                if (response.error) {
                    console.error('Server error:', response.error);
                    showErrorState('Error: ' + response.error);
                } else {
                    construct_data(response);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                console.error('Response text:', xhr.responseText);
                showErrorState('Error loading profile data: ' + error);
            },
            complete: function() {
                hideLoadingState();
            }
        });
    }

    function showLoadingState() {
        $('#student-name').text('Loading...');
        $('#student-class').text('Loading...');
        $('#father-name').text('Loading...');
        $('#father-contact').text('Loading...');
        $('#mother-name').text('Loading...');
        $('#mother-contact').text('Loading...');
    }

    function hideLoadingState() {
        // Loading state will be replaced by actual data
    }

    function showErrorState(message) {
        $('#student-name').text('Error loading data');
        $('#student-class').text('');
        $('#father-name').text('Error');
        $('#father-contact').text('');
        $('#mother-name').text('Error');
        $('#mother-contact').text('');
        console.error(message);
    }

    function construct_data(response){
        try {
            console.log('Constructing data with response:', response);

            if (!response) {
                console.error('No response data received');
                $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">No data received from server.</div>');
                return;
            }

            if (response.error) {
                console.error('Server error:', response.error);
                $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">Error: ' + response.error + '</div>');
                return;
            }

            // Update profile fields with actual data
            updateProfileFields(response);

        } catch (error) {
            console.error('Error in construct_data:', error);
            $('.profile-section').html('<div style="text-align: center; padding: 2rem; color: #dc3545;">Error processing data.</div>');
        }
    }

    function updateProfileFields(data) {
        console.log('Updating profile fields with data:', data);

        // Update student profile photo and name
        if (data.STUDENT_PHOTO) {
            $('#student-photo').attr('src', data.STUDENT_PHOTO);
        }
        if (data.STUDENT_NAME) {
            $('#student-name').text(data.STUDENT_NAME);
            // Also update any dynamic student name fields
            $('[data-field="STUDENT_NAME"]').text(data.STUDENT_NAME);
        }

        // Handle CLASS_SECTION field (combination of CLASS_NAME and SECTION_NAME)
        var classSection = '';
        if (data.CLASS_NAME && data.SECTION_NAME) {
            classSection = 'Grade ' + data.CLASS_NAME + '-' + data.SECTION_NAME;
        } else if (data.CLASS_NAME) {
            classSection = 'Grade ' + data.CLASS_NAME;
        }

        if (classSection) {
            $('#student-class').text(classSection);
            $('[data-field="CLASS_SECTION"]').text(classSection);
        }

        // Update all detail items with data
        $('.detail-item[data-field]').each(function() {
            var $item = $(this);
            var fieldName = $item.data('field');
            var $valueSpan = $item.find('.detail-value');

            // Get the value from response data
            var value = getFieldValue(data, fieldName);

            if (value !== null && value !== undefined && value !== '') {
                $valueSpan.text(value);
                $item.show();
            } else {
                $valueSpan.text('-');
            }
        });

        // Update photo fields
        $('img[data-field]').each(function() {
            var $img = $(this);
            var fieldName = $img.data('field');
            var photoUrl = getFieldValue(data, fieldName);

            if (photoUrl && photoUrl !== '' && photoUrl !== '-') {
                $img.attr('src', photoUrl);
            } else {
                // Set default photo
                $img.attr('src', '<?php echo base_url('assets/img/icons/profile.png'); ?>');
            }
        });

        // Update name fields in parent sections
        $('.parent-name[data-field], .profile-class[data-field]').each(function() {
            var $name = $(this);
            var fieldName = $name.data('field');
            var nameValue = getFieldValue(data, fieldName);

            if (nameValue && nameValue !== '' && nameValue !== '-') {
                $name.text(nameValue);
            } else {
                $name.text('Not Available');
            }
        });

        // Update contact information in parent sections
        $('.parent-contact[data-field]').each(function() {
            var $contact = $(this);
            var fieldName = $contact.data('field');
            var contactValue = getFieldValue(data, fieldName);

            if (contactValue && contactValue !== '' && contactValue !== '-') {
                $contact.text(contactValue);
            } else {
                $contact.text('Not Available');
            }
        });
    }

    function updatePersonalDetails(data) {
        var personalDetailsHtml = '';

        // Common personal details fields
        var personalFields = [
            { key: 'ADMISSION_NO', label: 'Admission No' },
            { key: 'ROLL_NO', label: 'Roll No' },
            { key: 'DATE_OF_BIRTH', label: 'Date of Birth' },
            { key: 'GENDER', label: 'Gender' },
            { key: 'BLOOD_GROUP', label: 'Blood Group' },
            { key: 'RELIGION', label: 'Religion' },
            { key: 'CASTE', label: 'Caste' },
            { key: 'CATEGORY', label: 'Category' },
            { key: 'MOTHER_TONGUE', label: 'Mother Tongue' },
            { key: 'NATIONALITY', label: 'Nationality' }
        ];

        personalFields.forEach(function(field) {
            if (data[field.key]) {
                personalDetailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">${field.label}</span>
                        <span class="detail-value">${data[field.key]}</span>
                    </div>
                `;
            }
        });

        $('#personal-details').html(personalDetailsHtml);
    }

    function updateFatherDetails(data) {
        var fatherDetailsHtml = '';

        var fatherFields = [
            { key: 'FATHER_EMAIL', label: 'Email' },
            { key: 'FATHER_OCCUPATION', label: 'Occupation' },
            { key: 'FATHER_QUALIFICATION', label: 'Qualification' },
            { key: 'FATHER_OFFICE_ADDRESS', label: 'Office Address' },
            { key: 'FATHER_ANNUAL_INCOME', label: 'Annual Income' }
        ];

        fatherFields.forEach(function(field) {
            if (data[field.key]) {
                fatherDetailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">${field.label}</span>
                        <span class="detail-value">${data[field.key]}</span>
                    </div>
                `;
            }
        });

        $('#father-details').html(fatherDetailsHtml);
    }

    function updateMotherDetails(data) {
        var motherDetailsHtml = '';

        var motherFields = [
            { key: 'MOTHER_EMAIL', label: 'Email' },
            { key: 'MOTHER_OCCUPATION', label: 'Occupation' },
            { key: 'MOTHER_QUALIFICATION', label: 'Qualification' },
            { key: 'MOTHER_OFFICE_ADDRESS', label: 'Office Address' },
            { key: 'MOTHER_ANNUAL_INCOME', label: 'Annual Income' }
        ];

        motherFields.forEach(function(field) {
            if (data[field.key]) {
                motherDetailsHtml += `
                    <div class="detail-item">
                        <span class="detail-label">${field.label}</span>
                        <span class="detail-value">${data[field.key]}</span>
                    </div>
                `;
            }
        });

        $('#mother-details').html(motherDetailsHtml);
    }

    function getFieldValue(data, fieldName) {
        // Since we're using the column names directly from the configuration,
        // the response should have the field names as keys
        return data[fieldName] || null;
    }

    function loadStudentProfilcolumns(){
        // Parse the PHP data properly
        var parent_columns = <?php echo json_encode($parent_columns); ?>;
        var display_columns = <?php echo json_encode($display_columns); ?>;

        // Group columns by tabs
        var groupedColumns = {
            'student_info': [],
            'father_info': [],
            'mother_info': [],
            'guardian_info': [],
            'family_info': [],
            'Electives': []
        };

        // Group parent columns by tabs and filter by display_columns
        parent_columns.forEach(function(column) {
            if (display_columns.includes(column.column_name)) {
                if (groupedColumns[column.tabs]) {
                    groupedColumns[column.tabs].push(column);
                }
            }
        });

        var html = '';
        console.log('Grouped columns:', groupedColumns);

        // Generate Student Profile Section
        if (groupedColumns.student_info.length > 0) {
            console.log('Generating student profile section with', groupedColumns.student_info.length, 'columns');
            html += generateProfileSection('Student Profile', 'fa-user', groupedColumns.student_info, 'student');
        }

        // Generate Father Profile Section
        if (groupedColumns.father_info.length > 0) {
            html += generateProfileSection('Father Profile', 'fa-male', groupedColumns.father_info, 'father');
        }

        // Generate Mother Profile Section
        if (groupedColumns.mother_info.length > 0) {
            html += generateProfileSection('Mother Profile', 'fa-female', groupedColumns.mother_info, 'mother');
        }

        // Generate Guardian Profile Section
        if (groupedColumns.guardian_info.length > 0) {
            html += generateProfileSection('Guardian Profile', 'fa-users', groupedColumns.guardian_info, 'guardian');
        }

        // Generate Family Profile Section
        if (groupedColumns.family_info.length > 0) {
            html += generateProfileSection('Family Profile', 'fa-home', groupedColumns.family_info, 'family');
        }

        // Generate Electives Section
        if (groupedColumns.Electives.length > 0) {
            html += generateProfileSection('Electives', 'fa-book', groupedColumns.Electives, 'electives');
        }

        // Replace the existing profile section with dynamic content
        console.log('Generated HTML length:', html.length);
        console.log('Generated HTML preview:', html.substring(0, 500));

        if (html.trim() !== '') {
            $('#dynamic-profile-sections').html(html);
            console.log('HTML inserted into #dynamic-profile-sections');
        } else {
            $('#dynamic-profile-sections').html('<div style="text-align: center; padding: 2rem; color: #666;">No profile fields configured to display.</div>');
            console.log('No HTML generated, showing default message');
        }

    }

    function generateProfileSection(title, icon, columns, type) {
        var html = `
            <div class="section-header">
                <h4><i class="fa ${icon} me-2"></i>${title}</h4>
            </div>`;

        // Add photo section for profiles that have photos
        var photoColumn = columns.find(col => col.data_input === 'file');
        if (photoColumn) {
            html += `
            <div class="section-content">
                <div class="student-header">
                    <div class="student-photo">
                        <img src="" alt="${title} Photo" class="student-avatar" data-field="${photoColumn.column_name}">
                    </div>
                    <div class="student-info">
                        <h3 class="student-name" data-field="${type.toUpperCase()}_NAME"></h3>
                    </div>
                </div>
            </div>`;
        }

        // Add profile details
        html += '<div class="profile-details">';

        columns.forEach(function(column) {
            // Skip photo columns and name columns as they're handled above
            var isNameField = column.column_name.endsWith('_NAME') ||
                             column.column_name === 'STUDENT_NAME' ||
                             column.column_name === 'FATHER_NAME' ||
                             column.column_name === 'MOTHER_NAME' ||
                             column.column_name === 'GUARDIAN_NAME';

            if (column.data_input !== 'file' && !isNameField) {
                var editButtonHtml = '';
                // Always show edit button for testing - remove this condition later
                editButtonHtml = `<button class="edit-btn" data-field="${column.column_name}" title="Edit ${column.display_name}">
                    <i class="fa fa-pencil"></i>
                </button>`;

                console.log('Adding detail item for:', column.column_name, 'with edit button:', editButtonHtml);

                html += `
                    <div class="detail-item" data-field="${column.column_name}">
                        <span class="detail-label">${column.display_name}</span>
                        <div class="detail-value-container">
                            <span class="detail-value" data-column="${column.table_column_name}">Loading...</span>
                            ${editButtonHtml}
                        </div>
                    </div>`;
            }
        });

        html += '</div>';

        return html;
    }

    function update_profile_confirmedbyuser(stdYearId) {
  Swal.fire({
    title: "Confirm Profile",
    text: "Are you sure that the profile information is correct?",
    icon: "question",
    showCancelButton: true,
    confirmButtonText: 'Yes, Confirm',
    cancelButtonText: 'Cancel',
    reverseButtons: true
  }).then((result) => {
    if (result.isConfirmed) {
      $.ajax({
        url: '<?php echo site_url('parent/parent_dashboard_controller/update_profile_confirmed'); ?>',
        type: 'post',
        data: { 'stdYearId': stdYearId },
        success: function (data) {
          var response = JSON.parse(data);
          if (response == 1) {
            Swal.fire({
              icon: 'success',
              title: 'Confirmed!',
              showConfirmButton: false,
              timer: 1000
            }).then(() => {
              location.reload();
            });
          } else {
            Swal.close();
            mandatory_fields_display_in_popup(response);
          }
        },
        error: function () {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Something went wrong. Please try again.',
            customClass: {
              popup: 'modern-modal'
            }
          });
        }
      });
    }
  });
}

  function mandatory_fields_display_in_popup(response) {
    var html = '<div class="missing-fields-container">';
    html += '<div class="missing-fields-header">';
    html += '<i class="fa fa-exclamation-triangle text-warning me-2"></i>';
    html += '<h6 class="mb-3">The following fields need to be completed:</h6>';
    html += '</div>';
    html += '<div class="missing-fields-list">';

    for (var i = 0; i < response.length; i++) {
      var field = response[i].replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      html += '<div class="missing-field-item">';
      html += '<span class="field-number">' + (i + 1) + '</span>';
      html += '<span class="field-name">' + field + '</span>';
      html += '</div>';
    }

    html += '</div>';
    html += '<div class="missing-fields-footer">';
    html += '<p class="mb-0"><i class="fa fa-info-circle me-1"></i> Please complete these fields and try confirming again.</p>';
    html += '</div>';
    html += '</div>';

    bootbox.alert({
      title: 'Missing Information',
      message: html,
      className: 'modern-modal missing-fields-modal',
      backdrop: true
    });
  }

  function generate_enquiry_referal_link(student_id) {
    // Show loading state
    $('.referral-button').html('<i class="fa fa-spinner fa-spin me-1"></i> Generating...');
    $('.referral-button').prop('disabled', true);

    $.ajax({
        url: '<?php echo site_url('parent_controller/get_referal_link'); ?>',
        type: 'post',
        data: { 'student_id': student_id },
        success: function(data) {
            var response = JSON.parse(data);
            if (response) {
                bootbox.alert({
                    title: '<i class="fa fa-link me-2"></i>Your Referral Link',
                    message: `
                        <div class="referral-link-container">
                            <div class="referral-link-text">
                                <input type="text" id="referralLinkInput" value="${response}" readonly class="form-control mb-3">
                            </div>
                            <div class="referral-link-actions">
                                <button class="btn btn-primary" onclick="copyReferralLink('${response}', this)">
                                    <i class="fa fa-copy me-1"></i> Copy Link
                                </button>
                            </div>
                        </div>
                    `,
                    className: 'modern-modal referral-modal',
                    backdrop: true
                });
            }
            // Reset button state
            $('.referral-button').html('<i class="fa fa-link me-1"></i> Get URL');
            $('.referral-button').prop('disabled', false);
        },
        error: function() {
            $('.referral-button').html('<i class="fa fa-link me-1"></i> Get URL');
            $('.referral-button').prop('disabled', false);
            bootbox.alert({
                title: 'Error',
                message: 'Unable to generate referral link. Please try again.',
                className: 'modern-modal'
            });
        }
    });
  }

  // Enhanced copy function for referral link
  function copyReferralLink(text, button) {
      navigator.clipboard.writeText(text).then(() => {
          $(button).html('<i class="fa fa-check me-1"></i> Copied!');
          $(button).removeClass('btn-primary').addClass('btn-success');

          setTimeout(() => {
              $(button).html('<i class="fa fa-copy me-1"></i> Copy Link');
              $(button).removeClass('btn-success').addClass('btn-primary');
          }, 2000);
      }).catch(err => {
          console.error('Could not copy text: ', err);
          // Fallback for older browsers
          $('#referralLinkInput').select();
          document.execCommand('copy');
          $(button).html('<i class="fa fa-check me-1"></i> Copied!');
          $(button).removeClass('btn-primary').addClass('btn-success');

          setTimeout(() => {
              $(button).html('<i class="fa fa-copy me-1"></i> Copy Link');
              $(button).removeClass('btn-success').addClass('btn-primary');
          }, 2000);
      });
  }

  // Dynamic profile data loading function
  function loadDynamicProfileData(studentId) {
      if (!studentId) {
          console.error('Student ID is required');
          return;
      }

      $.ajax({
          url: '<?php echo site_url('parent/parent_dashboard_controller/getStudentDetailsById'); ?>',
          type: 'POST',
          data: { 'student_id': studentId },
          dataType: 'json',
          beforeSend: function() {
              // Show loading state
              $('.profile-section').addClass('loading');
          },
          success: function(response) {
              if (response.error) {
                  console.error('Error loading profile data:', response.error);
                  return;
              }

              // Update profile sections dynamically
              updateProfileSections(response);

              console.log('Profile data loaded successfully:', response);
          },
          error: function(xhr, status, error) {
              console.error('AJAX error loading profile data:', error);
          },
          complete: function() {
              // Remove loading state
              $('.profile-section').removeClass('loading');
          }
      });
  }

  // Function to update profile sections with dynamic data
  function updateProfileSections(data) {
      const studentData = data.student_data;
      const enabledFields = data.enabled_fields;
      const fieldLabels = data.field_labels;

      // Update student name if enabled
      if (enabledFields.includes('STUDENT_NAME') && studentData.STUDENT_NAME) {
          $('.student-name').text(studentData.STUDENT_NAME);
      }

      // Update profile photo if enabled
      if (enabledFields.includes('STUDENT_PHOTO') && studentData.STUDENT_PHOTO) {
          $('.student-avatar').attr('src', studentData.STUDENT_PHOTO);
      }

      // Update other fields dynamically
      enabledFields.forEach(function(field) {
          // Skip name fields as they're handled in the header sections
          var isNameField = field.endsWith('_NAME') ||
                           field === 'STUDENT_NAME' ||
                           field === 'FATHER_NAME' ||
                           field === 'MOTHER_NAME' ||
                           field === 'GUARDIAN_NAME';

          if (studentData[field] && fieldLabels[field] && !isNameField) {
              const value = studentData[field];
              const label = fieldLabels[field];

              // Find or create detail item for this field
              let detailItem = $(`.detail-item[data-field="${field}"]`);
              if (detailItem.length === 0) {
                  var editButtonHtml = '';
                  // Always show edit button for testing - remove this condition later
                  editButtonHtml = `<button class="edit-btn" data-field="${field}" title="Edit ${label}">
                      <?php $this->load->view('svg_icons/edit_profile.svg'); ?>
                  </button>`;

                  detailItem = $(`
                      <div class="detail-item" data-field="${field}">
                          <span class="detail-label">${label}</span>
                          <div class="detail-value-container">
                              <span class="detail-value">${value}</span>
                              ${editButtonHtml}
                          </div>
                      </div>
                  `);
                  $('.profile-details').append(detailItem);
              } else {
                  detailItem.find('.detail-value').text(value);
              }
          }
      });
  }

  // Initialize dynamic loading on page load
  $(document).ready(function() {
      // Check if we have dynamic configuration
      <?php if (isset($profileDisplayConfig) && !empty($profileDisplayConfig['enabled_fields'])) { ?>
      console.log('Dynamic profile configuration detected');
      console.log('Enabled fields:', <?php echo json_encode($profileDisplayConfig['enabled_fields']); ?>);

      // Optionally reload data dynamically
      // loadDynamicProfileData(<?php echo $student_id; ?>);
      <?php } else { ?>
      console.log('Using static profile configuration');
      <?php } ?>

      // Add click event handler for edit buttons
      $(document).on('click', '.edit-btn', function(e) {
          e.preventDefault();
          e.stopPropagation();

          // Check if profile is unlocked
          <?php if ($studentData['profile_status'] != 'Unlock') { ?>
              alert('Profile editing is currently locked. Please contact the administrator.');
              return;
          <?php } ?>

          var fieldName = $(this).data('field');
          var $detailItem = $(this).closest('.detail-item');
          var $detailValue = $detailItem.find('.detail-value');
          var currentValue = $detailValue.text();
          var fieldLabel = $detailItem.find('.detail-label').text();

          console.log('Edit button clicked for field:', fieldName, 'Current value:', currentValue);

          // Show appropriate modal based on field type
          if (fieldName.includes('ADDRESS')) {
              showAddressEditModal(fieldName, fieldLabel, currentValue);
          } else {
              showFieldEditModal(fieldName, fieldLabel, currentValue);
          }
      });
  });

  // Function to show field edit modal
  function showFieldEditModal(fieldName, fieldLabel, currentValue) {
      $('#editModalTitle').text('Edit ' + fieldLabel);
      $('#editFieldLabel').text(fieldLabel);
      $('#editFieldName').val(fieldName);

      // Generate appropriate input based on field type
      var inputHtml = generateFieldInput(fieldName, currentValue);
      $('#editFieldInputContainer').html(inputHtml);

      // Show the modal
      $('#editBottomSheet').addClass('show');
      $('body').addClass('modal-open');

      // Focus on the input
      setTimeout(function() {
          $('#editFieldInput').focus();
      }, 300);
  }

  // Generate field input based on field type
  function generateFieldInput(fieldName, currentValue) {
      var inputHtml = '';

      switch(fieldName) {
          // Student Blood Group
          case 'STUDENT_BLOOD_GROUP':
              inputHtml = `
                  <select class="form-control" id="editFieldInput">
                      <option value="">Select Blood Group</option>
                      <option value="A +ve" ${currentValue === 'A +ve' ? 'selected' : ''}>A +ve</option>
                      <option value="B +ve" ${currentValue === 'B +ve' ? 'selected' : ''}>B +ve</option>
                      <option value="AB +ve" ${currentValue === 'AB +ve' ? 'selected' : ''}>AB +ve</option>
                      <option value="O +ve" ${currentValue === 'O +ve' ? 'selected' : ''}>O +ve</option>
                      <option value="A -ve" ${currentValue === 'A -ve' ? 'selected' : ''}>A -ve</option>
                      <option value="B -ve" ${currentValue === 'B -ve' ? 'selected' : ''}>B -ve</option>
                      <option value="AB -ve" ${currentValue === 'AB -ve' ? 'selected' : ''}>AB -ve</option>
                      <option value="O -ve" ${currentValue === 'O -ve' ? 'selected' : ''}>O -ve</option>
                  </select>
              `;
              break;

          // Student Gender
          case 'STUDENT_GENDER':
              inputHtml = `
                  <select class="form-control" id="editFieldInput">
                      <option value="">Select Gender</option>
                      <option value="M" ${currentValue === 'M' || currentValue === 'Male' ? 'selected' : ''}>Male</option>
                      <option value="F" ${currentValue === 'F' || currentValue === 'Female' ? 'selected' : ''}>Female</option>
                  </select>
              `;
              break;

          // Point of Contact
          case 'POINT_OF_CONTACT':
              inputHtml = `
                  <select class="form-control" id="editFieldInput">
                      <option value="">Select Point of Contact</option>
                      <option value="Father" ${currentValue === 'Father' ? 'selected' : ''}>Father</option>
                      <option value="Mother" ${currentValue === 'Mother' ? 'selected' : ''}>Mother</option>
                      <option value="Guardian" ${currentValue === 'Guardian' ? 'selected' : ''}>Guardian</option>
                  </select>
              `;
              break;

          // Pickup Mode
          case 'STUDENT_PICKUP_MODE':
              inputHtml = `
                  <select class="form-control" id="editFieldInput">
                      <option value="">Select Mode</option>
                      <option value="PICKING" ${currentValue === 'PICKING' ? 'selected' : ''}>PICKING</option>
                      <option value="DROPPING" ${currentValue === 'DROPPING' ? 'selected' : ''}>DROPPING</option>
                      <option value="BOTH" ${currentValue === 'BOTH' ? 'selected' : ''}>BOTH</option>
                  </select>
              `;
              break;

          // Email Fields
          case 'STUDENT_EMAIL':
          case 'FATHER_EMAIL':
          case 'MOTHER_EMAIL':
          case 'GUARDIAN_EMAIL':
              inputHtml = `<input type="email" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter email address">`;
              break;

          // Date Fields
          case 'STUDENT_DOB':
              var dateValue = currentValue ? formatDateForInput(currentValue) : '';
              inputHtml = `<input type="date" class="form-control" id="editFieldInput" value="${dateValue}">`;
              break;

          // Mobile Number Fields
          case 'STUDENT_MOBILE_NUMBER':
          case 'FATHER_CONTACT_NO':
          case 'MOTHER_CONTACT_NO':
          case 'GUARDIAN_CONTACT_NO':
          case 'PREFFERED_CONTACT_NUMBER':
              inputHtml = `<input type="tel" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter mobile number" maxlength="15" pattern="[0-9 -()+]+">`;
              break;

          // Aadhar Number Fields
          case 'STUDENT_AADHAR':
          case 'FATHER_AADHAR':
          case 'MOTHER_AADHAR':
              inputHtml = `<input type="number" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter 12-digit Aadhar number" maxlength="12">`;
              break;

          // Annual Income Fields
          case 'FATHER_ANNUAL_INCOME':
          case 'MOTHER_ANNUAL_INCOME':
              inputHtml = `<input type="number" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter annual income" step="0.01" min="0">`;
              break;

          // Name Fields
          case 'STUDENT_FIRST_NAME':
          case 'STUDENT_LAST_NAME':
          case 'FATHER_FIRST_NAME':
          case 'FATHER_LAST_NAME':
          case 'MOTHER_FIRST_NAME':
          case 'MOTHER_LAST_NAME':
          case 'GUARDIAN_FIRST_NAME':
          case 'GUARDIAN_LAST_NAME':
              inputHtml = `<input type="text" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter name" required>`;
              break;

          // Text Fields
          case 'STUDENT_MOTHER_TONGUE':
          case 'STUDENT_CASTE':
          case 'STUDENT_RELIGION':
          case 'STUDENT_NATIONALITY':
          case 'NAME_AS_PER_AADHAR':
          case 'FATHER_QUALIFICATION':
          case 'FATHER_OCCUPATION':
          case 'FATHER_COMPANY':
          case 'MOTHER_QUALIFICATION':
          case 'MOTHER_OCCUPATION':
          case 'MOTHER_COMPANY':
              inputHtml = `<input type="text" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter ${getFieldPlaceholder(fieldName)}">`;
              break;

          // Default text input
          default:
              inputHtml = `<input type="text" class="form-control" id="editFieldInput" value="${currentValue}" placeholder="Enter ${getFieldPlaceholder(fieldName)}">`;
      }

      return inputHtml;
  }

  // Format date for input field
  function formatDateForInput(dateString) {
      if (!dateString) return '';
      var date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toISOString().split('T')[0];
  }

  // Function to show address edit modal
  function showAddressEditModal(fieldName, fieldLabel, currentValue) {
      $('#addressModalTitle').text('Edit Details');
      $('#addressFieldName').val(fieldName);

      // Parse current address value and populate fields
      // For now, we'll show empty fields - you can enhance this to parse the address
      $('#addressLine1').val('');
      $('#addressLine2').val('');
      $('#addressArea').val('');
      $('#addressCity').val('');
      $('#addressState').val('Karnataka');
      $('#addressPincode').val('');
      $('#addressCountry').val('India');

      // Show the address modal
      $('#addressBottomSheet').addClass('show');
      $('body').addClass('modal-open');
  }

  // Function to close modals
  function closeBottomSheet() {
      $('#editBottomSheet').removeClass('show');
      $('#addressBottomSheet').removeClass('show');
      $('body').removeClass('modal-open');
  }

  // Close modal when clicking backdrop
  $(document).on('click', '.bottom-sheet-backdrop', function() {
      closeBottomSheet();
  });

  // Update field function
  function updateField() {
      var fieldName = $('#editFieldName').val();
      var newValue = $('#editFieldInput').val();
      var fieldLabel = $('#editFieldLabel').text();

      if (!newValue.trim()) {
          alert('Please enter a value');
          return;
      }

      // Get field configuration
      var fieldConfig = getFieldConfiguration(fieldName);
      var oldValue = $('.detail-item[data-field="' + fieldName + '"] .detail-value').text();

      // Validate field based on type
      if (!validateField(fieldConfig.field_name, newValue)) {
          return;
      }

      // Show loading state
      $('#editBottomSheet .btn-primary').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');

      // Make AJAX call to save data
      $.ajax({
          url: '<?php echo site_url('parent_controller/save_profile_data'); ?>',
          type: 'POST',
          data: {
              'type': fieldConfig.type,
              'field_value': newValue,
              'field_name': fieldConfig.field_name,
              'student_id': student_id,
              'father_id': 0,
              'mother_id': 0,
              'guardian_id': 0,
              'old_value': oldValue
          },
          success: function(response) {
              // Update the display value
              $('.detail-item[data-field="' + fieldName + '"] .detail-value').text(newValue);

              closeBottomSheet();

              // Show success message
              showSuccessToast(fieldLabel + ' updated successfully!');
          },
          error: function(xhr, status, error) {
              alert('Error saving data: ' + error);
              $('#editBottomSheet .btn-primary').prop('disabled', false).html('Save');
          }
      });
  }

  // Update address function
  function updateAddress() {
      var fieldName = $('#addressFieldName').val();
      var addressData = {
          line1: $('#addressLine1').val(),
          line2: $('#addressLine2').val(),
          area: $('#addressArea').val(),
          city: $('#addressCity').val(),
          state: $('#addressState').val(),
          pincode: $('#addressPincode').val(),
          country: $('#addressCountry').val()
      };

      // Construct address string
      var addressString = [
          addressData.line1,
          addressData.line2,
          addressData.area,
          addressData.city,
          addressData.state,
          addressData.country,
          addressData.pincode
      ].filter(Boolean).join(', ');

      // Update the display value
      $('.detail-item[data-field="' + fieldName + '"] .detail-value').text(addressString);

      // Here you would typically make an AJAX call to save the data
      // For now, we'll just close the modal
      closeBottomSheet();

      // Show success message
      alert('Address updated successfully!');
  }

  // Get field configuration for database mapping
  function getFieldConfiguration(fieldName) {
      var configurations = {
          // Student fields
          'STUDENT_BLOOD_GROUP': { type: 'student', field_name: 'blood_group' },
          'STUDENT_EMAIL': { type: 'student', field_name: 'email' },
          'STUDENT_GENDER': { type: 'student', field_name: 'gender' },
          'STUDENT_DOB': { type: 'student', field_name: 'dob' },
          'STUDENT_MOBILE_NUMBER': { type: 'student', field_name: 'student_mobile_no' },
          'STUDENT_MOTHER_TONGUE': { type: 'student', field_name: 'mother_tongue' },
          'STUDENT_AADHAR': { type: 'student', field_name: 'aadhar_no' },
          'STUDENT_CASTE': { type: 'student', field_name: 'caste' },
          'STUDENT_RELIGION': { type: 'student', field_name: 'religion' },
          'STUDENT_NATIONALITY': { type: 'student', field_name: 'nationality' },
          'STUDENT_PICKUP_MODE': { type: 'student', field_name: 'pickup_mode' },
          'STUDENT_STOP': { type: 'student', field_name: 'stop' },
          'STUDENT_FIRST_NAME': { type: 'student', field_name: 'first_name' },
          'STUDENT_LAST_NAME': { type: 'student', field_name: 'last_name' },
          'POINT_OF_CONTACT': { type: 'student', field_name: 'point_of_contact' },
          'PREFFERED_CONTACT_NUMBER': { type: 'student', field_name: 'preferred_contact_no' },
          'NAME_AS_PER_AADHAR': { type: 'student', field_name: 'name_as_per_aadhar' },
          'CATEGORY': { type: 'student', field_name: 'category' },

          // Father fields
          'FATHER_EMAIL': { type: 'father', field_name: 'email' },
          'FATHER_CONTACT_NO': { type: 'father', field_name: 'mobile_no' },
          'FATHER_QUALIFICATION': { type: 'father', field_name: 'qualification' },
          'FATHER_OCCUPATION': { type: 'father', field_name: 'occupation' },
          'FATHER_COMPANY': { type: 'father', field_name: 'company' },
          'FATHER_ANNUAL_INCOME': { type: 'father', field_name: 'annual_income' },
          'FATHER_AADHAR': { type: 'father', field_name: 'aadhar_no' },
          'FATHER_FIRST_NAME': { type: 'father', field_name: 'first_name' },
          'FATHER_LAST_NAME': { type: 'father', field_name: 'last_name' },

          // Mother fields
          'MOTHER_EMAIL': { type: 'mother', field_name: 'email' },
          'MOTHER_CONTACT_NO': { type: 'mother', field_name: 'mobile_no' },
          'MOTHER_QUALIFICATION': { type: 'mother', field_name: 'qualification' },
          'MOTHER_OCCUPATION': { type: 'mother', field_name: 'occupation' },
          'MOTHER_COMPANY': { type: 'mother', field_name: 'company' },
          'MOTHER_ANNUAL_INCOME': { type: 'mother', field_name: 'annual_income' },
          'MOTHER_AADHAR': { type: 'mother', field_name: 'aadhar_no' },
          'MOTHER_FIRST_NAME': { type: 'mother', field_name: 'first_name' },
          'MOTHER_LAST_NAME': { type: 'mother', field_name: 'last_name' },

          // Guardian fields
          'GUARDIAN_EMAIL': { type: 'guardian', field_name: 'email' },
          'GUARDIAN_CONTACT_NO': { type: 'guardian', field_name: 'mobile_no' },
          'GUARDIAN_FIRST_NAME': { type: 'guardian', field_name: 'first_name' },
          'GUARDIAN_LAST_NAME': { type: 'guardian', field_name: 'last_name' }
      };

      return configurations[fieldName] || { type: 'student', field_name: fieldName.toLowerCase() };
  }

  // Get field placeholder text
  function getFieldPlaceholder(fieldName) {
      var placeholders = {
          'STUDENT_MOTHER_TONGUE': 'mother tongue',
          'STUDENT_CASTE': 'caste',
          'STUDENT_RELIGION': 'religion',
          'STUDENT_NATIONALITY': 'nationality',
          'NAME_AS_PER_AADHAR': 'name as per Aadhar',
          'FATHER_QUALIFICATION': 'qualification',
          'FATHER_OCCUPATION': 'occupation',
          'FATHER_COMPANY': 'company name',
          'MOTHER_QUALIFICATION': 'qualification',
          'MOTHER_OCCUPATION': 'occupation',
          'MOTHER_COMPANY': 'company name'
      };

      return placeholders[fieldName] || fieldName.toLowerCase().replace(/_/g, ' ');
  }

  // Validate field based on type
  function validateField(fieldName, value) {
      // Empty value check
      if (!value || value.trim() === '') {
          alert('Please enter a value.');
          return false;
      }

      // Mobile number validation
      if (fieldName === 'mobile_no' || fieldName === 'preferred_contact_no' || fieldName === 'student_mobile_no') {
          let pattern = /^[0-9 -()+]+$/;
          if (!pattern.test(value)) {
              alert('Please enter a valid mobile number.');
              return false;
          }
          if (value.length < 8 || value.length > 15) {
              alert('Mobile number must be between 8 and 15 characters.');
              return false;
          }
      }

      // Email validation
      if (fieldName === 'email') {
          let emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailPattern.test(value)) {
              alert('Please enter a valid email address.');
              return false;
          }
      }

      // Aadhar validation
      if (fieldName === 'aadhar_no') {
          let aadharPattern = /^\d{12}$/;
          if (!aadharPattern.test(value)) {
              alert('Please enter a valid 12-digit Aadhar number.');
              return false;
          }
      }

      // Annual income validation
      if (fieldName === 'annual_income') {
          let incomePattern = /^[0-9]\d*(\.\d+)?$/;
          if (!incomePattern.test(value)) {
              alert('Please enter a valid currency value.');
              return false;
          }
      }

      // Name validation (no special characters except spaces, hyphens, apostrophes)
      if (fieldName === 'first_name' || fieldName === 'last_name') {
          let namePattern = /^[a-zA-Z\s\-']+$/;
          if (!namePattern.test(value)) {
              alert('Please enter a valid name (letters, spaces, hyphens, and apostrophes only).');
              return false;
          }
          if (value.length < 2) {
              alert('Name must be at least 2 characters long.');
              return false;
          }
      }

      // Date validation
      if (fieldName === 'dob') {
          let dateValue = new Date(value);
          let today = new Date();
          if (dateValue >= today) {
              alert('Date of birth must be in the past.');
              return false;
          }
          // Check if age is reasonable (between 3 and 25 for students)
          let age = today.getFullYear() - dateValue.getFullYear();
          if (age < 3 || age > 25) {
              alert('Please enter a valid date of birth.');
              return false;
          }
      }

      return true;
  }

  // Show success toast
  function showSuccessToast(message) {
      var toast = $(`
          <div class="success-toast">
              <i class="fa fa-check-circle"></i> ${message}
          </div>
      `);

      $('body').append(toast);

      setTimeout(function() {
          toast.fadeOut(function() {
              $(this).remove();
          });
      }, 3000);
  }

</script>

<!-- Bottom Sheet Modals -->
<!-- Simple Field Edit Modal -->
<div id="editBottomSheet" class="bottom-sheet">
    <div class="bottom-sheet-backdrop"></div>
    <div class="bottom-sheet-content">
        <div class="bottom-sheet-header">
            <div class="bottom-sheet-handle"></div>
        </div>
        <div class="bottom-sheet-body">
            <h5 id="editModalTitle">Edit Details</h5>
            <div class="form-group">
                <label id="editFieldLabel">Field</label>
                <div id="editFieldInputContainer">
                    <!-- Dynamic input will be generated here -->
                </div>
                <input type="hidden" id="editFieldName">
            </div>
            <div>
                <button type="button" class="btn btn-primary btn-block" onclick="updateField()">Update</button>
            </div>    
        </div>
    </div>
</div>

<!-- Address Edit Modal -->
<div id="addressBottomSheet" class="bottom-sheet">
    <div class="bottom-sheet-backdrop"></div>
    <div class="bottom-sheet-content">
        <div class="bottom-sheet-header">
            <div class="bottom-sheet-handle"></div>
        </div>
        <div class="bottom-sheet-body">
            <h5 id="addressModalTitle">Edit Details</h5>
            <div class="form-group">
                <label>Home Address</label>
                <input type="text" class="form-control mb-2" id="addressLine1" placeholder="86, YV Armaiah Rd">
                <input type="text" class="form-control mb-2" id="addressLine2" placeholder="Jayanahalli">
                <input type="text" class="form-control mb-2" id="addressArea" placeholder="Yalachenahalli, Naidu Layout">
                <input type="text" class="form-control mb-2" id="addressCity" placeholder="Bengaluru">
                <select class="form-control mb-2" id="addressState">
                    <option value="Karnataka">Karnataka</option>
                    <option value="Tamil Nadu">Tamil Nadu</option>
                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                    <option value="Kerala">Kerala</option>
                </select>
                <input type="text" class="form-control mb-2" id="addressPincode" placeholder="560078">
                <select class="form-control mb-2" id="addressCountry">
                    <option value="India">India</option>
                    <option value="USA">USA</option>
                    <option value="UK">UK</option>
                </select>
                <input type="hidden" id="addressFieldName">
            </div>
            <button type="button" class="btn btn-primary btn-block" onclick="updateAddress()">Update</button>
        </div>
    </div>
</div>

<!-- Modern Modal Styles -->
<style>
    /* Modern Modal Styling */
    .modern-modal .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }

    .modern-modal .modal-content {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    }

    .modern-modal .modal-header {
        background: linear-gradient(135deg, #7b5cff 0%, #4e8cff 100%);
        color: white;
        border-radius: 16px 16px 0 0;
        padding: 1.25rem 1.5rem;
        border-bottom: none;
    }

    .modern-modal .modal-title {
        font-weight: 600;
        font-size: 1.1rem;
    }

    .modern-modal .modal-body {
        padding: 1.5rem;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .modern-modal .modal-footer {
        border-top: 1px solid #e0e0e0;
        padding: 1rem 1.5rem;
        border-radius: 0 0 16px 16px;
    }

    .modern-modal .btn {
        border-radius: 12px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.2s;
    }

    .modern-modal .btn:hover {
        transform: translateY(-1px);
    }

    /* Missing Fields Modal Specific Styles */
    .missing-fields-container {
        max-height: 400px;
        overflow-y: auto;
    }

    .missing-fields-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .missing-fields-header h6 {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .missing-fields-list {
        margin-bottom: 1rem;
    }

    .missing-field-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        border-left: 4px solid #ffc107;
    }

    .field-number {
        background: #ffc107;
        color: #212529;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .field-name {
        font-weight: 500;
        color: #333;
    }

    .missing-fields-footer {
        padding-top: 1rem;
        border-top: 1px solid #e0e0e0;
        color: #666;
        font-size: 0.9rem;
    }

    /* Referral Modal Specific Styles */
    .referral-link-container {
        text-align: center;
    }

    .referral-link-text input {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 0.75rem;
        text-align: center;
    }

    .referral-link-actions .btn {
        min-width: 120px;
    }

    /* Responsive Modal Adjustments */
    @media (max-width: 576px) {
        .modern-modal .modal-dialog {
            margin: 1rem;
            max-width: calc(100% - 2rem);
        }

        .modern-modal .modal-body,
        .modern-modal .modal-header,
        .modern-modal .modal-footer {
            padding-left: 1rem;
            padding-right: 1rem;
        }
    }

    /* Utility classes for icons */
    .me-1 { margin-right: 0.25rem; }
    .me-2 { margin-right: 0.5rem; }
    .mb-0 { margin-bottom: 0; }
    .mb-3 { margin-bottom: 1rem; }
    .text-warning { color: #ffc107; }

    /* Loading states for dynamic content */
    .profile-section.loading {
        position: relative;
        opacity: 0.6;
        pointer-events: none;
    }

    .profile-section.loading::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #7b5cff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1000;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Dynamic field highlighting */
    .detail-item[data-field] {
        transition: all 0.3s ease;
    }

    .detail-item[data-field].updated {
        background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
        border-left-color: #28a745;
    }

    /* Configuration info panel */
    .config-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 1px solid #e1bee7;
        border-radius: 12px;
        padding: 1rem;
        margin: 1rem;
        font-size: 0.9rem;
    }

    .config-info h5 {
        color: #4a148c;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .config-info .config-details {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .config-tag {
        background: #7b5cff;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
    }
</style>

<style>
    /* Mobile-first design matching the provided image */
    .mobile-container {
        max-width: 440px;
        margin: 0 auto;
        background: #f5f5f5;
        min-height: 100vh;
    }

    .profile-photo-section {
        background: #fff;
        padding: 1.5rem;
        text-align: center;
        border-radius: 0 0 20px 20px;
        margin-bottom: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .profile-photo {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #e0e0e0;
        margin-bottom: 1rem;
    }

    .profile-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .profile-class {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .edit-profile-btn {
        background: #7b5cff;
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .edit-profile-btn:hover {
        background: #6a4de8;
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }

    .profile-section {
        background: #fff;
        margin: 0 1rem 1rem 1rem;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .section-header {
        background: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e0e0e0;
        font-weight: 700;
        font-size: 1rem;
        color: #333;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-content {
        padding: 0;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        min-height: auto;
        gap: 0.5rem;
        position: relative;
    }

    .detail-value-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 8px;
    }

    .detail-value {
        flex: 1;
        word-wrap: break-word;
        line-height: 1.4;
    }

    .edit-btn {
        background: transparent;
        border: none;
        /* color: #6c757d; */
        cursor: pointer;
        padding: 4px;
        /* border-radius: 4px; */
        /* transition: all 0.2s ease; */
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .edit-btn:hover {
        color: #007bff;
        background-color: #f8f9fa;
    }

    .edit-btn svg {
        width: 14px;
        height: 14px;
        fill: currentColor;
        transition: all 0.2s ease;
    }

    .edit-btn i {
        font-size: 14px;
        color: inherit;
    }

    .edit-btn:hover svg {
        fill: #007bff;
    }

    .edit-btn:hover i {
        color: #007bff;
    }

    /* Success Toast Styling */
    .success-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        animation: slideInRight 0.3s ease;
    }

    .success-toast i {
        font-size: 1.2rem;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .detail-value-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 10px;
    }

    .edit-btn {
        background:rgb(255, 255, 255);
        border: 0px solidrgb(255, 255, 255);
        color: #6c757d;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;
        transition: all 0.2s ease;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .edit-btn svg {
        width: 16px;
        height: 16px;
        fill: currentColor;
        transition: all 0.2s ease;
    }

    .edit-btn i {
        font-size: 16px;
        color: inherit;
    }

    .edit-btn:hover {
        color: #007bff;
        background-color: #f8f9fa;
    }

    .edit-btn:hover svg {
        fill: #007bff;
    }

    .edit-btn:active {
        transform: scale(0.95);
    }

    /* Bottom Sheet Modal Styles */
    .bottom-sheet {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .bottom-sheet.show {
        visibility: visible;
        opacity: 1;
    }

    .bottom-sheet-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .bottom-sheet-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-radius: 20px 20px 0 0;
        transform: translateY(100%);
        transition: transform 0.3s ease;
        max-height: 80vh;
        overflow-y: auto;
    }

    .bottom-sheet.show .bottom-sheet-content {
        transform: translateY(0);
    }

    .bottom-sheet-header {
        padding: 12px 0;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
    }

    .bottom-sheet-handle {
        width: 40px;
        height: 4px;
        background: #ddd;
        border-radius: 2px;
        margin: 0 auto;
    }

    .bottom-sheet-body {
        padding: 20px;
    }

    .bottom-sheet-body h5 {
        margin-bottom: 20px;
        font-weight: 600;
        color: #333;
    }

    .bottom-sheet-body .form-control {
        /* border: 1px solid #e0e0e0; */
        border-radius: 8px;
        padding: 12px;
        font-size: 16px;
        margin-bottom: 12px;
    }

    .bottom-sheet-body .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .bottom-sheet-body .btn-primary {
       display: flex;
        width: 100%;
        height: 48px;
        padding: 14px 20px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        border-radius: 10px;
        background: #623CE7;
        border: none;
    }

    .bottom-sheet-body .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    body.modal-open {
        overflow: hidden;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 400;
        color: #888;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .detail-value {
        color: #333;
        font-size: 0.95rem;
        font-weight: 500;
        word-break: break-word;
        line-height: 1.4;
    }

    .parent-photo-section {
    text-align: center;
    padding: 1.5rem;
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    display: inline-block;
}

.photo-card {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.parent-photo {
    width: 220px;
    height: 160px;
    border-radius: 12px;
    object-fit: cover;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.photo-actions {
    display: flex;
    gap: 2rem;
    justify-content: center;
}

.action-btn {
    border: none;
    background: none;
    font-size: 14px;
    color: #444;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: color 0.3s;
}

.action-btn:hover {
    color: #000;
}

.student-photo-section {
        text-align: center;
        padding: 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        background: #fff;
    }

    .student-photo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #e0e0e0;
        margin-bottom: 0.75rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .parent-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .parent-contact {
        color: #666;
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .mobile-container {
            max-width: 100%;
            background: #f5f5f5;
        }

        .profile-section {
            margin: 0 0.75rem 1rem 0.75rem;
            border-radius: 12px;
        }

        .detail-item {
            padding: 1rem 1.25rem;
        }

        .detail-label {
            font-size: 0.8rem;
        }

        .detail-value {
            font-size: 0.9rem;
        }

        .student-photo-section {
            padding: 1.25rem;
        }

        .student-photo {
            width: 70px;
            height: 70px;
        }

        .parent-name {
            font-size: 1rem;
        }

        .parent-contact {
            font-size: 0.85rem;
        }
    }

    /* Additional styling for better visual hierarchy */
    .section-header {
        position: relative;
    }

    .section-header::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: #7b5cff;
        border-radius: 2px;
    }

    .section-header {
        padding-left: 2rem;
    }

    /* Enhanced detail item styling */
    .detail-item {
        transition: background-color 0.2s ease;
    }

    .detail-item:hover {
        background-color: #f8f9fa;
    }

    /* Profile photo with camera icon overlay */
    .profile-photo-section .profile-photo {
        position: relative;
    }

    .camera-icon {
        position: absolute;
        bottom: 5px;
        right: 5px;
        background: #7b5cff;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        border: 2px solid white;
    }
</style>

<style>
    .profile-header {
        background: #fff;
        padding: 1rem 1rem 0.5rem 1rem;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        display: flex;
        align-items: center;
        position: relative;
    }
    .profile-header .back-btn {
        font-size: 1.5rem;
        color: #7b5cff;
        background: none;
        border: none;
        margin-right: 0.5rem;
        padding: 0;
        cursor: pointer;
    }
    .profile-header-title {
        flex: 1;
        text-align: center;
        font-weight: 700;
        font-size: 1.1rem;
        color: #333;
    }
    .profile-alert {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 12px;
        margin: 1rem;
        padding: 1.25rem 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }

    .profile-alert .alert-content {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .profile-alert .alert-icon {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        margin-top: 0px;
    }

    .profile-alert .alert-icon svg {
        width: 20px;
        height: 20px;
        fill: #856404;
    }

    .profile-alert .alert-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .profile-alert .alert-title {
        font-size: 1rem;
        font-weight: 600;
        color: #856404;
        margin: 0 0 0.75rem 0;
        line-height: 1.3;
    }

    .profile-alert .alert-description {
        font-size: 0.9rem;
        color: #856404;
        line-height: 1.5;
        margin: 0 0 0.75rem 0;
    }

    .profile-alert .alert-warning {
        font-size: 0.9rem;
        color: #856404;
        line-height: 1.5;
        margin: 0 0 1.25rem 0;
        font-weight: 500;
    }

    .profile-alert .alert-button {
        background: #ffffff;
        color: #404040;
        border: 0px solid #d0d0d0;
        border-radius: 8px;
        padding: 0.75rem 3rem;
        font-size: 0.9rem;
        font-weight: 300;
        cursor: pointer;
        transition: all 0.2s;
        align-self: center;
        margin-top: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    .profile-alert .alert-button:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
    .profile-alert.confirmed {
        background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
        border: 1px solid #c3e6cb;
    }
    .profile-alert.confirmed .alert-text {
        color: #155724;
    }
    .profile-alert.locked {
        background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
        border: 1px solid #f5c6cb;
    }
    .profile-alert.locked .alert-text {
        color: #721c24;
    }
    .referral-section {
        background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
        border-radius: 16px;
        margin: 1rem;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        border: 1px solid #ffeaa7;
    }
    .referral-section .referral-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }
    .referral-section .referral-text {
        flex: 1;
        font-size: 0.95rem;
        color: #856404;
        font-weight: 500;
    }
    .referral-section .referral-button {
        background: #ffc107;
        color: #212529;
        border: none;
        border-radius: 12px;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
    }
    .referral-section .referral-button:hover {
        background: #e0a800;
        transform: translateY(-1px);
    }
    .content-container {
        margin-bottom: 6rem;
        padding-bottom: 1rem;
    }
    @media (min-width: 600px) {
        .profile-header, .profile-alert, .referral-section, .content-container {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
        }
        .profile-alert, .referral-section {
            margin-left: auto;
            margin-right: auto;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>


<style>
    .profile-section {
        background: #fff;
        border-radius: 16px;
        margin: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        overflow: hidden;
    }
    /* Section header styles are already defined in the main style block above */
    .section-content {
        padding: 0;
    }
    .family-photo-container {
        text-align: center;
        padding: 1rem 0;
    }
    .family-photo {
        max-width: 200px;
        max-height: 200px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        object-fit: cover;
    }
    .remarks-text {
        font-size: 0.95rem;
        line-height: 1.5;
        color: #333;
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 12px;
        border-left: 4px solid #7b5cff;
    }
    .text-muted {
        color: #888 !important;
        font-style: italic;
    }
    @media (min-width: 600px) {
        .profile-section {
            max-width: 440px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 1rem;
            margin-bottom: 1rem;
        }
    }
</style>
<!-- Bootbox -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.5.2/bootbox.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

