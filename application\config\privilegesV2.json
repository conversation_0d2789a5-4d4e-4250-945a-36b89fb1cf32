[{"name": "ACTIVITY", "friendlyName": "ACTIVITY", "description": "Module for ACTIVITY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ACTIVITY module", "dependsOn": []}]}, {"name": "ADDITIONAL_INCOME", "friendlyName": "ADDITIONAL_INCOME", "description": "Module for ADDITIONAL_INCOME management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ADDITIONAL_INCOME module", "dependsOn": []}, {"name": "ADD_CATEGORY", "friendlyName": "ADD_CATEGORY", "description": "Allows ADD_CATEGORY actions in ADDITIONAL_INCOME module", "dependsOn": []}, {"name": "ADD_INCOME", "friendlyName": "ADD_INCOME", "description": "Allows ADD_INCOME actions in ADDITIONAL_INCOME module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in ADDITIONAL_INCOME module", "dependsOn": []}]}, {"name": "ASSETS", "friendlyName": "ASSETS", "description": "Module for ASSETS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ASSETS module", "dependsOn": []}]}, {"name": "BUILDING_MASTER", "friendlyName": "BUILDING_MASTER", "description": "Module for BUILDING_MASTER management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in BUILDING_MASTER module", "dependsOn": []}]}, {"name": "OTHERLINKS", "friendlyName": "OTHERLINKS", "description": "Module for OTHERLINKS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in OTHERLINKS module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in OTHERLINKS module", "dependsOn": []}]}, {"name": "CIRCULARV2", "friendlyName": "CIRCULARV2", "description": "Module for CIRCULARV2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in CIRCULARV2 module", "dependsOn": []}, {"name": "CREATE", "friendlyName": "CREATE", "description": "Allows CREATE actions in CIRCULARV2 module", "dependsOn": []}, {"name": "VIEW_ALL", "friendlyName": "VIEW_ALL", "description": "Allows VIEW_ALL actions in CIRCULARV2 module", "dependsOn": []}, {"name": "REPORT", "friendlyName": "REPORT", "description": "Allows REPORT actions in CIRCULARV2 module", "dependsOn": []}, {"name": "APPROVER", "friendlyName": "APPROVER", "description": "Allows APPROVER actions in CIRCULARV2 module", "dependsOn": []}, {"name": "UNPUBLISH", "friendlyName": "UNPUBLISH", "description": "Allows UNPUBLISH actions in CIRCULARV2 module", "dependsOn": []}]}, {"name": "COMMUNICATION", "friendlyName": "COMMUNICATION", "description": "Module for COMMUNICATION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in COMMUNICATION module", "dependsOn": []}, {"name": "EMAIL_TEMPLATE", "friendlyName": "EMAIL_TEMPLATE", "description": "Allows EMAIL_TEMPLATE actions in COMMUNICATION module", "dependsOn": []}, {"name": "SMS_TEMPLATE", "friendlyName": "SMS_TEMPLATE", "description": "Allows SMS_TEMPLATE actions in COMMUNICATION module", "dependsOn": []}, {"name": "SMS_TEMPLATE_APPROVAL", "friendlyName": "SMS_TEMPLATE_APPROVAL", "description": "Allows SMS_TEMPLATE_APPROVAL actions in COMMUNICATION module", "dependsOn": []}, {"name": "GROUP_CREATION", "friendlyName": "GROUP_CREATION", "description": "Allows GROUP_CREATION actions in COMMUNICATION module", "dependsOn": []}, {"name": "REPORT", "friendlyName": "REPORT", "description": "Allows REPORT actions in COMMUNICATION module", "dependsOn": []}, {"name": "RESEND_CIRCULAR", "friendlyName": "RESEND_CIRCULAR", "description": "Allows RESEND_CIRCULAR actions in COMMUNICATION module", "dependsOn": []}]}, {"name": "COMPETITION", "friendlyName": "COMPETITION", "description": "Module for COMPETITION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in COMPETITION module", "dependsOn": []}, {"name": "ADD_EDIT_DELETE", "friendlyName": "ADD_EDIT_DELETE", "description": "Allows ADD_EDIT_DELETE actions in COMPETITION module", "dependsOn": []}, {"name": "APPROVE", "friendlyName": "APPROVE", "description": "Allows APPROVE actions in COMPETITION module", "dependsOn": []}, {"name": "VIEW", "friendlyName": "VIEW", "description": "Allows VIEW actions in COMPETITION module", "dependsOn": []}, {"name": "DEFAULT_STAFF_INCHARGE", "friendlyName": "DEFAULT_STAFF_INCHARGE", "description": "Allows DEFAULT_STAFF_INCHARGE actions in COMPETITION module", "dependsOn": []}]}, {"name": "EMAILS", "friendlyName": "EMAILS", "description": "Module for EMAILS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in EMAILS module", "dependsOn": []}, {"name": "SEND", "friendlyName": "SEND", "description": "Allows SEND actions in EMAILS module", "dependsOn": []}, {"name": "VIEW_ALL", "friendlyName": "VIEW_ALL", "description": "Allows VIEW_ALL actions in EMAILS module", "dependsOn": []}]}, {"name": "EXAMINATION", "friendlyName": "EXAMINATION", "description": "Module for EXAMINATION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in EXAMINATION module", "dependsOn": []}, {"name": "CREATE", "friendlyName": "CREATE", "description": "Allows CREATE actions in EXAMINATION module", "dependsOn": []}, {"name": "ACCESS_CONTROL", "friendlyName": "ACCESS_CONTROL", "description": "Allows ACCESS_CONTROL actions in EXAMINATION module", "dependsOn": []}, {"name": "PERFORMANCE_ANALYSIS", "friendlyName": "PERFORMANCE_ANALYSIS", "description": "Allows PERFORMANCE_ANALYSIS actions in EXAMINATION module", "dependsOn": []}, {"name": "CLASS_REPORT", "friendlyName": "CLASS_REPORT", "description": "Allows CLASS_REPORT actions in EXAMINATION module", "dependsOn": []}, {"name": "STUDENT_REPORT", "friendlyName": "STUDENT_REPORT", "description": "Allows STUDENT_REPORT actions in EXAMINATION module", "dependsOn": []}, {"name": "UNLOCK_MARKS_ENTRY", "friendlyName": "UNLOCK_MARKS_ENTRY", "description": "Allows UNLOCK_MARKS_ENTRY actions in EXAMINATION module", "dependsOn": []}, {"name": "ADD_REMARKS_CT", "friendlyName": "ADD_REMARKS_CT", "description": "Allows ADD_REMARKS_CT actions in EXAMINATION module", "dependsOn": []}, {"name": "CONSOLIDATION", "friendlyName": "CONSOLIDATION", "description": "Allows CONSOLIDATION actions in EXAMINATION module", "dependsOn": []}, {"name": "VERIFY_MARKS_CARDS", "friendlyName": "VERIFY_MARKS_CARDS", "description": "Allows VERIFY_MARKS_CARDS actions in EXAMINATION module", "dependsOn": []}, {"name": "ADD_REMARKS_ALL", "friendlyName": "ADD_REMARKS_ALL", "description": "Allows ADD_REMARKS_ALL actions in EXAMINATION module", "dependsOn": []}, {"name": "VERIFY_REMARKS", "friendlyName": "VERIFY_REMARKS", "description": "Allows VERIFY_REMARKS actions in EXAMINATION module", "dependsOn": []}, {"name": "EXAM_ADMIN", "friendlyName": "EXAM_ADMIN", "description": "Allows EXAM_ADMIN actions in EXAMINATION module", "dependsOn": []}, {"name": "REGENERATE_MARKSCARDS", "friendlyName": "REGENERATE_MARKSCARDS", "description": "Allows REGENERATE_MARKSCARDS actions in EXAMINATION module", "dependsOn": []}, {"name": "CREATE_ELECTIVES", "friendlyName": "CREATE_ELECTIVES", "description": "Allows CREATE_ELECTIVES actions in EXAMINATION module", "dependsOn": []}, {"name": "RANKING_REPORT", "friendlyName": "RANKING_REPORT", "description": "Allows RANKING_REPORT actions in EXAMINATION module", "dependsOn": []}, {"name": "STAFF_PERFORMANCE_INDEX", "friendlyName": "STAFF_PERFORMANCE_INDEX", "description": "Allows STAFF_PERFORMANCE_INDEX actions in EXAMINATION module", "dependsOn": []}, {"name": "SUBJECT_REMARKS", "friendlyName": "SUBJECT_REMARKS", "description": "Allows SUBJECT_REMARKS actions in EXAMINATION module", "dependsOn": []}, {"name": "VIEW_MARKS_STATUS", "friendlyName": "VIEW_MARKS_STATUS", "description": "Allows VIEW_MARKS_STATUS actions in EXAMINATION module", "dependsOn": []}, {"name": "VIEW_MARKS_CARD_ALL", "friendlyName": "VIEW_MARKS_CARD_ALL", "description": "Allows VIEW_MARKS_CARD_ALL actions in EXAMINATION module", "dependsOn": []}, {"name": "VIEW_MARKS_CARD_CT", "friendlyName": "VIEW_MARKS_CARD_CT", "description": "Allows VIEW_MARKS_CARD_CT actions in EXAMINATION module", "dependsOn": []}, {"name": "PUBLISH_MARKS_TO_PARENT", "friendlyName": "PUBLISH_MARKS_TO_PARENT", "description": "Allows PUBLISH_MARKS_TO_PARENT actions in EXAMINATION module", "dependsOn": []}, {"name": "RESULT_ANALYSIS_REPORT", "friendlyName": "RESULT_ANALYSIS_REPORT", "description": "Allows RESULT_ANALYSIS_REPORT actions in EXAMINATION module", "dependsOn": []}, {"name": "GENERATE_MARKS_AVERAGE", "friendlyName": "GENERATE_MARKS_AVERAGE", "description": "Allows GENERATE_MARKS_AVERAGE actions in EXAMINATION module", "dependsOn": []}]}, {"name": "AFL", "friendlyName": "AFL", "description": "<PERSON><PERSON><PERSON> for AFL management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in AFL module", "dependsOn": []}, {"name": "SUBJECTS", "friendlyName": "SUBJECTS", "description": "Allows SUBJECTS actions in AFL module", "dependsOn": []}, {"name": "ASSESSMENTS", "friendlyName": "ASSESSMENTS", "description": "Allows ASSESSMENTS actions in AFL module", "dependsOn": []}, {"name": "GRADING_SYSTEM", "friendlyName": "GRADING_SYSTEM", "description": "Allows GRADING_SYSTEM actions in AFL module", "dependsOn": []}, {"name": "RUBRICS", "friendlyName": "RUBRICS", "description": "Allows RUBRICS actions in AFL module", "dependsOn": []}, {"name": "MARKS", "friendlyName": "MARKS", "description": "Allows MARKS actions in AFL module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in AFL module", "dependsOn": []}, {"name": "PERFORMANCE_POINTERS", "friendlyName": "PERFORMANCE_POINTERS", "description": "Allows PERFORMANCE_POINTERS actions in AFL module", "dependsOn": []}, {"name": "STUDENT_REPORT", "friendlyName": "STUDENT_REPORT", "description": "Allows STUDENT_REPORT actions in AFL module", "dependsOn": []}]}, {"name": "ELIBRARY", "friendlyName": "ELIBRARY", "description": "Module for ELIBRARY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ELIBRARY module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in ELIBRARY module", "dependsOn": []}]}, {"name": "EXPENSE", "friendlyName": "EXPENSE", "description": "Module for EXPENSE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in EXPENSE module", "dependsOn": []}, {"name": "ADD_CATEGORY", "friendlyName": "ADD_CATEGORY", "description": "Allows ADD_CATEGORY actions in EXPENSE module", "dependsOn": []}, {"name": "APPROVAL", "friendlyName": "APPROVAL", "description": "Allows APPROVAL actions in EXPENSE module", "dependsOn": []}, {"name": "ADD_EXPENSE", "friendlyName": "ADD_EXPENSE", "description": "Allows ADD_EXPENSE actions in EXPENSE module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in EXPENSE module", "dependsOn": []}, {"name": "CANCELLED_VOUCHER", "friendlyName": "CANCELLED_VOUCHER", "description": "Allows CANCELLED_VOUCHER actions in EXPENSE module", "dependsOn": []}, {"name": "APPROVAL_AMENDMENTS", "friendlyName": "APPROVAL_AMENDMENTS", "description": "Allows APPROVAL_AMENDMENTS actions in EXPENSE module", "dependsOn": []}, {"name": "ADD_EXPENSE_AMENDMENTS_BUTTON", "friendlyName": "ADD_EXPENSE_AMENDMENTS_BUTTON", "description": "Allows ADD_EXPENSE_AMENDMENTS_BUTTON actions in EXPENSE module", "dependsOn": []}, {"name": "APPROVE_REJECT_AMENDMENTS_BUTTON", "friendlyName": "APPROVE_REJECT_AMENDMENTS_BUTTON", "description": "Allows APPROVE_REJECT_AMENDMENTS_BUTTON actions in EXPENSE module", "dependsOn": []}]}, {"name": "FEESV2", "friendlyName": "FEESV2", "description": "Module for FEESV2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in FEESV2 module", "dependsOn": []}, {"name": "COLLECT_FEES", "friendlyName": "COLLECT_FEES", "description": "Allows COLLECT_FEES actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_DAILY_TX_REPORT", "friendlyName": "VIEW_DAILY_TX_REPORT", "description": "Allows VIEW_DAILY_TX_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_BALANCE_REPORT", "friendlyName": "VIEW_BALANCE_REPORT", "description": "Allows VIEW_BALANCE_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_ONLINE_TX_REPORT", "friendlyName": "VIEW_ONLINE_TX_REPORT", "description": "Allows VIEW_ONLINE_TX_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_ONLINE_CHALLAN_TX_REPORT", "friendlyName": "VIEW_ONLINE_CHALLAN_TX_REPORT", "description": "Allows VIEW_ONLINE_CHALLAN_TX_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "ASSIGN_INVOICE", "friendlyName": "ASSIGN_INVOICE", "description": "Allows ASSIGN_INVOICE actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_CONCESSIONS", "friendlyName": "VIEW_CONCESSIONS", "description": "Allows VIEW_CONCESSIONS actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_SUMMARY", "friendlyName": "FEE_SUMMARY", "description": "Allows FEE_SUMMARY actions in FEESV2 module", "dependsOn": []}, {"name": "NON_RECONCILED", "friendlyName": "NON_RECONCILED", "description": "Allows NON_RECONCILED actions in FEESV2 module", "dependsOn": []}, {"name": "CLASS_WISE_REPORT", "friendlyName": "CLASS_WISE_REPORT", "description": "Allows CLASS_WISE_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "FAST_COLLECTION", "friendlyName": "FAST_COLLECTION", "description": "Allows FAST_COLLECTION actions in FEESV2 module", "dependsOn": []}, {"name": "TRANSPORTATION_CRUD", "friendlyName": "TRANSPORTATION_CRUD", "description": "Allows TRANSPORTATION_CRUD actions in FEESV2 module", "dependsOn": []}, {"name": "WIDGET", "friendlyName": "WIDGET", "description": "Allows WIDGET actions in FEESV2 module", "dependsOn": []}, {"name": "CANCELED_REPORT", "friendlyName": "CANCELED_REPORT", "description": "Allows CANCELED_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "CANCELED_REPORT", "friendlyName": "CANCELED_REPORT", "description": "Allows CANCELED_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "SOFT_DELETE_RECEIPTS", "friendlyName": "SOFT_DELETE_RECEIPTS", "description": "Allows SOFT_DELETE_RECEIPTS actions in FEESV2 module", "dependsOn": []}, {"name": "RECEIPT_CHANGE", "friendlyName": "RECEIPT_CHANGE", "description": "Allows RECEIPT_CHANGE actions in FEESV2 module", "dependsOn": []}, {"name": "CONSOLIDATED_FEE_REPORT", "friendlyName": "CONSOLIDATED_FEE_REPORT", "description": "Allows CONSOLIDATED_FEE_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "CLASS_WISE_DAILY_REPORT", "friendlyName": "CLASS_WISE_DAILY_REPORT", "description": "Allows CLASS_WISE_DAILY_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "FINE_AMOUNT_ASSIGN", "friendlyName": "FINE_AMOUNT_ASSIGN", "description": "Allows FINE_AMOUNT_ASSIGN actions in FEESV2 module", "dependsOn": []}, {"name": "FINE_AMOUNT_WAIVER", "friendlyName": "FINE_AMOUNT_WAIVER", "description": "Allows FINE_AMOUNT_WAIVER actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_DAILY_TX_REPORT_PRARTHANA", "friendlyName": "VIEW_DAILY_TX_REPORT_PRARTHANA", "description": "Allows VIEW_DAILY_TX_REPORT_PRARTHANA actions in FEESV2 module", "dependsOn": []}, {"name": "CONCESSIONS_ASSIGN", "friendlyName": "CONCESSIONS_ASSIGN", "description": "Allows CONCESSIONS_ASSIGN actions in FEESV2 module", "dependsOn": []}, {"name": "DAY_BOOK_APS", "friendlyName": "DAY_BOOK_APS", "description": "Allows DAY_BOOK_APS actions in FEESV2 module", "dependsOn": []}, {"name": "REFUND", "friendlyName": "REFUND", "description": "Allows REFUND actions in FEESV2 module", "dependsOn": []}, {"name": "FEES_COLLECTION_STATUS", "friendlyName": "FEES_COLLECTION_STATUS", "description": "Allows FEES_COLLECTION_STATUS actions in FEESV2 module", "dependsOn": []}, {"name": "DAY_BOOK_APS_COM", "friendlyName": "DAY_BOOK_APS_COM", "description": "Allows DAY_BOOK_APS_COM actions in FEESV2 module", "dependsOn": []}, {"name": "RE_GENERATE_PDF_RECEIPTS", "friendlyName": "RE_GENERATE_PDF_RECEIPTS", "description": "Allows RE_GENERATE_PDF_RECEIPTS actions in FEESV2 module", "dependsOn": []}, {"name": "REFUND_REPORTS", "friendlyName": "REFUND_REPORTS", "description": "Allows REFUND_REPORTS actions in FEESV2 module", "dependsOn": []}, {"name": "MANAGEMENT_SUMMARY", "friendlyName": "MANAGEMENT_SUMMARY", "description": "Allows MANAGEMENT_SUMMARY actions in FEESV2 module", "dependsOn": []}, {"name": "ADJUSTMENT_REPORT", "friendlyName": "ADJUSTMENT_REPORT", "description": "Allows ADJUSTMENT_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "OVERVIEW_ONLINE_SETTLEMENT_REPORT", "friendlyName": "OVERVIEW_ONLINE_SETTLEMENT_REPORT", "description": "Allows OVERVIEW_ONLINE_SETTLEMENT_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "ONLINE_SETTLEMENT_REPORT", "friendlyName": "ONLINE_SETTLEMENT_REPORT", "description": "Allows ONLINE_SETTLEMENT_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_STUDENT_HISTORY", "friendlyName": "FEE_STUDENT_HISTORY", "description": "Allows FEE_STUDENT_HISTORY actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_AUDIT_LOG", "friendlyName": "FEE_AUDIT_LOG", "description": "Allows FEE_AUDIT_LOG actions in FEESV2 module", "dependsOn": []}, {"name": "TRANSPORTATION_REPORT", "friendlyName": "TRANSPORTATION_REPORT", "description": "Allows TRANSPORTATION_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "PREDEFINED_CONCESSION", "friendlyName": "PREDEFINED_CONCESSION", "description": "Allows PREDEFINED_CONCESSION actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_SUMMARY_REPORT", "friendlyName": "FEE_SUMMARY_REPORT", "description": "Allows FEE_SUMMARY_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "EDIT_FEES_AMOUNT", "friendlyName": "EDIT_FEES_AMOUNT", "description": "Allows EDIT_FEES_AMOUNT actions in FEESV2 module", "dependsOn": []}, {"name": "REFUND_TRANSACTION", "friendlyName": "REFUND_TRANSACTION", "description": "Allows REFUND_TRANSACTION actions in FEESV2 module", "dependsOn": []}, {"name": "EXCESS_REPORT", "friendlyName": "EXCESS_REPORT", "description": "Allows EXCESS_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "INVOICE_GENERATE", "friendlyName": "INVOICE_GENERATE", "description": "Allows INVOICE_GENERATE actions in FEESV2 module", "dependsOn": []}, {"name": "EXCESS_AMOUNT", "friendlyName": "EXCESS_AMOUNT", "description": "Allows EXCESS_AMOUNT actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_COMPONENT_DETAIL_REPORT", "friendlyName": "FEE_COMPONENT_DETAIL_REPORT", "description": "Allows FEE_COMPONENT_DETAIL_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "CLASSWISE_STUDENT_FEE_COUNT", "friendlyName": "CLASSWISE_STUDENT_FEE_COUNT", "description": "Allows CLASSWISE_STUDENT_FEE_COUNT actions in FEESV2 module", "dependsOn": []}, {"name": "STATEMENT_GENERATE", "friendlyName": "STATEMENT_GENERATE", "description": "Allows STATEMENT_GENERATE actions in FEESV2 module", "dependsOn": []}, {"name": "SEND_FEES_RECEIPT_VIA_EMAIL", "friendlyName": "SEND_FEES_RECEIPT_VIA_EMAIL", "description": "Allows SEND_FEES_RECEIPT_VIA_EMAIL actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_ONLINE_CHALLAN_REPORT", "friendlyName": "VIEW_ONLINE_CHALLAN_REPORT", "description": "Allows VIEW_ONLINE_CHALLAN_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_DAILY_ONLINE_TX", "friendlyName": "VIEW_DAILY_ONLINE_TX", "description": "Allows VIEW_DAILY_ONLINE_TX actions in FEESV2 module", "dependsOn": []}, {"name": "CLASS_WISE_DATE_WISE_REPORT", "friendlyName": "CLASS_WISE_DATE_WISE_REPORT", "description": "Allows CLASS_WISE_DATE_WISE_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "FAST_COLLECTION_INSTALLMENT_WISE", "friendlyName": "FAST_COLLECTION_INSTALLMENT_WISE", "description": "Allows FAST_COLLECTION_INSTALLMENT_WISE actions in FEESV2 module", "dependsOn": []}, {"name": "ONLINE_PAYMENT_REFUND", "friendlyName": "ONLINE_PAYMENT_REFUND", "description": "Allows ONLINE_PAYMENT_REFUND actions in FEESV2 module", "dependsOn": []}, {"name": "FINE_WAIVER_REPORT", "friendlyName": "FINE_WAIVER_REPORT", "description": "Allows FINE_WAIVER_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_CONCESSIONS_DATE_WISE", "friendlyName": "VIEW_CONCESSIONS_DATE_WISE", "description": "Allows VIEW_CONCESSIONS_DATE_WISE actions in FEESV2 module", "dependsOn": []}, {"name": "PREDEFINED_FILTERS", "friendlyName": "PREDEFINED_FILTERS", "description": "Allows PREDEFINED_FILTERS actions in FEESV2 module", "dependsOn": []}, {"name": "MASS_FEE_ASSISGNED_STUDENT_WISE", "friendlyName": "MASS_FEE_ASSISGNED_STUDENT_WISE", "description": "Allows MASS_FEE_ASSISGNED_STUDENT_WISE actions in FEESV2 module", "dependsOn": []}, {"name": "CONCESSIONS_ASSIGN_APPROVER", "friendlyName": "CONCESSIONS_ASSIGN_APPROVER", "description": "Allows CONCESSIONS_ASSIGN_APPROVER actions in FEESV2 module", "dependsOn": []}, {"name": "CONCESSIONS_APPROVAL", "friendlyName": "CONCESSIONS_APPROVAL", "description": "Allows CONCESSIONS_APPROVAL actions in FEESV2 module", "dependsOn": []}, {"name": "FEES_EDIT_HISTORY_REPORT", "friendlyName": "FEES_EDIT_HISTORY_REPORT", "description": "Allows FEES_EDIT_HISTORY_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "MASS_INVOICE_STATEMENT", "friendlyName": "MASS_INVOICE_STATEMENT", "description": "Allows MASS_INVOICE_STATEMENT actions in FEESV2 module", "dependsOn": []}, {"name": "MASS_INVOICE_STATEMENT_EMAIL_REPORT", "friendlyName": "MASS_INVOICE_STATEMENT_EMAIL_REPORT", "description": "Allows MASS_INVOICE_STATEMENT_EMAIL_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "TALLY_DAILY_REPORT", "friendlyName": "TALLY_DAILY_REPORT", "description": "Allows TALLY_DAILY_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "SINGLE_WINDOW_APPROVAL_PROCESS", "friendlyName": "SINGLE_WINDOW_APPROVAL_PROCESS", "description": "Allows SINGLE_WINDOW_APPROVAL_PROCESS actions in FEESV2 module", "dependsOn": []}, {"name": "CREATE_INVOICES", "friendlyName": "CREATE_INVOICES", "description": "Allows CREATE_INVOICES actions in FEESV2 module", "dependsOn": []}, {"name": "ONLINE_REFUND_TX_REPORT", "friendlyName": "ONLINE_REFUND_TX_REPORT", "description": "Allows ONLINE_REFUND_TX_REPORT actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_COLLECTION_SUMMARY_DATE_WISE", "friendlyName": "FEE_COLLECTION_SUMMARY_DATE_WISE", "description": "Allows FEE_COLLECTION_SUMMARY_DATE_WISE actions in FEESV2 module", "dependsOn": []}, {"name": "STUDENT_WISE_PREDEFINED_FILTERS", "friendlyName": "STUDENT_WISE_PREDEFINED_FILTERS", "description": "Allows STUDENT_WISE_PREDEFINED_FILTERS actions in FEESV2 module", "dependsOn": []}, {"name": "VIEW_FEE_COLLECTION_PAGE_ADVANCED_DETAILS", "friendlyName": "VIEW_FEE_COLLECTION_PAGE_ADVANCED_DETAILS", "description": "Allows VIEW_FEE_COLLECTION_PAGE_ADVANCED_DETAILS actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_SUMMARY_REPORT_V2", "friendlyName": "FEE_SUMMARY_REPORT_V2", "description": "Allows FEE_SUMMARY_REPORT_V2 actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_COMPONENT_DETAIL_REPORT_V2", "friendlyName": "FEE_COMPONENT_DETAIL_REPORT_V2", "description": "Allows FEE_COMPONENT_DETAIL_REPORT_V2 actions in FEESV2 module", "dependsOn": []}, {"name": "FEE_SUMMARY_COMPONENT", "friendlyName": "FEE_SUMMARY_COMPONENT", "description": "Allows FEE_SUMMARY_COMPONENT actions in FEESV2 module", "dependsOn": []}, {"name": "FEES_DETAILS_REPORT_COMPONENT_WISE", "friendlyName": "FEES_DETAILS_REPORT_COMPONENT_WISE", "description": "Allows FEES_DETAILS_REPORT_COMPONENT_WISE actions in FEESV2 module", "dependsOn": []}]}, {"name": "FLASH_NEWS", "friendlyName": "FLASH_NEWS", "description": "Module for FLASH_NEWS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in FLASH_NEWS module", "dependsOn": []}]}, {"name": "GALLERY", "friendlyName": "GALLERY", "description": "Module for GALLERY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in GALLERY module", "dependsOn": []}, {"name": "VIEW_EDIT_DELETE", "friendlyName": "VIEW_EDIT_DELETE", "description": "Allows VIEW_EDIT_DELETE actions in GALLERY module", "dependsOn": []}]}, {"name": "HELIUM", "friendlyName": "HELIUM", "description": "Module for HELIUM management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in HELIUM module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in HELIUM module", "dependsOn": []}, {"name": "COURSE", "friendlyName": "COURSE", "description": "Allows COURSE actions in HELIUM module", "dependsOn": []}, {"name": "VIEW_SESSIONS", "friendlyName": "VIEW_SESSIONS", "description": "Allows VIEW_SESSIONS actions in HELIUM module", "dependsOn": []}, {"name": "MODIFY_COURSE", "friendlyName": "MODIFY_COURSE", "description": "Allows MODIFY_COURSE actions in HELIUM module", "dependsOn": []}, {"name": "BATCH", "friendlyName": "BATCH", "description": "Allows BATCH actions in HELIUM module", "dependsOn": []}, {"name": "MODIFY_BATCH", "friendlyName": "MODIFY_BATCH", "description": "Allows MODIFY_BATCH actions in HELIUM module", "dependsOn": []}, {"name": "MODIFY_SESSION", "friendlyName": "MODIFY_SESSION", "description": "Allows MODIFY_SESSION actions in HELIUM module", "dependsOn": []}, {"name": "VIEW_REGISTRATIONS", "friendlyName": "VIEW_REGISTRATIONS", "description": "Allows VIEW_REGISTRATIONS actions in HELIUM module", "dependsOn": []}, {"name": "VIEW_RESOURCES", "friendlyName": "VIEW_RESOURCES", "description": "Allows VIEW_RESOURCES actions in HELIUM module", "dependsOn": []}, {"name": "ADD_RESOURCES", "friendlyName": "ADD_RESOURCES", "description": "Allows ADD_RESOURCES actions in HELIUM module", "dependsOn": []}, {"name": "ADD_RECORDING", "friendlyName": "ADD_RECORDING", "description": "Allows ADD_RECORDING actions in HELIUM module", "dependsOn": []}, {"name": "REMOVE_RECORDING", "friendlyName": "REMOVE_RECORDING", "description": "Allows REMOVE_RECORDING actions in HELIUM module", "dependsOn": []}]}, {"name": "HOMEWORK", "friendlyName": "HOMEWORK", "description": "Module for HOMEWORK management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in HOMEWORK module", "dependsOn": []}, {"name": "HOMEWORK_ADMIN", "friendlyName": "HOMEWORK_ADMIN", "description": "Allows HOMEWORK_ADMIN actions in HOMEWORK module", "dependsOn": []}]}, {"name": "STUDENT_TASKS", "friendlyName": "STUDENT_TASKS", "description": "Module for STUDENT_TASKS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_TASKS module", "dependsOn": []}, {"name": "TASKS_ADMIN", "friendlyName": "TASKS_ADMIN", "description": "Allows TASKS_ADMIN actions in STUDENT_TASKS module", "dependsOn": []}, {"name": "QUESTION_BANK", "friendlyName": "QUESTION_BANK", "description": "Allows QUESTION_BANK actions in STUDENT_TASKS module", "dependsOn": []}]}, {"name": "ACADEMICS", "friendlyName": "ACADEMICS", "description": "Module for ACADEMICS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ACADEMICS module", "dependsOn": []}]}, {"name": "DONATION", "friendlyName": "DONATION", "description": "Module for DONATION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in DONATION module", "dependsOn": []}, {"name": "DONATION_ADMIN", "friendlyName": "DONATION_ADMIN", "description": "Allows DONATION_ADMIN actions in DONATION module", "dependsOn": []}, {"name": "ADD", "friendlyName": "ADD", "description": "Allows ADD actions in DONATION module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in DONATION module", "dependsOn": []}]}, {"name": "EVENT", "friendlyName": "EVENT", "description": "Module for EVENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in EVENT module", "dependsOn": []}, {"name": "EVENT_CRUD", "friendlyName": "EVENT_CRUD", "description": "Allows EVENT_CRUD actions in EVENT module", "dependsOn": []}, {"name": "EVENT_ATTENDANCE", "friendlyName": "EVENT_ATTENDANCE", "description": "Allows EVENT_ATTENDANCE actions in EVENT module", "dependsOn": []}, {"name": "CREATE_EVENT", "friendlyName": "CREATE_EVENT", "description": "Allows CREATE_EVENT actions in EVENT module", "dependsOn": []}, {"name": "VIEW_EVENT_DETAILS", "friendlyName": "VIEW_EVENT_DETAILS", "description": "Allows VIEW_EVENT_DETAILS actions in EVENT module", "dependsOn": []}, {"name": "RETURN_SAFETY_DEPOSIT", "friendlyName": "RETURN_SAFETY_DEPOSIT", "description": "Allows RETURN_SAFETY_DEPOSIT actions in EVENT module", "dependsOn": []}]}, {"name": "WALLET", "friendlyName": "WALLET", "description": "Module for WALLET management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in WALLET module", "dependsOn": []}, {"name": "LOAD_MONEY", "friendlyName": "LOAD_MONEY", "description": "Allows LOAD_MONEY actions in WALLET module", "dependsOn": []}, {"name": "VIEW_WALLET_DETAILS", "friendlyName": "VIEW_WALLET_DETAILS", "description": "Allows VIEW_WALLET_DETAILS actions in WALLET module", "dependsOn": []}, {"name": "ADD_TRANSACTION", "friendlyName": "ADD_TRANSACTION", "description": "Allows ADD_TRANSACTION actions in WALLET module", "dependsOn": []}, {"name": "DELETE_TRANSACTION", "friendlyName": "DELETE_TRANSACTION", "description": "Allows DELETE_TRANSACTION actions in WALLET module", "dependsOn": []}]}, {"name": "INVENTORY", "friendlyName": "INVENTORY", "description": "Module for INVENTORY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in INVENTORY module", "dependsOn": []}]}, {"name": "INTERNAL_TICKETING", "friendlyName": "INTERNAL_TICKETING", "description": "Module for INTERNAL_TICKETING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in INTERNAL_TICKETING module", "dependsOn": []}, {"name": "CREATE", "friendlyName": "CREATE", "description": "Allows CREATE actions in INTERNAL_TICKETING module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in INTERNAL_TICKETING module", "dependsOn": []}, {"name": "INTERNAL_TICKETING_ADMIN", "friendlyName": "INTERNAL_TICKETING_ADMIN", "description": "Allows INTERNAL_TICKETING_ADMIN actions in INTERNAL_TICKETING module", "dependsOn": []}, {"name": "CREATE_TICKET_ON_BEHALF", "friendlyName": "CREATE_TICKET_ON_BEHALF", "description": "Allows CREATE_TICKET_ON_BEHALF actions in INTERNAL_TICKETING module", "dependsOn": []}]}, {"name": "INFIRMARY", "friendlyName": "INFIRMARY", "description": "Module for INFIRMARY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in INFIRMARY module", "dependsOn": []}, {"name": "CREATE", "friendlyName": "CREATE", "description": "Allows CREATE actions in INFIRMARY module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in INFIRMARY module", "dependsOn": []}, {"name": "INFIRMARY_ADMIN", "friendlyName": "INFIRMARY_ADMIN", "description": "Allows INFIRMARY_ADMIN actions in INFIRMARY module", "dependsOn": []}, {"name": "MEDICAL_EXPENCES", "friendlyName": "MEDICAL_EXPENCES", "description": "Allows MEDICAL_EXPENCES actions in INFIRMARY module", "dependsOn": []}, {"name": "MEDICAL_EXPENSE_MASS", "friendlyName": "MEDICAL_EXPENSE_MASS", "description": "Allows MEDICAL_EXPENSE_MASS actions in INFIRMARY module", "dependsOn": []}]}, {"name": "LESSON_PLAN", "friendlyName": "LESSON_PLAN", "description": "Module for LESSON_PLAN management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in LESSON_PLAN module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in LESSON_PLAN module", "dependsOn": []}, {"name": "MANAGE_SYLLABUS", "friendlyName": "MANAGE_SYLLABUS", "description": "Allows MANAGE_SYLLABUS actions in LESSON_PLAN module", "dependsOn": []}, {"name": "PLAN_SYLLABUS_SCHEDULE", "friendlyName": "PLAN_SYLLABUS_SCHEDULE", "description": "Allows PLAN_SYLLABUS_SCHEDULE actions in LESSON_PLAN module", "dependsOn": []}, {"name": "DESIGN_SESSION", "friendlyName": "DESIGN_SESSION", "description": "Allows DESIGN_SESSION actions in LESSON_PLAN module", "dependsOn": []}, {"name": "CHECK_IN_OUT_SESSION", "friendlyName": "CHECK_IN_OUT_SESSION", "description": "Allows CHECK_IN_OUT_SESSION actions in LESSON_PLAN module", "dependsOn": []}, {"name": "LMS_ROLLBACK_PRIVILEGE", "friendlyName": "LMS_ROLLBACK_PRIVILEGE", "description": "Allows LMS_ROLLBACK_PRIVILEGE actions in LESSON_PLAN module", "dependsOn": []}, {"name": "ADD_SUBJECTS", "friendlyName": "ADD_SUBJECTS", "description": "Allows ADD_SUBJECTS actions in LESSON_PLAN module", "dependsOn": []}, {"name": "CLONE_OPTIONS", "friendlyName": "CLONE_OPTIONS", "description": "Allows CLONE_OPTIONS actions in LESSON_PLAN module", "dependsOn": []}, {"name": "DELETE_ADDED_SUBJECTS", "friendlyName": "DELETE_ADDED_SUBJECTS", "description": "Allows DELETE_ADDED_SUBJECTS actions in LESSON_PLAN module", "dependsOn": []}]}, {"name": "LIBRARY", "friendlyName": "LIBRARY", "description": "Module for LIBRARY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in LIBRARY module", "dependsOn": []}, {"name": "VIEW_EDIT_DELETE", "friendlyName": "VIEW_EDIT_DELETE", "description": "Allows VIEW_EDIT_DELETE actions in LIBRARY module", "dependsOn": []}, {"name": "STAFF_VIEW", "friendlyName": "STAFF_VIEW", "description": "Allows STAFF_VIEW actions in LIBRARY module", "dependsOn": []}, {"name": "BOOKS", "friendlyName": "BOOKS", "description": "Allows BOOKS actions in LIBRARY module", "dependsOn": []}, {"name": "BORROW_RETURN", "friendlyName": "BORROW_RETURN", "description": "Allows BORROW_RETURN actions in LIBRARY module", "dependsOn": []}, {"name": "FINE_COLLECTION", "friendlyName": "FINE_COLLECTION", "description": "Allows FINE_COLLECTION actions in LIBRARY module", "dependsOn": []}, {"name": "BULK_CURCULATION", "friendlyName": "BULK_CURCULATION", "description": "Allows BULK_CURCULATION actions in LIBRARY module", "dependsOn": []}, {"name": "DAMAGE_OR_LOST_BOOK", "friendlyName": "DAMAGE_OR_LOST_BOOK", "description": "Allows DAMAGE_OR_LOST_BOOK actions in LIBRARY module", "dependsOn": []}, {"name": "STOCK_CHECK", "friendlyName": "STOCK_CHECK", "description": "Allows STOCK_CHECK actions in LIBRARY module", "dependsOn": []}, {"name": "DAILY_TX_REPORT", "friendlyName": "DAILY_TX_REPORT", "description": "Allows DAILY_TX_REPORT actions in LIBRARY module", "dependsOn": []}, {"name": "BOOK_BORROWED_REPORT", "friendlyName": "BOOK_BORROWED_REPORT", "description": "Allows BOOK_BORROWED_REPORT actions in LIBRARY module", "dependsOn": []}, {"name": "FINE_DEFAULTER_REPORT", "friendlyName": "FINE_DEFAULTER_REPORT", "description": "Allows FINE_DEFAULTER_REPORT actions in LIBRARY module", "dependsOn": []}, {"name": "STOCK_REPORT", "friendlyName": "STOCK_REPORT", "description": "Allows STOCK_REPORT actions in LIBRARY module", "dependsOn": []}, {"name": "MEMBER_TX_REPORT", "friendlyName": "MEMBER_TX_REPORT", "description": "Allows MEMBER_TX_REPORT actions in LIBRARY module", "dependsOn": []}, {"name": "GENERATE_QR_CODES", "friendlyName": "GENERATE_QR_CODES", "description": "Allows GENERATE_QR_CODES actions in LIBRARY module", "dependsOn": []}, {"name": "ASSIGN_CARD", "friendlyName": "ASSIGN_CARD", "description": "Allows ASSIGN_CARD actions in LIBRARY module", "dependsOn": []}, {"name": "BULK_ASSIGN_CARD", "friendlyName": "BULK_ASSIGN_CARD", "description": "Allows BULK_ASSIGN_CARD actions in LIBRARY module", "dependsOn": []}, {"name": "CARD_MASTER", "friendlyName": "CARD_MASTER", "description": "Allows CARD_MASTER actions in LIBRARY module", "dependsOn": []}, {"name": "BORROW_RETURN_RENEWAL", "friendlyName": "BORROW_RETURN_RENEWAL", "description": "Allows BORROW_RETURN_RENEWAL actions in LIBRARY module", "dependsOn": []}]}, {"name": "MANAGEMENT", "friendlyName": "MANAGEMENT", "description": "Module for MANAGEMENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in MANAGEMENT module", "dependsOn": []}]}, {"name": "MSM", "friendlyName": "MSM", "description": "Module for MSM management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in MSM module", "dependsOn": []}, {"name": "ACTIVITY_TRACKER", "friendlyName": "ACTIVITY_TRACKER", "description": "Allows ACTIVITY_TRACKER actions in MSM module", "dependsOn": []}, {"name": "BOARDING", "friendlyName": "BOARDING", "description": "Allows BOARDING actions in MSM module", "dependsOn": []}, {"name": "STUDENT_ANALYTICS", "friendlyName": "STUDENT_ANALYTICS", "description": "Allows STUDENT_ANALYTICS actions in MSM module", "dependsOn": []}, {"name": "FEE_COLLECTION", "friendlyName": "FEE_COLLECTION", "description": "Allows FEE_COLLECTION actions in MSM module", "dependsOn": []}, {"name": "FEE_SUMMARY", "friendlyName": "FEE_SUMMARY", "description": "Allows FEE_SUMMARY actions in MSM module", "dependsOn": []}, {"name": "STAFF_ATTENDANCE", "friendlyName": "STAFF_ATTENDANCE", "description": "Allows STAFF_ATTENDANCE actions in MSM module", "dependsOn": []}, {"name": "STUDENT_ATTENDANCE", "friendlyName": "STUDENT_ATTENDANCE", "description": "Allows STUDENT_ATTENDANCE actions in MSM module", "dependsOn": []}, {"name": "LEAD_MANAGEMENT", "friendlyName": "LEAD_MANAGEMENT", "description": "Allows LEAD_MANAGEMENT actions in MSM module", "dependsOn": []}, {"name": "PARENT_TICKETING", "friendlyName": "PARENT_TICKETING", "description": "Allows PARENT_TICKETING actions in MSM module", "dependsOn": []}, {"name": "INTERNAL_TICKETING", "friendlyName": "INTERNAL_TICKETING", "description": "Allows INTERNAL_TICKETING actions in MSM module", "dependsOn": []}, {"name": "ADMISSIONS", "friendlyName": "ADMISSIONS", "description": "Allows ADMISSIONS actions in MSM module", "dependsOn": []}, {"name": "INDUS_COGNITENSOR_DASHBOARD", "friendlyName": "INDUS_COGNITENSOR_DASHBOARD", "description": "Allows INDUS_COGNITENSOR_DASHBOARD actions in MSM module", "dependsOn": []}, {"name": "STAFF_REPORT", "friendlyName": "STAFF_REPORT", "description": "Allows STAFF_REPORT actions in MSM module", "dependsOn": []}, {"name": "INFIRMARY_ANALYTICS", "friendlyName": "INFIRMARY_ANALYTICS", "description": "Allows INFIRMARY_ANALYTICS actions in MSM module", "dependsOn": []}, {"name": "STAFF_LEAVE_APPROVE", "friendlyName": "STAFF_LEAVE_APPROVE", "description": "Allows STAFF_LEAVE_APPROVE actions in MSM module", "dependsOn": []}]}, {"name": "ONLINE_CLASS", "friendlyName": "ONLINE_CLASS", "description": "Module for ONLINE_CLASS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ONLINE_CLASS module", "dependsOn": []}, {"name": "CREATE_SCHEDULE", "friendlyName": "CREATE_SCHEDULE", "description": "Allows CREATE_SCHEDULE actions in ONLINE_CLASS module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in ONLINE_CLASS module", "dependsOn": []}]}, {"name": "ONLINE_CLASS_V2", "friendlyName": "ONLINE_CLASS_V2", "description": "Module for ONLINE_CLASS_V2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ONLINE_CLASS_V2 module", "dependsOn": []}, {"name": "CREATE_SCHEDULE", "friendlyName": "CREATE_SCHEDULE", "description": "Allows CREATE_SCHEDULE actions in ONLINE_CLASS_V2 module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in ONLINE_CLASS_V2 module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in ONLINE_CLASS_V2 module", "dependsOn": []}]}, {"name": "PARENT_TICKETING", "friendlyName": "PARENT_TICKETING", "description": "Module for PARENT_TICKETING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PARENT_TICKETING module", "dependsOn": []}, {"name": "CRUD_CATEGORY", "friendlyName": "CRUD_CATEGORY", "description": "Allows CRUD_CATEGORY actions in PARENT_TICKETING module", "dependsOn": []}, {"name": "ADD_OFFLINE_TICKET", "friendlyName": "ADD_OFFLINE_TICKET", "description": "Allows ADD_OFFLINE_TICKET actions in PARENT_TICKETING module", "dependsOn": []}, {"name": "TICKETING_ADMIN", "friendlyName": "TICKETING_ADMIN", "description": "Allows TICKETING_ADMIN actions in PARENT_TICKETING module", "dependsOn": []}, {"name": "TICKET_ANALYTICS", "friendlyName": "TICKET_ANALYTICS", "description": "Allows TICKET_ANALYTICS actions in PARENT_TICKETING module", "dependsOn": []}, {"name": "STAFF_TICKET_REPORT", "friendlyName": "STAFF_TICKET_REPORT", "description": "Allows STAFF_TICKET_REPORT actions in PARENT_TICKETING module", "dependsOn": []}, {"name": "STUDENT_TICKET_REPORT", "friendlyName": "STUDENT_TICKET_REPORT", "description": "Allows STUDENT_TICKET_REPORT actions in PARENT_TICKETING module", "dependsOn": []}]}, {"name": "PAYROLL", "friendlyName": "PAYROLL", "description": "Module for PAYROLL management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PAYROLL module", "dependsOn": []}, {"name": "VIEW_MY_PAYSLIPS", "friendlyName": "VIEW_MY_PAYSLIPS", "description": "Allows VIEW_MY_PAYSLIPS actions in PAYROLL module", "dependsOn": []}, {"name": "VIEW_TAX_DECLARATION", "friendlyName": "VIEW_TAX_DECLARATION", "description": "Allows VIEW_TAX_DECLARATION actions in PAYROLL module", "dependsOn": []}, {"name": "MANAGE_TAX_DECLARATION", "friendlyName": "MANAGE_TAX_DECLARATION", "description": "Allows MANAGE_TAX_DECLARATION actions in PAYROLL module", "dependsOn": []}, {"name": "PAYROLL_ADMIN", "friendlyName": "PAYROLL_ADMIN", "description": "Allows PAYROLL_ADMIN actions in PAYROLL module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in PAYROLL module", "dependsOn": []}, {"name": "DISBURSEMENT", "friendlyName": "DISBURSEMENT", "description": "Allows DISBURSEMENT actions in PAYROLL module", "dependsOn": []}, {"name": "PF_REPORTS", "friendlyName": "PF_REPORTS", "description": "Allows PF_REPORTS actions in PAYROLL module", "dependsOn": []}, {"name": "PT_REPORTS", "friendlyName": "PT_REPORTS", "description": "Allows PT_REPORTS actions in PAYROLL module", "dependsOn": []}, {"name": "ESI_REPORTS", "friendlyName": "ESI_REPORTS", "description": "Allows ESI_REPORTS actions in PAYROLL module", "dependsOn": []}, {"name": "ENTITLED_SALARY", "friendlyName": "ENTITLED_SALARY", "description": "Allows ENTITLED_SALARY actions in PAYROLL module", "dependsOn": []}, {"name": "MASS_PAYSLIP_GENERATION", "friendlyName": "MASS_PAYSLIP_GENERATION", "description": "Allows MASS_PAYSLIP_GENERATION actions in PAYROLL module", "dependsOn": []}, {"name": "STAFF_WISE_REPORT", "friendlyName": "STAFF_WISE_REPORT", "description": "Allows STAFF_WISE_REPORT actions in PAYROLL module", "dependsOn": []}, {"name": "RE_GENERATE_PAYSLIP", "friendlyName": "RE_GENERATE_PAYSLIP", "description": "Allows RE_GENERATE_PAYSLIP actions in PAYROLL module", "dependsOn": []}, {"name": "PAYROLL_HISTORY_REPORT", "friendlyName": "PAYROLL_HISTORY_REPORT", "description": "Allows PAYROLL_HISTORY_REPORT actions in PAYROLL module", "dependsOn": []}, {"name": "MONTHWISE_TDS_DEDUCTEE_REPORT", "friendlyName": "MONTHWISE_TDS_DEDUCTEE_REPORT", "description": "Allows MONTHWISE_TDS_DEDUCTEE_REPORT actions in PAYROLL module", "dependsOn": []}, {"name": "INCREMENTS_REPORT", "friendlyName": "INCREMENTS_REPORT", "description": "Allows INCREMENTS_REPORT actions in PAYROLL module", "dependsOn": []}, {"name": "ADD_INCREMENTS_PREVIOUS_MONTH", "friendlyName": "ADD_INCREMENTS_PREVIOUS_MONTH", "description": "Allows ADD_INCREMENTS_PREVIOUS_MONTH actions in PAYROLL module", "dependsOn": []}]}, {"name": "PROCUREMENT", "friendlyName": "PROCUREMENT", "description": "Module for PROCUREMENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT module", "dependsOn": []}, {"name": "REQUISITION", "friendlyName": "REQUISITION", "description": "Allows REQUISITION actions in PROCUREMENT module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in PROCUREMENT module", "dependsOn": []}, {"name": "REQUISITION_V2", "friendlyName": "REQUISITION_V2", "description": "Allows REQUISITION_V2 actions in PROCUREMENT module", "dependsOn": []}]}, {"name": "INDENT", "friendlyName": "INDENT", "description": "Module for INDENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to INDENT module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in INDENT module", "dependsOn": []}, {"name": "VIEW_INDENT", "friendlyName": "VIEW_INDENT", "description": "Allows VIEW_INDENT actions in INDENT module", "dependsOn": []}]}, {"name": "PROCUREMENT_VENDORS", "friendlyName": "PROCUREMENT_VENDORS", "description": "Module for PROCUREMENT_VENDORS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_VENDORS module", "dependsOn": []}]}, {"name": "PROCUREMENT_ASSETS", "friendlyName": "PROCUREMENT_ASSETS", "description": "Module for PROCUREMENT_ASSETS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_ASSETS module", "dependsOn": []}]}, {"name": "PROCUREMENT_INVENTORY", "friendlyName": "PROCUREMENT_INVENTORY", "description": "Module for PROCUREMENT_INVENTORY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_INVENTORY module", "dependsOn": []}]}, {"name": "PROCUREMENT_SERVICE_CONTRACT", "friendlyName": "PROCUREMENT_SERVICE_CONTRACT", "description": "Module for PROCUREMENT_SERVICE_CONTRACT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_SERVICE_CONTRACT module", "dependsOn": []}]}, {"name": "PROCUREMENT_BUDGET", "friendlyName": "PROCUREMENT_BUDGET", "description": "Module for PROCUREMENT_BUDGET management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_BUDGET module", "dependsOn": []}, {"name": "MANAGE_OVERALL_BUDGET", "friendlyName": "MANAGE_OVERALL_BUDGET", "description": "Allows MANAGE_OVERALL_BUDGET actions in PROCUREMENT_BUDGET module", "dependsOn": []}, {"name": "MANAGE_DEPARTMENT_BUDGET", "friendlyName": "MANAGE_DEPARTMENT_BUDGET", "description": "Allows MANAGE_DEPARTMENT_BUDGET actions in PROCUREMENT_BUDGET module", "dependsOn": []}, {"name": "CFO_CEO_APPROVER", "friendlyName": "CFO_CEO_APPROVER", "description": "Allows CFO_CEO_APPROVER actions in PROCUREMENT_BUDGET module", "dependsOn": []}]}, {"name": "PROCUREMENT_SALES", "friendlyName": "PROCUREMENT_SALES", "description": "Module for PROCUREMENT_SALES management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "SALES", "friendlyName": "SALES", "description": "Allows SALES actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "SALES_RETURN", "friendlyName": "SALES_RETURN", "description": "Allows SALES_RETURN actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "PRE_DEFINED_TEMPLATES", "friendlyName": "PRE_DEFINED_TEMPLATES", "description": "Allows PRE_DEFINED_TEMPLATES actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "DELETE_ITEMS_IN_TRANSACTION", "friendlyName": "DELETE_ITEMS_IN_TRANSACTION", "description": "Allows DELETE_ITEMS_IN_TRANSACTION actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "NON_RECONCILE_REPORT", "friendlyName": "NON_RECONCILE_REPORT", "description": "Allows NON_RECONCILE_REPORT actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "STUDENT_WISE_MISSING_ORDER_REPORT", "friendlyName": "STUDENT_WISE_MISSING_ORDER_REPORT", "description": "Allows STUDENT_WISE_MISSING_ORDER_REPORT actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "STUDENT_WISE_ORDER_HISTORY", "friendlyName": "STUDENT_WISE_ORDER_HISTORY", "description": "Allows STUDENT_WISE_ORDER_HISTORY actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "PARENT_ORDERS_SUMMARY_REPORT", "friendlyName": "PARENT_ORDERS_SUMMARY_REPORT", "description": "Allows PARENT_ORDERS_SUMMARY_REPORT actions in PROCUREMENT_SALES module", "dependsOn": []}, {"name": "PARENT_ORDERS_REPORT", "friendlyName": "PARENT_ORDERS_REPORT", "description": "Allows PARENT_ORDERS_REPORT actions in PROCUREMENT_SALES module", "dependsOn": []}]}, {"name": "PROCUREMENT_VOUCHER", "friendlyName": "PROCUREMENT_VOUCHER", "description": "Module for PROCUREMENT_VOUCHER management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_VOUCHER module", "dependsOn": []}, {"name": "VIEW_DETAILS", "friendlyName": "VIEW_DETAILS", "description": "Allows VIEW_DETAILS actions in PROCUREMENT_VOUCHER module", "dependsOn": []}, {"name": "CREATE_OR_EDIT", "friendlyName": "CREATE_OR_EDIT", "description": "Allows CREATE_OR_EDIT actions in PROCUREMENT_VOUCHER module", "dependsOn": []}, {"name": "APPROVE", "friendlyName": "APPROVE", "description": "Allows APPROVE actions in PROCUREMENT_VOUCHER module", "dependsOn": []}]}, {"name": "PROCUREMENT_DELIVERY_V2", "friendlyName": "PROCUREMENT_DELIVERY_V2", "description": "Module for PROCUREMENT_DELIVERY_V2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_DELIVERY_V2 module", "dependsOn": []}, {"name": "VIEW_DETAILS", "friendlyName": "VIEW_DETAILS", "description": "Allows VIEW_DETAILS actions in PROCUREMENT_DELIVERY_V2 module", "dependsOn": []}, {"name": "CREATE_OR_EDIT", "friendlyName": "CREATE_OR_EDIT", "description": "Allows CREATE_OR_EDIT actions in PROCUREMENT_DELIVERY_V2 module", "dependsOn": []}, {"name": "RECEIVE_DELIVERY_PO_VISIBLE", "friendlyName": "RECEIVE_DELIVERY_PO_VISIBLE", "description": "Allows RECEIVE_DELIVERY_PO_VISIBLE actions in PROCUREMENT_DELIVERY_V2 module", "dependsOn": []}, {"name": "RECEIVE_DELIVERY_PO_HIDDEN", "friendlyName": "RECEIVE_DELIVERY_PO_HIDDEN", "description": "Allows RECEIVE_DELIVERY_PO_HIDDEN actions in PROCUREMENT_DELIVERY_V2 module", "dependsOn": []}]}, {"name": "PROCUREMENT_INVOICE", "friendlyName": "PROCUREMENT_INVOICE", "description": "Module for PROCUREMENT_INVOICE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PROCUREMENT_INVOICE module", "dependsOn": []}, {"name": "VIEW_DETAILS", "friendlyName": "VIEW_DETAILS", "description": "Allows VIEW_DETAILS actions in PROCUREMENT_INVOICE module", "dependsOn": []}, {"name": "CREATE_OR_EDIT", "friendlyName": "CREATE_OR_EDIT", "description": "Allows CREATE_OR_EDIT actions in PROCUREMENT_INVOICE module", "dependsOn": []}, {"name": "APPROVE", "friendlyName": "APPROVE", "description": "Allows APPROVE actions in PROCUREMENT_INVOICE module", "dependsOn": []}]}, {"name": "PURCHASE", "friendlyName": "PURCHASE", "description": "Module for PURCHASE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in PURCHASE module", "dependsOn": []}]}, {"name": "QUESTION_BANK", "friendlyName": "QUESTION_BANK", "description": "Module for QUESTION_BANK management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in QUESTION_BANK module", "dependsOn": []}]}, {"name": "ROOM_BOOKING", "friendlyName": "ROOM_BOOKING", "description": "Module for ROOM_BOOKING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to ROOM_BOOKING module", "dependsOn": []}, {"name": "ALLOW_ALL_ROOMS", "friendlyName": "ALLOW_ALL_ROOMS", "description": "Allows ALLOW_ALL_ROOMS actions in ROOM_BOOKING module", "dependsOn": []}, {"name": "ALLOW_ROOM_BOOKING", "friendlyName": "ALLOW_ROOM_BOOKING", "description": "Allows ALLOW_ROOM_BOOKING actions in ROOM_BOOKING module", "dependsOn": []}]}, {"name": "SCHOOL", "friendlyName": "SCHOOL", "description": "Module for SCHOOL management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in SCHOOL module", "dependsOn": []}, {"name": "ACAD_YEAR_CHANGE", "friendlyName": "ACAD_YEAR_CHANGE", "description": "Allows ACAD_YEAR_CHANGE actions in SCHOOL module", "dependsOn": []}, {"name": "STAFF_QR_CODES", "friendlyName": "STAFF_QR_CODES", "description": "Allows STAFF_QR_CODES actions in SCHOOL module", "dependsOn": []}, {"name": "STUDENT_QR_CODES", "friendlyName": "STUDENT_QR_CODES", "description": "Allows STUDENT_QR_CODES actions in SCHOOL module", "dependsOn": []}, {"name": "STUDENT_PHOTOS", "friendlyName": "STUDENT_PHOTOS", "description": "Allows STUDENT_PHOTOS actions in SCHOOL module", "dependsOn": []}, {"name": "PARENT_QR_CODES", "friendlyName": "PARENT_QR_CODES", "description": "Allows PARENT_QR_CODES actions in SCHOOL module", "dependsOn": []}, {"name": "CLASS_MASTER", "friendlyName": "CLASS_MASTER", "description": "Allows CLASS_MASTER actions in SCHOOL module", "dependsOn": []}, {"name": "CLASS_MASTERV2", "friendlyName": "CLASS_MASTERV2", "description": "Allows CLASS_MASTERV2 actions in SCHOOL module", "dependsOn": []}, {"name": "PERMIT_ASSING_STOP", "friendlyName": "PERMIT_ASSING_STOP", "description": "Allows PERMIT_ASSING_STOP actions in SCHOOL module", "dependsOn": []}, {"name": "CLASS_SECTION", "friendlyName": "CLASS_SECTION", "description": "Allows CLASS_SECTION actions in SCHOOL module", "dependsOn": []}, {"name": "SEMESTER", "friendlyName": "SEMESTER", "description": "Allows SEMESTER actions in SCHOOL module", "dependsOn": []}, {"name": "ORDER_OF_SECTION", "friendlyName": "ORDER_OF_SECTION", "description": "Allows ORDER_OF_SECTION actions in SCHOOL module", "dependsOn": []}, {"name": "ORDER_OF_CLASS", "friendlyName": "ORDER_OF_CLASS", "description": "Allows ORDER_OF_CLASS actions in SCHOOL module", "dependsOn": []}, {"name": "ORDER_OF_MASTER_CLASS", "friendlyName": "ORDER_OF_MASTER_CLASS", "description": "Allows ORDER_OF_MASTER_CLASS actions in SCHOOL module", "dependsOn": []}, {"name": "CHANGE_CASE_OF_NAME", "friendlyName": "CHANGE_CASE_OF_NAME", "description": "Allows CHANGE_CASE_OF_NAME actions in SCHOOL module", "dependsOn": []}]}, {"name": "SCHOOL_CALENDAR", "friendlyName": "SCHOOL_CALENDAR", "description": "Module for SCHOOL_CALENDAR management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in SCHOOL_CALENDAR module", "dependsOn": []}]}, {"name": "STAFF", "friendlyName": "STAFF", "description": "Module for STAFF management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "ADD_EDIT_DELETE", "friendlyName": "ADD_EDIT_DELETE", "description": "Allows ADD_EDIT_DELETE actions in STAFF module", "dependsOn": []}, {"name": "VIEW_DETAILS", "friendlyName": "VIEW_DETAILS", "description": "Allows VIEW_DETAILS actions in STAFF module", "dependsOn": []}, {"name": "VIEW", "friendlyName": "VIEW", "description": "Allows VIEW actions in STAFF module", "dependsOn": []}, {"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF module", "dependsOn": []}, {"name": "VIEW_SMS_REPORT", "friendlyName": "VIEW_SMS_REPORT", "description": "Allows VIEW_SMS_REPORT actions in STAFF module", "dependsOn": []}, {"name": "VIEW_REPORT", "friendlyName": "VIEW_REPORT", "description": "Allows VIEW_REPORT actions in STAFF module", "dependsOn": []}, {"name": "VIEW_QUALIFICATION", "friendlyName": "VIEW_QUALIFICATION", "description": "Allows VIEW_QUALIFICATION actions in STAFF module", "dependsOn": []}, {"name": "ATTENDANCE", "friendlyName": "ATTENDANCE", "description": "Allows ATTENDANCE actions in STAFF module", "dependsOn": []}, {"name": "PROVISION_USER_LOGIN", "friendlyName": "PROVISION_USER_LOGIN", "description": "Allows PROVISION_USER_LOGIN actions in STAFF module", "dependsOn": []}, {"name": "REPORTING_MANAGER_ADMIN", "friendlyName": "REPORTING_MANAGER_ADMIN", "description": "Allows REPORTING_MANAGER_ADMIN actions in STAFF module", "dependsOn": []}, {"name": "PAYROLL_DETAILS", "friendlyName": "PAYROLL_DETAILS", "description": "Allows PAYROLL_DETAILS actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_DOCUMENTS", "friendlyName": "MANAGE_DOCUMENTS", "description": "Allows MANAGE_DOCUMENTS actions in STAFF module", "dependsOn": []}, {"name": "STAFF_TRANING_WORKSHOP", "friendlyName": "STAFF_TRANING_WORKSHOP", "description": "Allows STAFF_TRANING_WORKSHOP actions in STAFF module", "dependsOn": []}, {"name": "STAFF_PUBLICATIONS_CITAT", "friendlyName": "STAFF_PUBLICATIONS_CITAT", "description": "Allows STAFF_PUBLICATIONS_CITAT actions in STAFF module", "dependsOn": []}, {"name": "STAFF_INTERESTS", "friendlyName": "STAFF_INTERESTS", "description": "Allows STAFF_INTERESTS actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_EXPERIENCE", "friendlyName": "MANAGE_EXPERIENCE", "description": "Allows MANAGE_EXPERIENCE actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_AWARDS", "friendlyName": "MANAGE_AWARDS", "description": "Allows MANAGE_AWARDS actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_QUALIFICATION", "friendlyName": "MANAGE_QUALIFICATION", "description": "Allows MANAGE_QUALIFICATION actions in STAFF module", "dependsOn": []}, {"name": "STAFF_INITIATIVES", "friendlyName": "STAFF_INITIATIVES", "description": "Allows STAFF_INITIATIVES actions in STAFF module", "dependsOn": []}, {"name": "STAFF_EXIT", "friendlyName": "STAFF_EXIT", "description": "Allows STAFF_EXIT actions in STAFF module", "dependsOn": []}, {"name": "VIEW_AUDIT_REPORT", "friendlyName": "VIEW_AUDIT_REPORT", "description": "Allows VIEW_AUDIT_REPORT actions in STAFF module", "dependsOn": []}, {"name": "STAFF_DOCUMENT_REPORT", "friendlyName": "STAFF_DOCUMENT_REPORT", "description": "Allows STAFF_DOCUMENT_REPORT actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_PROFILE_DISPLAY", "friendlyName": "MANAGE_PROFILE_DISPLAY", "description": "Allows MANAGE_PROFILE_DISPLAY actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_PROFILE_EDITS", "friendlyName": "MANAGE_PROFILE_EDITS", "description": "Allows MANAGE_PROFILE_EDITS actions in STAFF module", "dependsOn": []}, {"name": "APPROVE_ATTRIBUTE_UPDATES", "friendlyName": "APPROVE_ATTRIBUTE_UPDATES", "description": "Allows APPROVE_ATTRIBUTE_UPDATES actions in STAFF module", "dependsOn": []}, {"name": "PROVISION_STAFF_NAME_CHANGE", "friendlyName": "PROVISION_STAFF_NAME_CHANGE", "description": "Allows PROVISION_STAFF_NAME_CHANGE actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_DEPARTMENTS", "friendlyName": "MANAGE_DEPARTMENTS", "description": "Allows MANAGE_DEPARTMENTS actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_DESIGNATIONS", "friendlyName": "MANAGE_DESIGNATIONS", "description": "Allows MANAGE_DESIGNATIONS actions in STAFF module", "dependsOn": []}, {"name": "STAFF_DATA_MISSING_REPORT", "friendlyName": "STAFF_DATA_MISSING_REPORT", "description": "Allows STAFF_DATA_MISSING_REPORT actions in STAFF module", "dependsOn": []}, {"name": "STAFF_EDIT_HISTORY_REPORT", "friendlyName": "STAFF_EDIT_HISTORY_REPORT", "description": "Allows STAFF_EDIT_HISTORY_REPORT actions in STAFF module", "dependsOn": []}, {"name": "PROVIDE_CONSENT", "friendlyName": "PROVIDE_CONSENT", "description": "Allows PROVIDE_CONSENT actions in STAFF module", "dependsOn": []}, {"name": "MANAGE_STAFF_CONSENT", "friendlyName": "MANAGE_STAFF_CONSENT", "description": "Allows MANAGE_STAFF_CONSENT actions in STAFF module", "dependsOn": []}, {"name": "CONFIGURE_STAFF_PROFILE_FIELDS", "friendlyName": "CONFIGURE_STAFF_PROFILE_FIELDS", "description": "Allows CONFIGURE_STAFF_PROFILE_FIELDS actions in STAFF module", "dependsOn": []}, {"name": "CONFIGURE_LOCK_UNLOCK_STAFF_PROFILE", "friendlyName": "CONFIGURE_LOCK_UNLOCK_STAFF_PROFILE", "description": "Allows CONFIGURE_LOCK_UNLOCK_STAFF_PROFILE actions in STAFF module", "dependsOn": []}, {"name": "DOCUMENTS_MASS_UPLOAD", "friendlyName": "DOCUMENTS_MASS_UPLOAD", "description": "Allows DOCUMENTS_MASS_UPLOAD actions in STAFF module", "dependsOn": []}]}, {"name": "STAFF_360_DEGREE", "friendlyName": "STAFF_360_DEGREE", "description": "Module for STAFF_360_DEGREE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "ATTENDANCE", "friendlyName": "ATTENDANCE", "description": "Allows ATTENDANCE actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "LEAVE_RECORDS", "friendlyName": "LEAVE_RECORDS", "description": "Allows LEAVE_RECORDS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "ASSIGNED_TASKS", "friendlyName": "ASSIGNED_TASKS", "description": "Allows ASSIGNED_TASKS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "QUALIFICATION", "friendlyName": "QUALIFICATION", "description": "Allows QUALIFICATION actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "AWARDS", "friendlyName": "AWARDS", "description": "Allows AWARDS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "EXPERIENCE", "friendlyName": "EXPERIENCE", "description": "Allows EXPERIENCE actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "DOCUMENTS", "friendlyName": "DOCUMENTS", "description": "Allows DOCUMENTS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "TRAININGS", "friendlyName": "TRAININGS", "description": "Allows TRAININGS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "INTERESTS", "friendlyName": "INTERESTS", "description": "Allows INTERESTS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "PUBLICATIONS", "friendlyName": "PUBLICATIONS", "description": "Allows PUBLICATIONS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "CIRCULARS", "friendlyName": "CIRCULARS", "description": "Allows CIRCULARS actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "MESSAGES", "friendlyName": "MESSAGES", "description": "Allows MESSAGES actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "INITIATIVES", "friendlyName": "INITIATIVES", "description": "Allows INITIATIVES actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "INFIRMARY", "friendlyName": "INFIRMARY", "description": "Allows INFIRMARY actions in STAFF_360_DEGREE module", "dependsOn": []}, {"name": "PAYROLL_DETAILS", "friendlyName": "PAYROLL_DETAILS", "description": "Allows PAYROLL_DETAILS actions in STAFF_360_DEGREE module", "dependsOn": []}]}, {"name": "STAFF_ATTENDANCE", "friendlyName": "STAFF_ATTENDANCE", "description": "Module for STAFF_ATTENDANCE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "CREATE_SHIFT", "friendlyName": "CREATE_SHIFT", "description": "Allows CREATE_SHIFT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "ASSIGN_SHIFT", "friendlyName": "ASSIGN_SHIFT", "description": "Allows ASSIGN_SHIFT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "MANAGE_ATTENDANCE", "friendlyName": "MANAGE_ATTENDANCE", "description": "Allows MANAGE_ATTENDANCE actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "DAY_WISE_REPORT", "friendlyName": "DAY_WISE_REPORT", "description": "Allows DAY_WISE_REPORT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "LATE_REPORT", "friendlyName": "LATE_REPORT", "description": "Allows LATE_REPORT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "INDIVIDUAL_STAFF_REPORT", "friendlyName": "INDIVIDUAL_STAFF_REPORT", "description": "Allows INDIVIDUAL_STAFF_REPORT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "OVERRIDE_REPORT", "friendlyName": "OVERRIDE_REPORT", "description": "Allows OVERRIDE_REPORT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "REDRIVE_ATTENDANCE", "friendlyName": "REDRIVE_ATTENDANCE", "description": "Allows REDRIVE_ATTENDANCE actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "MANAGE_GEOFENCE", "friendlyName": "MANAGE_GEOFENCE", "description": "Allows MANAGE_GEOFENCE actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "RECTIFY_ATTENDANCE_TOOL", "friendlyName": "RECTIFY_ATTENDANCE_TOOL", "description": "Allows RECTIFY_ATTENDANCE_TOOL actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "DEDUPE_ATTENDANCE_TOOL", "friendlyName": "DEDUPE_ATTENDANCE_TOOL", "description": "Allows DEDUPE_ATTENDANCE_TOOL actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "CHECK_IN_REPORT", "friendlyName": "CHECK_IN_REPORT", "description": "Allows CHECK_IN_REPORT actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "STAFF_REGULARIZE_LEAVE", "friendlyName": "STAFF_REGULARIZE_LEAVE", "description": "Allows STAFF_REGULARIZE_LEAVE actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "EXPORT_PERMISSION", "friendlyName": "EXPORT_PERMISSION", "description": "Allows EXPORT_PERMISSION actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "EDIT_ATTENDANCE", "friendlyName": "EDIT_ATTENDANCE", "description": "Allows EDIT_ATTENDANCE actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "MODIFY_PREVIOUS_SHIFTS", "friendlyName": "MODIFY_PREVIOUS_SHIFTS", "description": "Allows MODIFY_PREVIOUS_SHIFTS actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "STAFF_ATTENDANCE_APPROVE_FACE_CHECKIN", "friendlyName": "STAFF_ATTENDANCE_APPROVE_FACE_CHECKIN", "description": "Allows STAFF_ATTENDANCE_APPROVE_FACE_CHECKIN actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "STAFF_ATTENDANCE_EXCEPTION_ATTENDANCE_APPROVAL", "friendlyName": "STAFF_ATTENDANCE_EXCEPTION_ATTENDANCE_APPROVAL", "description": "Allows STAFF_ATTENDANCE_EXCEPTION_ATTENDANCE_APPROVAL actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "STAFF_ATTENDANCE_USE_LOCATION_CHECKIN", "friendlyName": "STAFF_ATTENDANCE_USE_LOCATION_CHECKIN", "description": "Allows STAFF_ATTENDANCE_USE_LOCATION_CHECKIN actions in STAFF_ATTENDANCE module", "dependsOn": []}, {"name": "STAFF_ATTENDANCE_USE_FACE_CHECKIN", "friendlyName": "STAFF_ATTENDANCE_USE_FACE_CHECKIN", "description": "Allows STAFF_ATTENDANCE_USE_FACE_CHECKIN actions in STAFF_ATTENDANCE module", "dependsOn": []}]}, {"name": "STAFF_MASSUPDATE", "friendlyName": "STAFF_MASSUPDATE", "description": "Module for STAFF_MASSUPDATE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF_MASSUPDATE module", "dependsOn": []}, {"name": "MASS_DOCUMENT_UPLOAD", "friendlyName": "MASS_DOCUMENT_UPLOAD", "description": "Allows MASS_DOCUMENT_UPLOAD actions in STAFF_MASSUPDATE module", "dependsOn": []}]}, {"name": "STAFF_OBSERVATION", "friendlyName": "STAFF_OBSERVATION", "description": "Module for STAFF_OBSERVATION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "CREATE", "friendlyName": "CREATE", "description": "Allows CREATE actions in STAFF_OBSERVATION module", "dependsOn": []}, {"name": "VIEW_SUMMARY", "friendlyName": "VIEW_SUMMARY", "description": "Allows VIEW_SUMMARY actions in STAFF_OBSERVATION module", "dependsOn": []}, {"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF_OBSERVATION module", "dependsOn": []}, {"name": "VIEW_ALL", "friendlyName": "VIEW_ALL", "description": "Allows VIEW_ALL actions in STAFF_OBSERVATION module", "dependsOn": []}, {"name": "VIEW", "friendlyName": "VIEW", "description": "Allows VIEW actions in STAFF_OBSERVATION module", "dependsOn": []}]}, {"name": "STAFF_PROFILE", "friendlyName": "STAFF_PROFILE", "description": "Module for STAFF_PROFILE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to STAFF_PROFILE module", "dependsOn": []}, {"name": "VIEW", "friendlyName": "VIEW", "description": "Allows VIEW actions in STAFF_PROFILE module", "dependsOn": []}]}, {"name": "STAFF_TASKS_BASKET", "friendlyName": "STAFF_TASKS_BASKET", "description": "Module for STAFF_TASKS_BASKET management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF_TASKS_BASKET module", "dependsOn": []}, {"name": "MY_PROJECTS", "friendlyName": "MY_PROJECTS", "description": "Allows MY_PROJECTS actions in STAFF_TASKS_BASKET module", "dependsOn": []}, {"name": "MY_TASKS", "friendlyName": "MY_TASKS", "description": "Allows MY_TASKS actions in STAFF_TASKS_BASKET module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in STAFF_TASKS_BASKET module", "dependsOn": []}, {"name": "MANAGE_TEAMS", "friendlyName": "MANAGE_TEAMS", "description": "Allows MANAGE_TEAMS actions in STAFF_TASKS_BASKET module", "dependsOn": []}, {"name": "MANAGE_STAFF_TASK_TYPES", "friendlyName": "MANAGE_STAFF_TASK_TYPES", "description": "Allows MANAGE_STAFF_TASK_TYPES actions in STAFF_TASKS_BASKET module", "dependsOn": []}]}, {"name": "STUDENT", "friendlyName": "STUDENT", "description": "Module for STUDENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "ADDRESS_CRUD", "friendlyName": "ADDRESS_CRUD", "description": "Allows ADDRESS_CRUD actions in STUDENT module", "dependsOn": []}, {"name": "HEALTH_CRUD", "friendlyName": "HEALTH_CRUD", "description": "Allows HEALTH_CRUD actions in STUDENT module", "dependsOn": []}, {"name": "BASIC_VIEW", "friendlyName": "BASIC_VIEW", "description": "Allows BASIC_VIEW actions in STUDENT module", "dependsOn": []}, {"name": "DETAIL_VIEW", "friendlyName": "DETAIL_VIEW", "description": "Allows DETAIL_VIEW actions in STUDENT module", "dependsOn": []}, {"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT module", "dependsOn": []}, {"name": "COMMUNICATION_CRUD", "friendlyName": "COMMUNICATION_CRUD", "description": "Allows COMMUNICATION_CRUD actions in STUDENT module", "dependsOn": []}, {"name": "FULL_INFO_CRUD", "friendlyName": "FULL_INFO_CRUD", "description": "Allows FULL_INFO_CRUD actions in STUDENT module", "dependsOn": []}, {"name": "NEW_STUDENT_ADD", "friendlyName": "NEW_STUDENT_ADD", "description": "Allows NEW_STUDENT_ADD actions in STUDENT module", "dependsOn": []}, {"name": "MOTHER_TONGUE_CRUD", "friendlyName": "MOTHER_TONGUE_CRUD", "description": "Allows MOTHER_TONGUE_CRUD actions in STUDENT module", "dependsOn": []}, {"name": "MOTHER_TONGUE_CRUD_IF_CT", "friendlyName": "MOTHER_TONGUE_CRUD_IF_CT", "description": "Allows MOTHER_TONGUE_CRUD_IF_CT actions in STUDENT module", "dependsOn": []}, {"name": "HEALTH_CRUD_IF_CT", "friendlyName": "HEALTH_CRUD_IF_CT", "description": "Allows HEALTH_CRUD_IF_CT actions in STUDENT module", "dependsOn": []}, {"name": "ADDRESS_CRUD_IF_CT", "friendlyName": "ADDRESS_CRUD_IF_CT", "description": "Allows ADDRESS_CRUD_IF_CT actions in STUDENT module", "dependsOn": []}, {"name": "COMMUNICATION_CRUD_IF_CT", "friendlyName": "COMMUNICATION_CRUD_IF_CT", "description": "Allows COMMUNICATION_CRUD_IF_CT actions in STUDENT module", "dependsOn": []}, {"name": "DETAIL_VIEW_IF_CT", "friendlyName": "DETAIL_VIEW_IF_CT", "description": "Allows DETAIL_VIEW_IF_CT actions in STUDENT module", "dependsOn": []}, {"name": "ADD_SIBLINGS", "friendlyName": "ADD_SIBLINGS", "description": "Allows ADD_SIBLINGS actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_SMS_REPORT", "friendlyName": "VIEW_SMS_REPORT", "description": "Allows VIEW_SMS_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_REPORT", "friendlyName": "VIEW_REPORT", "description": "Allows VIEW_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_CLASS_REPORT", "friendlyName": "VIEW_CLASS_REPORT", "description": "Allows VIEW_CLASS_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "SCHOOL_DETAILS", "friendlyName": "SCHOOL_DETAILS", "description": "Allows SCHOOL_DETAILS actions in STUDENT module", "dependsOn": []}, {"name": "DOCUMENTS", "friendlyName": "DOCUMENTS", "description": "Allows DOCUMENTS actions in STUDENT module", "dependsOn": []}, {"name": "TEMP_PASSWORD_RESET", "friendlyName": "TEMP_PASSWORD_RESET", "description": "Allows TEMP_PASSWORD_RESET actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT", "friendlyName": "VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT", "description": "Allows VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "GUARDIAN_INFO_CRUD", "friendlyName": "GUARDIAN_INFO_CRUD", "description": "Allows GUARDIAN_INFO_CRUD actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_ALUMNI_LIST", "friendlyName": "VIEW_ALUMNI_LIST", "description": "Allows VIEW_ALUMNI_LIST actions in STUDENT module", "dependsOn": []}, {"name": "EXIT_STUDENT", "friendlyName": "EXIT_STUDENT", "description": "Allows EXIT_STUDENT actions in STUDENT module", "dependsOn": []}, {"name": "CATEGORY_REPORT", "friendlyName": "CATEGORY_REPORT", "description": "Allows CATEGORY_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "TEMP_DEACTIVATION", "friendlyName": "TEMP_DEACTIVATION", "description": "Allows TEMP_DEACTIVATION actions in STUDENT module", "dependsOn": []}, {"name": "ADJUST_FEE_AMOUNT", "friendlyName": "ADJUST_FEE_AMOUNT", "description": "Allows ADJUST_FEE_AMOUNT actions in STUDENT module", "dependsOn": []}, {"name": "ASSIGN_FEES", "friendlyName": "ASSIGN_FEES", "description": "Allows ASSIGN_FEES actions in STUDENT module", "dependsOn": []}, {"name": "CONNECT_SIBLINGS", "friendlyName": "CONNECT_SIBLINGS", "description": "Allows CONNECT_SIBLINGS actions in STUDENT module", "dependsOn": []}, {"name": "PROVISION_PARENTS_CREDENTIALS", "friendlyName": "PROVISION_PARENTS_CREDENTIALS", "description": "Allows PROVISION_PARENTS_CREDENTIALS actions in STUDENT module", "dependsOn": []}, {"name": "MANAGE_ONLINE_CREDENTIALS", "friendlyName": "MANAGE_ONLINE_CREDENTIALS", "description": "Allows MANAGE_ONLINE_CREDENTIALS actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_TRANSPORT_REPORT", "friendlyName": "VIEW_TRANSPORT_REPORT", "description": "Allows VIEW_TRANSPORT_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "VIEW_VACCINATION_STATUS", "friendlyName": "VIEW_VACCINATION_STATUS", "description": "Allows VIEW_VACCINATION_STATUS actions in STUDENT module", "dependsOn": []}, {"name": "FEE_PAID_REPORT", "friendlyName": "FEE_PAID_REPORT", "description": "Allows FEE_PAID_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "CATEGORY_WISE_STUDENT_REPORT", "friendlyName": "CATEGORY_WISE_STUDENT_REPORT", "description": "Allows CATEGORY_WISE_STUDENT_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "MAP_RFID", "friendlyName": "MAP_RFID", "description": "Allows MAP_RFID actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_DOCUMENT_REPORT", "friendlyName": "STUDENT_DOCUMENT_REPORT", "description": "Allows STUDENT_DOCUMENT_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_AADHAR_REPORT", "friendlyName": "STUDENT_AADHAR_REPORT", "description": "Allows STUDENT_AADHAR_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_PAN_REPORT", "friendlyName": "STUDENT_PAN_REPORT", "description": "Allows STUDENT_PAN_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_PREVIOUS_SCHOOL_REPORT", "friendlyName": "STUDENT_PREVIOUS_SCHOOL_REPORT", "description": "Allows STUDENT_PREVIOUS_SCHOOL_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_DATA_MISSING_REPORT", "friendlyName": "STUDENT_DATA_MISSING_REPORT", "description": "Allows STUDENT_DATA_MISSING_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_DOCUMENTS_DELETE", "friendlyName": "STUDENT_DOCUMENTS_DELETE", "description": "Allows STUDENT_DOCUMENTS_DELETE actions in STUDENT module", "dependsOn": []}, {"name": "STUDENT_EDIT_HISTORY_REPORT", "friendlyName": "STUDENT_EDIT_HISTORY_REPORT", "description": "Allows STUDENT_EDIT_HISTORY_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "DOCUMENTS_MASS_UPLOAD", "friendlyName": "DOCUMENTS_MASS_UPLOAD", "description": "Allows DOCUMENTS_MASS_UPLOAD actions in STUDENT module", "dependsOn": []}, {"name": "MASS_ASSIGN_CLASS_SECTION", "friendlyName": "MASS_ASSIGN_CLASS_SECTION", "description": "Allows MASS_ASSIGN_CLASS_SECTION actions in STUDENT module", "dependsOn": []}, {"name": "SINGLE_WINDOW_REPORT", "friendlyName": "SINGLE_WINDOW_REPORT", "description": "Allows SINGLE_WINDOW_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "CONFIGURE_PARENT_SIDE_FIELDS", "friendlyName": "CONFIGURE_PARENT_SIDE_FIELDS", "description": "Allows CONFIGURE_PARENT_SIDE_FIELDS actions in STUDENT module", "dependsOn": []}, {"name": "LOCK_UNLOCK_PROFILE", "friendlyName": "LOCK_UNLOCK_PROFILE", "description": "Allows LOCK_UNLOCK_PROFILE actions in STUDENT module", "dependsOn": []}, {"name": "SIBLING_REPORT", "friendlyName": "SIBLING_REPORT", "description": "Allows SIBLING_REPORT actions in STUDENT module", "dependsOn": []}, {"name": "DOCUMENTS_UPLOAD_REMINDER_EMAIL", "friendlyName": "DOCUMENTS_UPLOAD_REMINDER_EMAIL", "description": "Allows DOCUMENTS_UPLOAD_REMINDER_EMAIL actions in STUDENT module", "dependsOn": []}, {"name": "PHOTO_DOCS_MASS_UPLOAD", "friendlyName": "PHOTO_DOCS_MASS_UPLOAD", "description": "Allows PHOTO_DOCS_MASS_UPLOAD actions in STUDENT module", "dependsOn": []}, {"name": "MANAGE_IDCARD_INFO", "friendlyName": "MANAGE_IDCARD_INFO", "description": "Allows MANAGE_IDCARD_INFO actions in STUDENT module", "dependsOn": []}]}, {"name": "STUDENT_360", "friendlyName": "STUDENT_360", "description": "Module for STUDENT_360 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_360 module", "dependsOn": []}, {"name": "SCHOOL_DETAILS", "friendlyName": "SCHOOL_DETAILS", "description": "Allows SCHOOL_DETAILS actions in STUDENT_360 module", "dependsOn": []}, {"name": "DOCUMENTS", "friendlyName": "DOCUMENTS", "description": "Allows DOCUMENTS actions in STUDENT_360 module", "dependsOn": []}, {"name": "PREV_SCHOOL_DETAILS", "friendlyName": "PREV_SCHOOL_DETAILS", "description": "Allows PREV_SCHOOL_DETAILS actions in STUDENT_360 module", "dependsOn": []}, {"name": "STUDENT_HEALTH", "friendlyName": "STUDENT_HEALTH", "description": "Allows STUDENT_HEALTH actions in STUDENT_360 module", "dependsOn": []}, {"name": "STUDENT_MEDICAL_FORM", "friendlyName": "STUDENT_MEDICAL_FORM", "description": "Allows STUDENT_MEDICAL_FORM actions in STUDENT_360 module", "dependsOn": []}, {"name": "STUDENT_CONSENT_FORMS", "friendlyName": "STUDENT_CONSENT_FORMS", "description": "Allows STUDENT_CONSENT_FORMS actions in STUDENT_360 module", "dependsOn": []}, {"name": "FEES", "friendlyName": "FEES", "description": "Allows FEES actions in STUDENT_360 module", "dependsOn": []}, {"name": "CIRCULARS", "friendlyName": "CIRCULARS", "description": "Allows CIRCULARS actions in STUDENT_360 module", "dependsOn": []}, {"name": "SMS", "friendlyName": "SMS", "description": "Allows SMS actions in STUDENT_360 module", "dependsOn": []}, {"name": "ATTENDANCE", "friendlyName": "ATTENDANCE", "description": "Allows ATTENDANCE actions in STUDENT_360 module", "dependsOn": []}, {"name": "EXAMINATION", "friendlyName": "EXAMINATION", "description": "Allows EXAMINATION actions in STUDENT_360 module", "dependsOn": []}, {"name": "OBSERVATION", "friendlyName": "OBSERVATION", "description": "Allows OBSERVATION actions in STUDENT_360 module", "dependsOn": []}, {"name": "ACADEMICANALYSIS", "friendlyName": "ACADEMICANALYSIS", "description": "Allows ACADEMICANALYSIS actions in STUDENT_360 module", "dependsOn": []}, {"name": "NON_COMPLIANCE", "friendlyName": "NON_COMPLIANCE", "description": "Allows NON_COMPLIANCE actions in STUDENT_360 module", "dependsOn": []}, {"name": "TRANSPORTATION", "friendlyName": "TRANSPORTATION", "description": "Allows TRANSPORTATION actions in STUDENT_360 module", "dependsOn": []}, {"name": "RFID_REPORT", "friendlyName": "RFID_REPORT", "description": "Allows RFID_REPORT actions in STUDENT_360 module", "dependsOn": []}, {"name": "SINGLE_WINDOW_PROCESS", "friendlyName": "SINGLE_WINDOW_PROCESS", "description": "Allows SINGLE_WINDOW_PROCESS actions in STUDENT_360 module", "dependsOn": []}, {"name": "PERSONAL_DETAILS_EXTENDED", "friendlyName": "PERSONAL_DETAILS_EXTENDED", "description": "Allows PERSONAL_DETAILS_EXTENDED actions in STUDENT_360 module", "dependsOn": []}]}, {"name": "STUDENT_ATTENDANCE", "friendlyName": "STUDENT_ATTENDANCE", "description": "Module for STUDENT_ATTENDANCE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "SHOW_ALL_SECTIONS", "friendlyName": "SHOW_ALL_SECTIONS", "description": "Allows SHOW_ALL_SECTIONS actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "TAKE", "friendlyName": "TAKE", "description": "Allows TAKE actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "EMERGENCY_EXIT", "friendlyName": "EMERGENCY_EXIT", "description": "Allows EMERGENCY_EXIT actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "HEALTH_CARE", "friendlyName": "HEALTH_CARE", "description": "Allows HEALTH_CARE actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "LATECOMER", "friendlyName": "LATECOMER", "description": "Allows LATECOMER actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "FRESH_ENTRY", "friendlyName": "FRESH_ENTRY", "description": "Allows FRESH_ENTRY actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "EDIT_ANY_DATE_ATTENDANCE", "friendlyName": "EDIT_ANY_DATE_ATTENDANCE", "description": "Allows EDIT_ANY_DATE_ATTENDANCE actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "VIEW_SUMMARY", "friendlyName": "VIEW_SUMMARY", "description": "Allows VIEW_SUMMARY actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "CLASS_ATTENDANCE_REPORT", "friendlyName": "CLASS_ATTENDANCE_REPORT", "description": "Allows CLASS_ATTENDANCE_REPORT actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "SPECIAL_CASE_REPORT", "friendlyName": "SPECIAL_CASE_REPORT", "description": "Allows SPECIAL_CASE_REPORT actions in STUDENT_ATTENDANCE module", "dependsOn": []}, {"name": "ATTENDANCE_NOT_TAKEN_REPORT", "friendlyName": "ATTENDANCE_NOT_TAKEN_REPORT", "description": "Allows ATTENDANCE_NOT_TAKEN_REPORT actions in STUDENT_ATTENDANCE module", "dependsOn": []}]}, {"name": "STUDENT_ATTENDANCE_V2", "friendlyName": "STUDENT_ATTENDANCE_V2", "description": "Module for STUDENT_ATTENDANCE_V2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to STUDENT_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "EDIT_ATTENDANCE_ALLSUBJECTS", "friendlyName": "EDIT_ATTENDANCE_ALLSUBJECTS", "description": "Allows EDIT_ATTENDANCE_ALLSUBJECTS actions in STUDENT_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "TAKE_ATTENDANCE", "friendlyName": "TAKE_ATTENDANCE", "description": "Allows TAKE_ATTENDANCE actions in STUDENT_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in STUDENT_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "EDIT_ATTENDANCE_SUBJECTWISE", "friendlyName": "EDIT_ATTENDANCE_SUBJECTWISE", "description": "Allows EDIT_ATTENDANCE_SUBJECTWISE actions in STUDENT_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "TAKE_PREVIOUS_DATE_ATTENDANCE", "friendlyName": "TAKE_PREVIOUS_DATE_ATTENDANCE", "description": "Allows TAKE_PREVIOUS_DATE_ATTENDANCE actions in STUDENT_ATTENDANCE_V2 module", "dependsOn": []}]}, {"name": "CALENDAR_EVENTS_V2", "friendlyName": "CALENDAR_EVENTS_V2", "description": "Module for CALENDAR_EVENTS_V2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in CALENDAR_EVENTS_V2 module", "dependsOn": []}, {"name": "MANAGE_CALENDAR", "friendlyName": "MANAGE_CALENDAR", "description": "Allows MANAGE_CALENDAR actions in CALENDAR_EVENTS_V2 module", "dependsOn": []}, {"name": "ASSIGN_CALENDAR", "friendlyName": "ASSIGN_CALENDAR", "description": "Allows ASSIGN_CALENDAR actions in CALENDAR_EVENTS_V2 module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in CALENDAR_EVENTS_V2 module", "dependsOn": []}, {"name": "ENABLE_CONFIG", "friendlyName": "ENABLE_CONFIG", "description": "Allows ENABLE_CONFIG actions in CALENDAR_EVENTS_V2 module", "dependsOn": []}]}, {"name": "STUDENT_MASSUPDATE", "friendlyName": "STUDENT_MASSUPDATE", "description": "Module for STUDENT_MASSUPDATE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_MASSUPDATE module", "dependsOn": []}, {"name": "STUDENT_ADDRESS_MASSUPDATE", "friendlyName": "STUDENT_ADDRESS_MASSUPDATE", "description": "Allows STUDENT_ADDRESS_MASSUPDATE actions in STUDENT_MASSUPDATE module", "dependsOn": []}]}, {"name": "STUDENT_OBSERVATION", "friendlyName": "STUDENT_OBSERVATION", "description": "Module for STUDENT_OBSERVATION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "ADD", "friendlyName": "ADD", "description": "Allows ADD actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "DEACTIVATE", "friendlyName": "DEACTIVATE", "description": "Allows DEACTIVATE actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "OBSERVATION_REPORT", "friendlyName": "OBSERVATION_REPORT", "description": "Allows OBSERVATION_REPORT actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "SECTION_SUMMARY_REPORT", "friendlyName": "SECTION_SUMMARY_REPORT", "description": "Allows SECTION_SUMMARY_REPORT actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "STUDENT_OBSERVATION_REPORT", "friendlyName": "STUDENT_OBSERVATION_REPORT", "description": "Allows STUDENT_OBSERVATION_REPORT actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "MANAGE_CATEGORIES", "friendlyName": "MANAGE_CATEGORIES", "description": "Allows MANAGE_CATEGORIES actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "MANAGE_CAPACITY", "friendlyName": "MANAGE_CAPACITY", "description": "Allows MANAGE_CAPACITY actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "PREDEFINED_REMARKS", "friendlyName": "PREDEFINED_REMARKS", "description": "Allows PREDEFINED_REMARKS actions in STUDENT_OBSERVATION module", "dependsOn": []}, {"name": "MASS_OBSERVATION", "friendlyName": "MASS_OBSERVATION", "description": "Allows MASS_OBSERVATION actions in STUDENT_OBSERVATION module", "dependsOn": []}]}, {"name": "STUDENT_NONCOMPLIANCE", "friendlyName": "STUDENT_NONCOMPLIANCE", "description": "Module for STUDENT_NONCOMPLIANCE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "ADD", "friendlyName": "ADD", "description": "Allows ADD actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "DEACTIVATE", "friendlyName": "DEACTIVATE", "description": "Allows DEACTIVATE actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "MANAGE_CATEGORIES", "friendlyName": "MANAGE_CATEGORIES", "description": "Allows MANAGE_CATEGORIES actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "MANAGE_PENALTY", "friendlyName": "MANAGE_PENALTY", "description": "Allows MANAGE_PENALTY actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "NONCOMPLIANCE_REPORT", "friendlyName": "NONCOMPLIANCE_REPORT", "description": "Allows NONCOMPLIANCE_REPORT actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "STUDENT_NONCOMPLIANCE_REPORT", "friendlyName": "STUDENT_NONCOMPLIANCE_REPORT", "description": "Allows STUDENT_NONCOMPLIANCE_REPORT actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "SECTION_SUMMARY_REPORT", "friendlyName": "SECTION_SUMMARY_REPORT", "description": "Allows SECTION_SUMMARY_REPORT actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}, {"name": "RESOLVE_ANY_NON_COMPLIANCE", "friendlyName": "RESOLVE_ANY_NON_COMPLIANCE", "description": "Allows RESOLVE_ANY_NON_COMPLIANCE actions in STUDENT_NONCOMPLIANCE module", "dependsOn": []}]}, {"name": "STUDENT_PROFILE", "friendlyName": "STUDENT_PROFILE", "description": "Module for STUDENT_PROFILE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW", "friendlyName": "VIEW", "description": "Allows VIEW actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_ACTIVITY_DETAILS", "friendlyName": "VIEW_ACTIVITY_DETAILS", "description": "Allows VIEW_ACTIVITY_DETAILS actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_EMERGENCY_INFO", "friendlyName": "VIEW_EMERGENCY_INFO", "description": "Allows VIEW_EMERGENCY_INFO actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_FEES_DETAILS", "friendlyName": "VIEW_FEES_DETAILS", "description": "Allows VIEW_FEES_DETAILS actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_HEALTH_DETAILS", "friendlyName": "VIEW_HEALTH_DETAILS", "description": "Allows VIEW_HEALTH_DETAILS actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_PERSONAL_DETAILS", "friendlyName": "VIEW_PERSONAL_DETAILS", "description": "Allows VIEW_PERSONAL_DETAILS actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_SCHOOL_DETAILS", "friendlyName": "VIEW_SCHOOL_DETAILS", "description": "Allows VIEW_SCHOOL_DETAILS actions in STUDENT_PROFILE module", "dependsOn": []}, {"name": "VIEW_TRANSPORT_DETAILS", "friendlyName": "VIEW_TRANSPORT_DETAILS", "description": "Allows VIEW_TRANSPORT_DETAILS actions in STUDENT_PROFILE module", "dependsOn": []}]}, {"name": "STUDENT_PROMOTION", "friendlyName": "STUDENT_PROMOTION", "description": "Module for STUDENT_PROMOTION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_PROMOTION module", "dependsOn": []}]}, {"name": "SUBJECT", "friendlyName": "SUBJECT", "description": "Module for SUBJECT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to SUBJECT module", "dependsOn": []}, {"name": "MASTER", "friendlyName": "MASTER", "description": "Allows MASTER actions in SUBJECT module", "dependsOn": []}, {"name": "ELECTIVE_MASTER", "friendlyName": "ELECTIVE_MASTER", "description": "Allows ELECTIVE_MASTER actions in SUBJECT module", "dependsOn": []}, {"name": "STUDENT_ELECTIVE", "friendlyName": "STUDENT_ELECTIVE", "description": "Allows STUDENT_ELECTIVE actions in SUBJECT module", "dependsOn": []}]}, {"name": "SUBSTITUTION", "friendlyName": "SUBSTITUTION", "description": "Module for SUBSTITUTION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in SUBSTITUTION module", "dependsOn": []}]}, {"name": "INDUS_SUY", "friendlyName": "INDUS_SUY", "description": "Module for INDUS_SUY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in INDUS_SUY module", "dependsOn": []}]}, {"name": "TIMETABLE", "friendlyName": "TIMETABLE", "description": "Module for TIMETABLE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MY_TIMETABLE", "friendlyName": "MY_TIMETABLE", "description": "Allows MY_TIMETABLE actions in TIMETABLE module", "dependsOn": []}, {"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in TIMETABLE module", "dependsOn": []}, {"name": "SUBJECTS", "friendlyName": "SUBJECTS", "description": "Allows SUBJECTS actions in TIMETABLE module", "dependsOn": []}, {"name": "SETTINGS", "friendlyName": "SETTINGS", "description": "Allows SETTINGS actions in TIMETABLE module", "dependsOn": []}, {"name": "CREATE_TIMETABLE", "friendlyName": "CREATE_TIMETABLE", "description": "Allows CREATE_TIMETABLE actions in TIMETABLE module", "dependsOn": []}, {"name": "CLONE_TIMETABLE", "friendlyName": "CLONE_TIMETABLE", "description": "Allows CLONE_TIMETABLE actions in TIMETABLE module", "dependsOn": []}, {"name": "STAFF_TIMETABLE_REPORT", "friendlyName": "STAFF_TIMETABLE_REPORT", "description": "Allows STAFF_TIMETABLE_REPORT actions in TIMETABLE module", "dependsOn": []}, {"name": "STAFF_WORKLOAD_REPORT", "friendlyName": "STAFF_WORKLOAD_REPORT", "description": "Allows STAFF_WORKLOAD_REPORT actions in TIMETABLE module", "dependsOn": []}, {"name": "SECTION_TIMETABLE_REPORT", "friendlyName": "SECTION_TIMETABLE_REPORT", "description": "Allows SECTION_TIMETABLE_REPORT actions in TIMETABLE module", "dependsOn": []}, {"name": "ROOM_TIMETABLE_REPORT", "friendlyName": "ROOM_TIMETABLE_REPORT", "description": "Allows ROOM_TIMETABLE_REPORT actions in TIMETABLE module", "dependsOn": []}, {"name": "FREEZE_SUBJECT_ALLOCATION", "friendlyName": "FREEZE_SUBJECT_ALLOCATION", "description": "Allows FREEZE_SUBJECT_ALLOCATION actions in TIMETABLE module", "dependsOn": []}, {"name": "AUTO_ALLOCATE_TIMETABLE", "friendlyName": "AUTO_ALLOCATE_TIMETABLE", "description": "Allows AUTO_ALLOCATE_TIMETABLE actions in TIMETABLE module", "dependsOn": []}, {"name": "MOVE_STAFF", "friendlyName": "MOVE_STAFF", "description": "Allows MOVE_STAFF actions in TIMETABLE module", "dependsOn": []}]}, {"name": "TODO", "friendlyName": "TODO", "description": "Module for TODO management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in TODO module", "dependsOn": []}]}, {"name": "TRANSPORTATION", "friendlyName": "TRANSPORTATION", "description": "Module for TRANSPORTATION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in TRANSPORTATION module", "dependsOn": []}, {"name": "BUSES", "friendlyName": "BUSES", "description": "Allows BUSES actions in TRANSPORTATION module", "dependsOn": []}, {"name": "ROUTES", "friendlyName": "ROUTES", "description": "Allows ROUTES actions in TRANSPORTATION module", "dependsOn": []}, {"name": "STOPS", "friendlyName": "STOPS", "description": "Allows STOPS actions in TRANSPORTATION module", "dependsOn": []}, {"name": "ENTITIES", "friendlyName": "ENTITIES", "description": "Allows ENTITIES actions in TRANSPORTATION module", "dependsOn": []}, {"name": "JOURNEYS", "friendlyName": "JOURNEYS", "description": "Allows JOURNEYS actions in TRANSPORTATION module", "dependsOn": []}, {"name": "DRIVERS", "friendlyName": "DRIVERS", "description": "Allows DRIVERS actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TAKE_ATTENDANCE", "friendlyName": "TAKE_ATTENDANCE", "description": "Allows TAKE_ATTENDANCE actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_REPORT", "friendlyName": "TX_REPORT", "description": "Allows TX_REPORT actions in TRANSPORTATION module", "dependsOn": []}, {"name": "ADMIN_CONSOLE", "friendlyName": "ADMIN_CONSOLE", "description": "Allows ADMIN_CONSOLE actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_MISMATCH_REPORT", "friendlyName": "TX_MISMATCH_REPORT", "description": "Allows TX_MISMATCH_REPORT actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_JOURNEY_CHANGES", "friendlyName": "TX_JOURNEY_CHANGES", "description": "Allows TX_JOURNEY_CHANGES actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_REPORT", "friendlyName": "TX_REPORT", "description": "Allows TX_REPORT actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_DAILY_TRACK", "friendlyName": "TX_DAILY_TRACK", "description": "Allows TX_DAILY_TRACK actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_DAILY_TRACK_SUMMARY", "friendlyName": "TX_DAILY_TRACK_SUMMARY", "description": "Allows TX_DAILY_TRACK_SUMMARY actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_STUDENT_WISE_REPORT", "friendlyName": "TX_STUDENT_WISE_REPORT", "description": "Allows TX_STUDENT_WISE_REPORT actions in TRANSPORTATION module", "dependsOn": []}, {"name": "TX_AVG_JOURNEY_TIME", "friendlyName": "TX_AVG_JOURNEY_TIME", "description": "Allows TX_AVG_JOURNEY_TIME actions in TRANSPORTATION module", "dependsOn": []}, {"name": "NOTIFICATION_LOGS", "friendlyName": "NOTIFICATION_LOGS", "description": "Allows NOTIFICATION_LOGS actions in TRANSPORTATION module", "dependsOn": []}, {"name": "EXPORT_STUDENT_DATA", "friendlyName": "EXPORT_STUDENT_DATA", "description": "Allows EXPORT_STUDENT_DATA actions in TRANSPORTATION module", "dependsOn": []}, {"name": "EXPORT_STAFF_DATA", "friendlyName": "EXPORT_STAFF_DATA", "description": "Allows EXPORT_STAFF_DATA actions in TRANSPORTATION module", "dependsOn": []}]}, {"name": "UPCOMING_BIRTHDAY", "friendlyName": "UPCOMING_BIRTHDAY", "description": "Module for UPCOMING_BIRTHDAY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to UPCOMING_BIRTHDAY module", "dependsOn": []}, {"name": "VIEW", "friendlyName": "VIEW", "description": "Allows VIEW actions in UPCOMING_BIRTHDAY module", "dependsOn": []}, {"name": "VIEW_STAFF", "friendlyName": "VIEW_STAFF", "description": "Allows VIEW_STAFF actions in UPCOMING_BIRTHDAY module", "dependsOn": []}]}, {"name": "USER_MANAGEMENT", "friendlyName": "USER_MANAGEMENT", "description": "Module for USER_MANAGEMENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "PROVISION_PARENTS", "friendlyName": "PROVISION_PARENTS", "description": "Allows PROVISION_PARENTS actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "PROVISION_STAFF", "friendlyName": "PROVISION_STAFF", "description": "Allows PROVISION_STAFF actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "CONNECT_SIBLINGS", "friendlyName": "CONNECT_SIBLINGS", "description": "Allows CONNECT_SIBLINGS actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "ASSIGN_ROLES_AND_PRIVILEGES", "friendlyName": "ASSIGN_ROLES_AND_PRIVILEGES", "description": "Allows ASSIGN_ROLES_AND_PRIVILEGES actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "ACTIVATE_PARENTS", "friendlyName": "ACTIVATE_PARENTS", "description": "Allows ACTIVATE_PARENTS actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "ACTIVATE_PARENTS_REPORTS", "friendlyName": "ACTIVATE_PARENTS_REPORTS", "description": "Allows ACTIVATE_PARENTS_REPORTS actions in USER_MANAGEMENT module", "dependsOn": []}, {"name": "ACCESS_CONTROL_HISTORY", "friendlyName": "ACCESS_CONTROL_HISTORY", "description": "Allows ACCESS_CONTROL_HISTORY actions in USER_MANAGEMENT module", "dependsOn": []}]}, {"name": "VENDOR", "friendlyName": "VENDOR", "description": "Module for VENDOR management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in VENDOR module", "dependsOn": []}, {"name": "CRUD", "friendlyName": "CRUD", "description": "Allows CRUD actions in VENDOR module", "dependsOn": []}]}, {"name": "VISITOR", "friendlyName": "VISITOR", "description": "Module for VISITOR management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in VISITOR module", "dependsOn": []}, {"name": "SECURITY_REGISTRATION", "friendlyName": "SECURITY_REGISTRATION", "description": "Allows SECURITY_REGISTRATION actions in VISITOR module", "dependsOn": []}, {"name": "STAFF_PREREGISTRATION", "friendlyName": "STAFF_PREREGISTRATION", "description": "Allows STAFF_PREREGISTRATION actions in VISITOR module", "dependsOn": []}, {"name": "STAFF_APPROVAL", "friendlyName": "STAFF_APPROVAL", "description": "Allows STAFF_APPROVAL actions in VISITOR module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in VISITOR module", "dependsOn": []}, {"name": "SWITCH_SCHOOL", "friendlyName": "SWITCH_SCHOOL", "description": "Allows SWITCH_SCHOOL actions in VISITOR module", "dependsOn": []}]}, {"name": "LEAVE", "friendlyName": "LEAVE", "description": "Module for LEAVE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in LEAVE module", "dependsOn": []}, {"name": "STUDENT_LEAVE_APPLY", "friendlyName": "STUDENT_LEAVE_APPLY", "description": "Allows STUDENT_LEAVE_APPLY actions in LEAVE module", "dependsOn": []}, {"name": "APPLY_STUDENT_LEAVE_FOR_PREVIOUS_DATES", "friendlyName": "APPLY_STUDENT_LEAVE_FOR_PREVIOUS_DATES", "description": "Allows APPLY_STUDENT_LEAVE_FOR_PREVIOUS_DATES actions in LEAVE module", "dependsOn": []}, {"name": "STUDENT_LEAVE_APPROVE", "friendlyName": "STUDENT_LEAVE_APPROVE", "description": "Allows STUDENT_LEAVE_APPROVE actions in LEAVE module", "dependsOn": []}, {"name": "STAFF_LEAVE_APPROVE", "friendlyName": "STAFF_LEAVE_APPROVE", "description": "Allows STAFF_LEAVE_APPROVE actions in LEAVE module", "dependsOn": []}, {"name": "STAFF_LEAVE_APPLY", "friendlyName": "STAFF_LEAVE_APPLY", "description": "Allows STAFF_LEAVE_APPLY actions in LEAVE module", "dependsOn": []}, {"name": "LEAVE_REPORT", "friendlyName": "LEAVE_REPORT", "description": "Allows LEAVE_REPORT actions in LEAVE module", "dependsOn": []}, {"name": "STUDENT_LEAVE_REPORT", "friendlyName": "STUDENT_LEAVE_REPORT", "description": "Allows STUDENT_LEAVE_REPORT actions in LEAVE module", "dependsOn": []}, {"name": "STUDENT_LEAVE_TYPE_CONFIG", "friendlyName": "STUDENT_LEAVE_TYPE_CONFIG", "description": "Allows STUDENT_LEAVE_TYPE_CONFIG actions in LEAVE module", "dependsOn": []}, {"name": "APPLY_LEAVE_FOR_OTHER_STAFF", "friendlyName": "APPLY_LEAVE_FOR_OTHER_STAFF", "description": "Allows APPLY_LEAVE_FOR_OTHER_STAFF actions in LEAVE module", "dependsOn": []}, {"name": "STAFF_LEAVE_ADMIN", "friendlyName": "STAFF_LEAVE_ADMIN", "description": "Allows STAFF_LEAVE_ADMIN actions in LEAVE module", "dependsOn": []}, {"name": "STAFF_LEAVE_CATEGORY", "friendlyName": "STAFF_LEAVE_CATEGORY", "description": "Allows STAFF_LEAVE_CATEGORY actions in LEAVE module", "dependsOn": []}, {"name": "STAFF_LEAVE_QUOTA", "friendlyName": "STAFF_LEAVE_QUOTA", "description": "Allows STAFF_LEAVE_QUOTA actions in LEAVE module", "dependsOn": []}, {"name": "STUDENT_LEAVE_ADMIN", "friendlyName": "STUDENT_LEAVE_ADMIN", "description": "Allows STUDENT_LEAVE_ADMIN actions in LEAVE module", "dependsOn": []}, {"name": "COMPENSATION_STAFF_LEAVE_APPLY", "friendlyName": "COMPENSATION_STAFF_LEAVE_APPLY", "description": "Allows COMPENSATION_STAFF_LEAVE_APPLY actions in LEAVE module", "dependsOn": []}, {"name": "COMPENSATION_STAFF_LEAVE_APPROVE", "friendlyName": "COMPENSATION_STAFF_LEAVE_APPROVE", "description": "Allows COMPENSATION_STAFF_LEAVE_APPROVE actions in LEAVE module", "dependsOn": []}, {"name": "STAFF_LEAVE_YEARLY_REPORT", "friendlyName": "STAFF_LEAVE_YEARLY_REPORT", "description": "Allows STAFF_LEAVE_YEARLY_REPORT actions in LEAVE module", "dependsOn": []}, {"name": "ENABLE_HALF_DAY_STAFF_LEAVE_APPLY", "friendlyName": "ENABLE_HALF_DAY_STAFF_LEAVE_APPLY", "description": "Allows ENABLE_HALF_DAY_STAFF_LEAVE_APPLY actions in LEAVE module", "dependsOn": []}, {"name": "ENABLE_HALF_DAY_STAFF_LEAVE_APPLY_3_LEVEL", "friendlyName": "ENABLE_HALF_DAY_STAFF_LEAVE_APPLY_3_LEVEL", "description": "Allows ENABLE_HALF_DAY_STAFF_LEAVE_APPLY_3_LEVEL actions in LEAVE module", "dependsOn": []}, {"name": "ENABLE_STAFF_LEAVES_LOP_REPORT", "friendlyName": "ENABLE_STAFF_LEAVES_LOP_REPORT", "description": "Allows ENABLE_STAFF_LEAVES_LOP_REPORT actions in LEAVE module", "dependsOn": []}, {"name": "ENABLE_STAFF_LEAVE_CONVERSION", "friendlyName": "ENABLE_STAFF_LEAVE_CONVERSION", "description": "Allows ENABLE_STAFF_LEAVE_CONVERSION actions in LEAVE module", "dependsOn": []}]}, {"name": "TEXTING", "friendlyName": "TEXTING", "description": "Module for TEXTING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to TEXTING module", "dependsOn": []}, {"name": "SEND", "friendlyName": "SEND", "description": "Allows SEND actions in TEXTING module", "dependsOn": []}, {"name": "REPORT", "friendlyName": "REPORT", "description": "Allows REPORT actions in TEXTING module", "dependsOn": []}]}, {"name": "TRANSPORTATION_REQUEST", "friendlyName": "TRANSPORTATION_REQUEST", "description": "Module for TRANSPORTATION_REQUEST management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in TRANSPORTATION_REQUEST module", "dependsOn": []}]}, {"name": "SALES", "friendlyName": "SALES", "description": "Module for SALES management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in SALES module", "dependsOn": []}, {"name": "SOFT_DELETE_RECEIPTS", "friendlyName": "SOFT_DELETE_RECEIPTS", "description": "Allows SOFT_DELETE_RECEIPTS actions in SALES module", "dependsOn": []}, {"name": "NEW_STUDENT_SALES_REPORT", "friendlyName": "NEW_STUDENT_SALES_REPORT", "description": "Allows NEW_STUDENT_SALES_REPORT actions in SALES module", "dependsOn": []}, {"name": "NON_RECONCILE_REPORT", "friendlyName": "NON_RECONCILE_REPORT", "description": "Allows NON_RECONCILE_REPORT actions in SALES module", "dependsOn": []}]}, {"name": "ADMISSION", "friendlyName": "ADMISSION", "description": "Module for ADMISSION management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ADMISSION module", "dependsOn": []}, {"name": "REPORTS", "friendlyName": "REPORTS", "description": "Allows REPORTS actions in ADMISSION module", "dependsOn": []}, {"name": "FIELD_SELECTION", "friendlyName": "FIELD_SELECTION", "description": "Allows FIELD_SELECTION actions in ADMISSION module", "dependsOn": []}, {"name": "SETTINGS", "friendlyName": "SETTINGS", "description": "Allows SETTINGS actions in ADMISSION module", "dependsOn": []}, {"name": "VIEW_EDIT_APPLICATION", "friendlyName": "VIEW_EDIT_APPLICATION", "description": "Allows VIEW_EDIT_APPLICATION actions in ADMISSION module", "dependsOn": []}, {"name": "UPDATE_DETAILS", "friendlyName": "UPDATE_DETAILS", "description": "Allows UPDATE_DETAILS actions in ADMISSION module", "dependsOn": []}, {"name": "APPROVED_SEND_SMS", "friendlyName": "APPROVED_SEND_SMS", "description": "Allows APPROVED_SEND_SMS actions in ADMISSION module", "dependsOn": []}, {"name": "ONLINE_SETTLEMENT_REPORT", "friendlyName": "ONLINE_SETTLEMENT_REPORT", "description": "Allows ONLINE_SETTLEMENT_REPORT actions in ADMISSION module", "dependsOn": []}, {"name": "ONLINE_TRANSACTION_REPORT", "friendlyName": "ONLINE_TRANSACTION_REPORT", "description": "Allows ONLINE_TRANSACTION_REPORT actions in ADMISSION module", "dependsOn": []}, {"name": "MOVE_TO_ERP", "friendlyName": "MOVE_TO_ERP", "description": "Allows MOVE_TO_ERP actions in ADMISSION module", "dependsOn": []}, {"name": "CREATE_OFFLINE_APPLICATION", "friendlyName": "CREATE_OFFLINE_APPLICATION", "description": "Allows CREATE_OFFLINE_APPLICATION actions in ADMISSION module", "dependsOn": []}, {"name": "FOLLOWUP", "friendlyName": "FOLLOWUP", "description": "Allows FOLLOWUP actions in ADMISSION module", "dependsOn": []}, {"name": "PRINT_EMPTY_APPLICATION", "friendlyName": "PRINT_EMPTY_APPLICATION", "description": "Allows PRINT_EMPTY_APPLICATION actions in ADMISSION module", "dependsOn": []}, {"name": "VIEW_DETAILS", "friendlyName": "VIEW_DETAILS", "description": "Allows VIEW_DETAILS actions in ADMISSION module", "dependsOn": []}, {"name": "MOVE_TO_ERP_INDIVIDUAL", "friendlyName": "MOVE_TO_ERP_INDIVIDUAL", "description": "Allows MOVE_TO_ERP_INDIVIDUAL actions in ADMISSION module", "dependsOn": []}, {"name": "CAN_ADMIT_REJECT", "friendlyName": "CAN_ADMIT_REJECT", "description": "Allows CAN_ADMIT_REJECT actions in ADMISSION module", "dependsOn": []}, {"name": "COLLECT_OFFLINE_FEE", "friendlyName": "COLLECT_OFFLINE_FEE", "description": "Allows COLLECT_OFFLINE_FEE actions in ADMISSION module", "dependsOn": []}, {"name": "SEAT_ALLOTMENT", "friendlyName": "SEAT_ALLOTMENT", "description": "Allows SEAT_ALLOTMENT actions in ADMISSION module", "dependsOn": []}, {"name": "EDIT_APPLICATION_FORM", "friendlyName": "EDIT_APPLICATION_FORM", "description": "Allows EDIT_APPLICATION_FORM actions in ADMISSION module", "dependsOn": []}, {"name": "CREATE_OFFERS", "friendlyName": "CREATE_OFFERS", "description": "Allows CREATE_OFFERS actions in ADMISSION module", "dependsOn": []}, {"name": "JOINING_FORMS", "friendlyName": "JOINING_FORMS", "description": "Allows JOINING_FORMS actions in ADMISSION module", "dependsOn": []}, {"name": "INDUS_APPLICATION_REPORT", "friendlyName": "INDUS_APPLICATION_REPORT", "description": "Allows INDUS_APPLICATION_REPORT actions in ADMISSION module", "dependsOn": []}, {"name": "EDIT_HISTORY", "friendlyName": "EDIT_HISTORY", "description": "Allows EDIT_HISTORY actions in ADMISSION module", "dependsOn": []}, {"name": "LINK_TO_ENQUIRY", "friendlyName": "LINK_TO_ENQUIRY", "description": "Allows LINK_TO_ENQUIRY actions in ADMISSION module", "dependsOn": []}, {"name": "ASSIGN_COUNSELOR", "friendlyName": "ASSIGN_COUNSELOR", "description": "Allows ASSIGN_COUNSELOR actions in ADMISSION module", "dependsOn": []}, {"name": "ADMISSION_ANALYSIS", "friendlyName": "ADMISSION_ANALYSIS", "description": "Allows ADMISSION_ANALYSIS actions in ADMISSION module", "dependsOn": []}, {"name": "GENERATE_APPLICATION", "friendlyName": "GENERATE_APPLICATION", "description": "Allows GENERATE_APPLICATION actions in ADMISSION module", "dependsOn": []}, {"name": "CHANGE_ACADEMIC_YEAR", "friendlyName": "CHANGE_ACADEMIC_YEAR", "description": "Allows CHANGE_ACADEMIC_YEAR actions in ADMISSION module", "dependsOn": []}, {"name": "RECEIPT_CANCLED_REPORT", "friendlyName": "RECEIPT_CANCLED_REPORT", "description": "Allows RECEIPT_CANCLED_REPORT actions in ADMISSION module", "dependsOn": []}, {"name": "MASS_EMAIL", "friendlyName": "MASS_EMAIL", "description": "Allows MASS_EMAIL actions in ADMISSION module", "dependsOn": []}, {"name": "CLOSE_APPLICATION", "friendlyName": "CLOSE_APPLICATION", "description": "Allows CLOSE_APPLICATION actions in ADMISSION module", "dependsOn": []}, {"name": "ENABLE_COMPLETED_INTERACTIONS", "friendlyName": "ENABLE_COMPLETED_INTERACTIONS", "description": "Allows ENABLE_COMPLETED_INTERACTIONS actions in ADMISSION module", "dependsOn": []}, {"name": "ACTIVITY_REPORT", "friendlyName": "ACTIVITY_REPORT", "description": "Allows ACTIVITY_REPORT actions in ADMISSION module", "dependsOn": []}, {"name": "COUNSELOR_ADMIN", "friendlyName": "COUNSELOR_ADMIN", "description": "Allows COUNSELOR_ADMIN actions in ADMISSION module", "dependsOn": []}, {"name": "REVERT_SUBMISSION", "friendlyName": "REVERT_SUBMISSION", "description": "Allows REVERT_SUBMISSION actions in ADMISSION module", "dependsOn": []}]}, {"name": "ENQUIRY", "friendlyName": "ENQUIRY", "description": "Module for ENQUIRY management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ENQUIRY module", "dependsOn": []}, {"name": "ACAD_YEAR_CHANGE", "friendlyName": "ACAD_YEAR_CHANGE", "description": "Allows ACAD_YEAR_CHANGE actions in ENQUIRY module", "dependsOn": []}, {"name": "ENQUIRY_ADMIN", "friendlyName": "ENQUIRY_ADMIN", "description": "Allows ENQUIRY_ADMIN actions in ENQUIRY module", "dependsOn": []}, {"name": "SHOW_COUNSELOR_LIST", "friendlyName": "SHOW_COUNSELOR_LIST", "description": "Allows SHOW_COUNSELOR_LIST actions in ENQUIRY module", "dependsOn": []}, {"name": "MASS_EMAIL", "friendlyName": "MASS_EMAIL", "description": "Allows MASS_EMAIL actions in ENQUIRY module", "dependsOn": []}, {"name": "DEDUPE_ENQUIRIES", "friendlyName": "DEDUPE_ENQUIRIES", "description": "Allows DEDUPE_ENQUIRIES actions in ENQUIRY module", "dependsOn": []}]}, {"name": "VIRTUAL_CLASSROOM", "friendlyName": "VIRTUAL_CLASSROOM", "description": "Module for VIRTUAL_CLASSROOM management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in VIRTUAL_CLASSROOM module", "dependsOn": []}, {"name": "CREATE_SCHEDULE", "friendlyName": "CREATE_SCHEDULE", "description": "Allows CREATE_SCHEDULE actions in VIRTUAL_CLASSROOM module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in VIRTUAL_CLASSROOM module", "dependsOn": []}]}, {"name": "BIRTHDAY_SMS_SEND", "friendlyName": "BIRTHDAY_SMS_SEND", "description": "Module for BIRTHDAY_SMS_SEND management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in BIRTHDAY_SMS_SEND module", "dependsOn": []}]}, {"name": "IDCARDS", "friendlyName": "IDCARDS", "description": "Module for IDCARDS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in IDCARDS module", "dependsOn": []}]}, {"name": "WIDGET", "friendlyName": "WIDGET", "description": "Module for WIDGET management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Base access to WIDGET module", "dependsOn": []}, {"name": "STAFF_DATA", "friendlyName": "STAFF_DATA", "description": "Allows STAFF_DATA actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_DATA", "friendlyName": "STUDENT_DATA", "description": "Allows STUDENT_DATA actions in WIDGET module", "dependsOn": []}, {"name": "TASK_ASSIGNED", "friendlyName": "TASK_ASSIGNED", "description": "Allows TASK_ASSIGNED actions in WIDGET module", "dependsOn": []}, {"name": "TASK_SUMMARY", "friendlyName": "TASK_SUMMARY", "description": "Allows TASK_SUMMARY actions in WIDGET module", "dependsOn": []}, {"name": "FEE_COLLECTION_TOTAL", "friendlyName": "FEE_COLLECTION_TOTAL", "description": "Allows FEE_COLLECTION_TOTAL actions in WIDGET module", "dependsOn": []}, {"name": "FEE_COLLECTION_TREND", "friendlyName": "FEE_COLLECTION_TREND", "description": "Allows FEE_COLLECTION_TREND actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_COUNT", "friendlyName": "STUDENT_COUNT", "description": "Allows STUDENT_COUNT actions in WIDGET module", "dependsOn": []}, {"name": "COMMUNICATION_TREND", "friendlyName": "COMMUNICATION_TREND", "description": "Allows COMMUNICATION_TREND actions in WIDGET module", "dependsOn": []}, {"name": "MY_TIMETABLE", "friendlyName": "MY_TIMETABLE", "description": "Allows MY_TIMETABLE actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_ATTENDANCE_V2_SUMMARY", "friendlyName": "STUDENT_ATTENDANCE_V2_SUMMARY", "description": "Allows STUDENT_ATTENDANCE_V2_SUMMARY actions in WIDGET module", "dependsOn": []}, {"name": "REPORTING_MANAGER", "friendlyName": "REPORTING_MANAGER", "description": "Allows REPORTING_MANAGER actions in WIDGET module", "dependsOn": []}, {"name": "OTHER_LINKS", "friendlyName": "OTHER_LINKS", "description": "Allows OTHER_LINKS actions in WIDGET module", "dependsOn": []}, {"name": "GALLERY", "friendlyName": "GALLERY", "description": "Allows GALLERY actions in WIDGET module", "dependsOn": []}, {"name": "TICKETING", "friendlyName": "TICKETING", "description": "Allows TICKETING actions in WIDGET module", "dependsOn": []}, {"name": "PARENT_TICKETING", "friendlyName": "PARENT_TICKETING", "description": "Allows PARENT_TICKETING actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_COUNT_GENDERWISE", "friendlyName": "STUDENT_COUNT_GENDERWISE", "description": "Allows STUDENT_COUNT_GENDERWISE actions in WIDGET module", "dependsOn": []}, {"name": "ENQUIRY_WIDGET", "friendlyName": "ENQUIRY_WIDGET", "description": "Allows ENQUIRY_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "INTERNAL_TICKETING", "friendlyName": "INTERNAL_TICKETING", "description": "Allows INTERNAL_TICKETING actions in WIDGET module", "dependsOn": []}, {"name": "STAFF_ON_LEAVE_WIDGET", "friendlyName": "STAFF_ON_LEAVE_WIDGET", "description": "Allows STAFF_ON_LEAVE_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_NON_COMPLIANCE", "friendlyName": "STUDENT_NON_COMPLIANCE", "description": "Allows STUDENT_NON_COMPLIANCE actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_NON_COMPLIANCE_STATISTICS", "friendlyName": "STUDENT_NON_COMPLIANCE_STATISTICS", "description": "Allows STUDENT_NON_COMPLIANCE_STATISTICS actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_OBSERVATION", "friendlyName": "STUDENT_OBSERVATION", "description": "Allows STUDENT_OBSERVATION actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_WIDGET_STATISTICS", "friendlyName": "STUDENT_WIDGET_STATISTICS", "description": "Allows STUDENT_WIDGET_STATISTICS actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_COUNCELLING_TREND", "friendlyName": "STUDENT_COUNCELLING_TREND", "description": "Allows STUDENT_COUNCELLING_TREND actions in WIDGET module", "dependsOn": []}, {"name": "GET_APPROVAL_WIDGET_DATA", "friendlyName": "GET_APPROVAL_WIDGET_DATA", "description": "Allows GET_APPROVAL_WIDGET_DATA actions in WIDGET module", "dependsOn": []}, {"name": "STAFF_LEAVES_DETAILS", "friendlyName": "STAFF_LEAVES_DETAILS", "description": "Allows STAFF_LEAVES_DETAILS actions in WIDGET module", "dependsOn": []}, {"name": "MONTH_WISE_STAFF_CALENDAR", "friendlyName": "MONTH_WISE_STAFF_CALENDAR", "description": "Allows MONTH_WISE_STAFF_CALENDAR actions in WIDGET module", "dependsOn": []}, {"name": "INFIRMARY_VISITOR_WIDGET_DATA", "friendlyName": "INFIRMARY_VISITOR_WIDGET_DATA", "description": "Allows INFIRMARY_VISITOR_WIDGET_DATA actions in WIDGET module", "dependsOn": []}, {"name": "ATTENDANCE_CHECKIN_WIDGET", "friendlyName": "ATTENDANCE_CHECKIN_WIDGET", "description": "Allows ATTENDANCE_CHECKIN_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "VISITOR", "friendlyName": "VISITOR", "description": "Allows VISITOR actions in WIDGET module", "dependsOn": []}, {"name": "INFIRMARY_STATISTICS_WIDGET", "friendlyName": "INFIRMARY_STATISTICS_WIDGET", "description": "Allows INFIRMARY_STATISTICS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "STAFF_ANNIVERSARY_WIDGET", "friendlyName": "STAFF_ANNIVERSARY_WIDGET", "description": "Allows STAFF_ANNIVERSARY_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "ENQUIRY_STATISTICS_WIDGET", "friendlyName": "ENQUIRY_STATISTICS_WIDGET", "description": "Allows ENQUIRY_STATISTICS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "LIBRARY_STATISTICS_WIDGET", "friendlyName": "LIBRARY_STATISTICS_WIDGET", "description": "Allows LIBRARY_STATISTICS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "TRANSPORTATION_STATISTICS_WIDGET", "friendlyName": "TRANSPORTATION_STATISTICS_WIDGET", "description": "Allows TRANSPORTATION_STATISTICS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "INVENTORY_STATISTICS_WIDGET", "friendlyName": "INVENTORY_STATISTICS_WIDGET", "description": "Allows INVENTORY_STATISTICS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "BOOKS_TREND_WIDGET", "friendlyName": "BOOKS_TREND_WIDGET", "description": "Allows BOOKS_TREND_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "RFID_WIDGET", "friendlyName": "RFID_WIDGET", "description": "Allows RFID_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_COUNSELLING_STAT", "friendlyName": "STUDENT_COUNSELLING_STAT", "description": "Allows STUDENT_COUNSELLING_STAT actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_CHECKIN_TRACKING", "friendlyName": "STUDENT_CHECKIN_TRACKING", "description": "Allows STUDENT_CHECKIN_TRACKING actions in WIDGET module", "dependsOn": []}, {"name": "SUBSTITUTION_STATISTICS", "friendlyName": "SUBSTITUTION_STATISTICS", "description": "Allows SUBSTITUTION_STATISTICS actions in WIDGET module", "dependsOn": []}, {"name": "TASK_BASKET", "friendlyName": "TASK_BASKET", "description": "Allows TASK_BASKET actions in WIDGET module", "dependsOn": []}, {"name": "STAFF_ATTENDANCE_REPORTS_WISE", "friendlyName": "STAFF_ATTENDANCE_REPORTS_WISE", "description": "Allows STAFF_ATTENDANCE_REPORTS_WISE actions in WIDGET module", "dependsOn": []}, {"name": "ADMISSION_STATISTICS_WIDGET", "friendlyName": "ADMISSION_STATISTICS_WIDGET", "description": "Allows ADMISSION_STATISTICS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "SINGLE_WINDOW", "friendlyName": "SINGLE_WINDOW", "description": "Allows SINGLE_WINDOW actions in WIDGET module", "dependsOn": []}, {"name": "CATEGORY_STOCK_DETAILS_WIDGET", "friendlyName": "CATEGORY_STOCK_DETAILS_WIDGET", "description": "Allows CATEGORY_STOCK_DETAILS_WIDGET actions in WIDGET module", "dependsOn": []}, {"name": "DAILY_PLANNER", "friendlyName": "DAILY_PLANNER", "description": "Allows DAILY_PLANNER actions in WIDGET module", "dependsOn": []}, {"name": "ID_CARDS", "friendlyName": "ID_CARDS", "description": "Allows ID_CARDS actions in WIDGET module", "dependsOn": []}, {"name": "STUDENT_DAY_ATTENDANCE_V2_SUMMARY", "friendlyName": "STUDENT_DAY_ATTENDANCE_V2_SUMMARY", "description": "Allows STUDENT_DAY_ATTENDANCE_V2_SUMMARY actions in WIDGET module", "dependsOn": []}]}, {"name": "STUDENT_WALLET", "friendlyName": "STUDENT_WALLET", "description": "Module for STUDENT_WALLET management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_WALLET module", "dependsOn": []}]}, {"name": "STUDENT_TRACKING", "friendlyName": "STUDENT_TRACKING", "description": "Module for STUDENT_TRACKING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_TRACKING module", "dependsOn": []}, {"name": "MANAGE_ESCORTS", "friendlyName": "MANAGE_ESCORTS", "description": "Allows MANAGE_ESCORTS actions in STUDENT_TRACKING module", "dependsOn": []}, {"name": "FACILITY_ENTRY", "friendlyName": "FACILITY_ENTRY", "description": "Allows FACILITY_ENTRY actions in STUDENT_TRACKING module", "dependsOn": []}, {"name": "VIEW_REPORTS", "friendlyName": "VIEW_REPORTS", "description": "Allows VIEW_REPORTS actions in STUDENT_TRACKING module", "dependsOn": []}, {"name": "TRACKING_ADMIN", "friendlyName": "TRACKING_ADMIN", "description": "Allows TRACKING_ADMIN actions in STUDENT_TRACKING module", "dependsOn": []}]}, {"name": "ITARI", "friendlyName": "ITARI", "description": "Module for ITARI management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ITARI module", "dependsOn": []}]}, {"name": "STAFF_RECRUITMENT", "friendlyName": "STAFF_RECRUITMENT", "description": "Module for STAFF_RECRUITMENT management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STAFF_RECRUITMENT module", "dependsOn": []}, {"name": "BUDGETED_HEADCOUNT", "friendlyName": "BUDGETED_HEADCOUNT", "description": "Allows BUDGETED_HEADCOUNT actions in STAFF_RECRUITMENT module", "dependsOn": []}, {"name": "VACANCIES", "friendlyName": "VACANCIES", "description": "Allows VACANCIES actions in STAFF_RECRUITMENT module", "dependsOn": []}, {"name": "MASS_UPLOAD_RESUMES", "friendlyName": "MASS_UPLOAD_RESUMES", "description": "Allows MASS_UPLOAD_RESUMES actions in STAFF_RECRUITMENT module", "dependsOn": []}, {"name": "MANAGE_CANDIDATES", "friendlyName": "MANAGE_CANDIDATES", "description": "Allows MANAGE_CANDIDATES actions in STAFF_RECRUITMENT module", "dependsOn": []}, {"name": "MANAGE_INTERVIEW_PROCESS", "friendlyName": "MANAGE_INTERVIEW_PROCESS", "description": "Allows MANAGE_INTERVIEW_PROCESS actions in STAFF_RECRUITMENT module", "dependsOn": []}, {"name": "TAKE_INTERVIEW", "friendlyName": "TAKE_INTERVIEW", "description": "Allows TAKE_INTERVIEW actions in STAFF_RECRUITMENT module", "dependsOn": []}]}, {"name": "STUDENT_COUNSELLING", "friendlyName": "STUDENT_COUNSELLING", "description": "Module for STUDENT_COUNSELLING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "STUDENT_WISE_REPORT", "friendlyName": "STUDENT_WISE_REPORT", "description": "Allows STUDENT_WISE_REPORT actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "COUNCELLING_REPORT", "friendlyName": "COUNCELLING_REPORT", "description": "Allows COUNCELLING_REPORT actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "GRADE_WISE_REPORT", "friendlyName": "GRADE_WISE_REPORT", "description": "Allows GRADE_WISE_REPORT actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "COUNSELLING_ANALYTICS", "friendlyName": "COUNSELLING_ANALYTICS", "description": "Allows COUNSELLING_ANALYTICS actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "ADMINISTRATION", "friendlyName": "ADMINISTRATION", "description": "Allows ADMINISTRATION actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "MY_WEEKLY_REPORT", "friendlyName": "MY_WEEKLY_REPORT", "description": "Allows MY_WEEKLY_REPORT actions in STUDENT_COUNSELLING module", "dependsOn": []}, {"name": "COMMENTS_REPORT", "friendlyName": "COMMENTS_REPORT", "description": "Allows COMMENTS_REPORT actions in STUDENT_COUNSELLING module", "dependsOn": []}]}, {"name": "CLASSROOM_CHRONICLES", "friendlyName": "CLASSROOM_CHRONICLES", "description": "Module for CLASSROOM_CHRONICLES management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in CLASSROOM_CHRONICLES module", "dependsOn": []}]}, {"name": "STUDENT_EXIT_FLOW", "friendlyName": "STUDENT_EXIT_FLOW", "description": "Module for STUDENT_EXIT_FLOW management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_EXIT_FLOW module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in STUDENT_EXIT_FLOW module", "dependsOn": []}]}, {"name": "STUDENT_EXIT_FLOW_STAFF", "friendlyName": "STUDENT_EXIT_FLOW_STAFF", "description": "Module for STUDENT_EXIT_FLOW_STAFF management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_EXIT_FLOW_STAFF module", "dependsOn": []}, {"name": "ADMIN", "friendlyName": "ADMIN", "description": "Allows ADMIN actions in STUDENT_EXIT_FLOW_STAFF module", "dependsOn": []}]}, {"name": "BOARDING", "friendlyName": "BOARDING", "description": "Module for BOARDING management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in BOARDING module", "dependsOn": []}, {"name": "CAMPUS_MANAGEMENT", "friendlyName": "CAMPUS_MANAGEMENT", "description": "Allows CAMPUS_MANAGEMENT actions in BOARDING module", "dependsOn": []}, {"name": "ROOM_TYPE", "friendlyName": "ROOM_TYPE", "description": "Allows ROOM_TYPE actions in BOARDING module", "dependsOn": []}, {"name": "MANAGE_WARDEN", "friendlyName": "MANAGE_WARDEN", "description": "Allows MANAGE_WARDEN actions in BOARDING module", "dependsOn": []}, {"name": "MANAGE_OBSERVATION_CATEGORIES", "friendlyName": "MANAGE_OBSERVATION_CATEGORIES", "description": "Allows MANAGE_OBSERVATION_CATEGORIES actions in BOARDING module", "dependsOn": []}, {"name": "MANAGE_ATTENDANCE_SESSIONS", "friendlyName": "MANAGE_ATTENDANCE_SESSIONS", "description": "Allows MANAGE_ATTENDANCE_SESSIONS actions in BOARDING module", "dependsOn": []}, {"name": "BOARDING_CAMPUS_REPORT", "friendlyName": "BOARDING_CAMPUS_REPORT", "description": "Allows BOARDING_CAMPUS_REPORT actions in BOARDING module", "dependsOn": []}, {"name": "BLOCK_WISE_REPORT", "friendlyName": "BLOCK_WISE_REPORT", "description": "Allows BLOCK_WISE_REPORT actions in BOARDING module", "dependsOn": []}, {"name": "BED_ALLOTMENT_REPORT", "friendlyName": "BED_ALLOTMENT_REPORT", "description": "Allows BED_ALLOTMENT_REPORT actions in BOARDING module", "dependsOn": []}, {"name": "BED_VACANCY_REPORT", "friendlyName": "BED_VACANCY_REPORT", "description": "Allows BED_VACANCY_REPORT actions in BOARDING module", "dependsOn": []}, {"name": "OBSERVATION_REPORT", "friendlyName": "OBSERVATION_REPORT", "description": "Allows OBSERVATION_REPORT actions in BOARDING module", "dependsOn": []}, {"name": "ATTENDANCE_REPORT", "friendlyName": "ATTENDANCE_REPORT", "description": "Allows ATTENDANCE_REPORT actions in BOARDING module", "dependsOn": []}, {"name": "ALLOCATION", "friendlyName": "ALLOCATION", "description": "Allows ALLOCATION actions in BOARDING module", "dependsOn": []}, {"name": "MY_OBSERVATIONS", "friendlyName": "MY_OBSERVATIONS", "description": "Allows MY_OBSERVATIONS actions in BOARDING module", "dependsOn": []}, {"name": "DEACTIVATE_OBSERVATION", "friendlyName": "DEACTIVATE_OBSERVATION", "description": "Allows DEACTIVATE_OBSERVATION actions in BOARDING module", "dependsOn": []}, {"name": "TAKE_ATTENDENCE", "friendlyName": "TAKE_ATTENDENCE", "description": "Allows TAKE_ATTENDENCE actions in BOARDING module", "dependsOn": []}]}, {"name": "STUDENT_DAY_ATTENDANCE_V2", "friendlyName": "STUDENT_DAY_ATTENDANCE_V2", "description": "Module for STUDENT_DAY_ATTENDANCE_V2 management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "TAKE_ATTENDANCE", "friendlyName": "TAKE_ATTENDANCE", "description": "Allows TAKE_ATTENDANCE actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "STUDENT_DAY_ATTENDANCE_V2_ADMIN", "friendlyName": "STUDENT_DAY_ATTENDANCE_V2_ADMIN", "description": "Allows STUDENT_DAY_ATTENDANCE_V2_ADMIN actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "EMERGENCY_EXIT", "friendlyName": "EMERGENCY_EXIT", "description": "Allows EMERGENCY_EXIT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "ATTENDANCE_ABSENT_REASONS", "friendlyName": "ATTENDANCE_ABSENT_REASONS", "description": "Allows ATTENDANCE_ABSENT_REASONS actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "ATTENDANCE_REPORT", "friendlyName": "ATTENDANCE_REPORT", "description": "Allows ATTENDANCE_REPORT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "CALENDER_EVENTS", "friendlyName": "CALENDER_EVENTS", "description": "Allows CALENDER_EVENTS actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "ATTENDANCE_NOT_TAKEN_REPORT", "friendlyName": "ATTENDANCE_NOT_TAKEN_REPORT", "description": "Allows ATTENDANCE_NOT_TAKEN_REPORT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "STUDENT_CONSECUTIVE_ABSENT_REPORT", "friendlyName": "STUDENT_CONSECUTIVE_ABSENT_REPORT", "description": "Allows STUDENT_CONSECUTIVE_ABSENT_REPORT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "MONTH_WISE_ATTENDANCE_REPORT", "friendlyName": "MONTH_WISE_ATTENDANCE_REPORT", "description": "Allows MONTH_WISE_ATTENDANCE_REPORT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "TAKE_ATTENDANCE_FOR_PREVIOUS_DATES", "friendlyName": "TAKE_ATTENDANCE_FOR_PREVIOUS_DATES", "description": "Allows TAKE_ATTENDANCE_FOR_PREVIOUS_DATES actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "TAKE_ALL_SECTION_ATTENDANCE", "friendlyName": "TAKE_ALL_SECTION_ATTENDANCE", "description": "Allows TAKE_ALL_SECTION_ATTENDANCE actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "SPECIAL_CASE_REPORT", "friendlyName": "SPECIAL_CASE_REPORT", "description": "Allows SPECIAL_CASE_REPORT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}, {"name": "ATTENDANCE_MISSING_REPORT", "friendlyName": "ATTENDANCE_MISSING_REPORT", "description": "Allows ATTENDANCE_MISSING_REPORT actions in STUDENT_DAY_ATTENDANCE_V2 module", "dependsOn": []}]}, {"name": "DAILY_PLANNER", "friendlyName": "DAILY_PLANNER", "description": "Module for DAILY_PLANNER management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in DAILY_PLANNER module", "dependsOn": []}, {"name": "DAILY_PLANNER_CALENDAR", "friendlyName": "DAILY_PLANNER_CALENDAR", "description": "Allows DAILY_PLANNER_CALENDAR actions in DAILY_PLANNER module", "dependsOn": []}, {"name": "MANAGE_STAFF_TASK_TYPES", "friendlyName": "MANAGE_STAFF_TASK_TYPES", "description": "Allows MANAGE_STAFF_TASK_TYPES actions in DAILY_PLANNER module", "dependsOn": []}, {"name": "ENABLE_STYLUS_PEN", "friendlyName": "ENABLE_STYLUS_PEN", "description": "Allows ENABLE_STYLUS_PEN actions in DAILY_PLANNER module", "dependsOn": []}]}, {"name": "STUDENT_CERTIFICATE", "friendlyName": "STUDENT_CERTIFICATE", "description": "Module for STUDENT_CERTIFICATE management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in STUDENT_CERTIFICATE module", "dependsOn": []}, {"name": "ADD_NEW_CERTIFICATE_TEMPLATE", "friendlyName": "ADD_NEW_CERTIFICATE_TEMPLATE", "description": "Allows ADD_NEW_CERTIFICATE_TEMPLATE actions in STUDENT_CERTIFICATE module", "dependsOn": []}, {"name": "CLONE_CERTIFICATE_TEMPLATE", "friendlyName": "CLONE_CERTIFICATE_TEMPLATE", "description": "Allows CLONE_CERTIFICATE_TEMPLATE actions in STUDENT_CERTIFICATE module", "dependsOn": []}, {"name": "EDIT_CERTIFICATE_TEMPLATE", "friendlyName": "EDIT_CERTIFICATE_TEMPLATE", "description": "Allows EDIT_CERTIFICATE_TEMPLATE actions in STUDENT_CERTIFICATE module", "dependsOn": []}, {"name": "ISSUE_CERTIFICATES", "friendlyName": "ISSUE_CERTIFICATES", "description": "Allows ISSUE_CERTIFICATES actions in STUDENT_CERTIFICATE module", "dependsOn": []}, {"name": "CERTIFICATES_REPORT", "friendlyName": "CERTIFICATES_REPORT", "description": "Allows CERTIFICATES_REPORT actions in STUDENT_CERTIFICATE module", "dependsOn": []}, {"name": "MANAGE_CERTIFICATES_TEMPLATE", "friendlyName": "MANAGE_CERTIFICATES_TEMPLATE", "description": "Allows MANAGE_CERTIFICATES_TEMPLATE actions in STUDENT_CERTIFICATE module", "dependsOn": []}]}, {"name": "ID_CARDS", "friendlyName": "ID_CARDS", "description": "Module for ID_CARDS management", "dependsOn": [], "isCritical": false, "subPrivileges": [{"name": "MODULE", "friendlyName": "MODULE", "description": "Allows MODULE actions in ID_CARDS module", "dependsOn": []}, {"name": "MANAGE_ID_CARD_TEMPLATE", "friendlyName": "MANAGE_ID_CARD_TEMPLATE", "description": "Allows MANAGE_ID_CARD_TEMPLATE actions in ID_CARDS module", "dependsOn": []}, {"name": "MANAGE_ID_CARD_ORDERS", "friendlyName": "MANAGE_ID_CARD_ORDERS", "description": "Allows MANAGE_ID_CARD_ORDERS actions in ID_CARDS module", "dependsOn": []}, {"name": "APPROVE_ID_CARDS", "friendlyName": "APPROVE_ID_CARDS", "description": "Allows APPROVE_ID_CARDS actions in ID_CARDS module", "dependsOn": []}]}]