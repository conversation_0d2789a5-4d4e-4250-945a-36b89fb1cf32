<?php
/**
 * Mobile Calendar Page for Parent Dashboard
 * Displays calendar events with month/week view toggle
 */

$current_date = date('Y-m-d');
$current_month_start = date('Y-m-01');

// Get module name for header
$moduleName = 'Calendar';
?>

<!-- Inner Page Header -->
<div class="inner-page-header">
    <div class="header-content">
        <!-- Back Button -->
        <button class="back-btn" onclick="goBackToDashboard()" aria-label="Go back to dashboard">
            <i class="fa fa-chevron-left"></i>
        </button>
        <!-- Module Title -->
        <h1 class="module-title">Calendar</h1>
    </div>
</div>

<!-- Page Content -->
<div class="page-content">
    <div class="calendar-mobile-container">
    <!-- Calendar Widget Container -->
    <div class="calendar-widget">
        <!-- Month Navigation Header -->
        <div class="calendar-header">
            <button type="button" class="nav-btn" id="prevMonth">
                <i class="fa fa-chevron-left"></i>
            </button>
            <div class="month-display" id="currentMonth"><?php echo date('j M Y'); ?></div>
            <button type="button" class="nav-btn" id="nextMonth">
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <!-- Calendar View Toggle -->
        <div class="view-toggle">
            <button type="button" class="toggle-btn active" id="monthView" data-view="month">Month</button>
            <button type="button" class="toggle-btn" id="weekView" data-view="week">Week</button>
        </div>

        <!-- Calendar Container -->
        <div class="calendar-container" id="calendarContainer">
            <!-- Calendar will be rendered here -->
        </div>
    </div>

    <!-- Events List Section (for Week View) -->
    <div class="events-section" id="eventsSection" style="display: none;">
        <div class="events-list" id="eventsList">
            <!-- Events will be loaded here -->
        </div>
    </div>

    <!-- No Events Message -->
    <div class="no-events-container" id="noEventsCard" style="display: none;">
        <div class="no-events-illustration">
            <div class="illustration-wrapper">
                <div class="calendar-icon">
                    <i class="fa fa-calendar-o"></i>
                </div>
                <div class="person-silhouette">
                    <i class="fa fa-user"></i>
                </div>
            </div>
        </div>
        <h6 class="no-events-title">No Events</h6>
        <p class="no-events-text">You have no upcoming events.<br>Let's change the date!</p>
    </div>

    </div> <!-- End calendar-mobile-container -->
</div> <!-- End page-content -->

<!-- Bottom Sheet for Date Events -->
<div class="bottom-sheet-overlay" id="bottomSheetOverlay" style="display: none;">
    <div class="bottom-sheet" id="bottomSheet">
        <!-- Bottom Sheet Handle -->
        <div class="bottom-sheet-handle"></div>

        <!-- Date Navigation Header -->
        <div class="bottom-sheet-header">
            <button type="button" class="date-nav-btn" id="prevDate">
                <i class="fa fa-chevron-left"></i>
            </button>
            <div class="selected-date-display" id="selectedDateDisplay">3 Sep 2025</div>
            <button type="button" class="date-nav-btn" id="nextDate">
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <!-- Events List -->
        <div class="bottom-sheet-content">
            <div class="bottom-sheet-events" id="bottomSheetEvents">
                <!-- Events will be loaded here -->
            </div>
        </div>
    </div>
</div>

    </div> <!-- End calendar-mobile-container -->
</div> <!-- End page-content -->

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>

<style>
/* Header Styling - Use existing inner-page-header from text page */
.inner-page-header {
    background: #F9F7FE;
    color: #161327;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    margin-bottom: 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    max-width: 1200px;
    margin: 0 auto;
    gap: 7rem;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #161327;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(-2px);
}

.module-title {
    color: #161327;
    text-align: center;
    font-family: Inter;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    margin: 0;
}

/* Page Content */
.page-content {
    background: #f8f9fa;
    min-height: calc(100vh - 80px);
    padding: 1rem;
}

/* Calendar Widget */
.calendar-widget {
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 1rem;
}

/* Calendar Header */
.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border-bottom: 1px solid #f1f5f9;
}

.nav-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.month-display {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    text-align: center;
    flex: 1;
}

/* View Toggle */
.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin: 0 1rem 1rem 1rem;
}

.toggle-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn.active {
    background: #7b5cff;
    color: white;
    box-shadow: 0 2px 4px rgba(123, 92, 255, 0.3);
}

/* Calendar Container */
.calendar-container {
    background: white;
    padding: 0;
}

/* Calendar Grid */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

/* Calendar Header Days */
.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.calendar-weekday {
    padding: 12px 4px;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
}

/* Calendar Days */
.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
    min-height: 80px;
    padding: 6px 4px;
    border-right: 1px solid #f1f5f9;
    border-bottom: 1px solid #f1f5f9;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    background: white;
}

.calendar-day:nth-child(7n) {
    border-right: none;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day.today .calendar-day-number {
    background-color: #7b5cff;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-day.selected {
    background-color: #f0f8ff;
}

.calendar-day.other-month {
    color: #ccc;
    background-color: #fafafa;
}

.calendar-day-number {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1;
    align-self: center;
}

/* Event Tags */
.event-tags {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 100%;
    margin-top: 2px;
}

.event-tag {
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.event-tag.holiday {
    background-color: #ff4444;
}

.event-tag.information {
    background-color: #2196f3;
}

.event-tag.money {
    background-color: #4caf50;
}

.event-tag.wednesday {
    background-color: #ff9800;
}

/* Event Dots (fallback for too many events) */
.event-dots {
    display: flex;
    gap: 2px;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 100%;
    margin-top: 2px;
}

.event-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #7b5cff;
}

.event-dot.holiday {
    background-color: #ff4444;
}

.event-dot.information {
    background-color: #2196f3;
}

.event-dot.money {
    background-color: #4caf50;
}

.event-dot.wednesday {
    background-color: #ff9800;
}

/* Week View */
.week-view-container {
    background: white;
    padding: 1rem;
}

.week-days-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}

.week-day-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    position: relative;
    min-height: 70px;
    justify-content: center;
}

.week-day-item:hover {
    background-color: #f8f9fa;
}

.week-day-item.today {
    background-color: #7b5cff;
    color: white;
    box-shadow: 0 4px 12px rgba(123, 92, 255, 0.3);
}

.week-day-item.selected {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 2px solid #2196f3;
}

.week-day-letter {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 6px;
    text-transform: uppercase;
    line-height: 1;
}

.week-day-number {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 4px;
}

.week-day-item.today .week-day-letter,
.week-day-item.today .week-day-number {
    color: white;
}

.week-day-item.selected .week-day-letter,
.week-day-item.selected .week-day-number {
    color: #1976d2;
}

/* Week Event Dots */
.week-event-dots {
    display: flex;
    gap: 2px;
    justify-content: center;
    margin-top: 4px;
}

.week-event-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #7b5cff;
}

.week-event-dot.holiday {
    background-color: #ff4444;
}

.week-event-dot.information {
    background-color: #2196f3;
}

.week-event-dot.money {
    background-color: #4caf50;
}

.week-event-dot.wednesday {
    background-color: #ff9800;
}

.week-day-item.today .week-event-dot {
    background-color: rgba(255, 255, 255, 0.8);
}

/* Week View */
.week-view {
    display: none;
}

.week-view.active {
    display: block;
}

.week-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.week-day {
    padding: 16px 8px;
    text-align: center;
    border-right: 1px solid #f1f5f9;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.week-day:last-child {
    border-right: none;
}

.week-day:hover {
    background-color: #e9ecef;
}

.week-day.today {
    background-color: #7b5cff;
    color: white;
}

.week-day-name {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 4px;
}

.week-day-number {
    font-size: 18px;
    font-weight: 600;
}

.week-day.today .week-day-name {
    color: rgba(255, 255, 255, 0.8);
}

/* Events Section */
.events-section {
    background: #f8f9fa;
    padding: 1rem;
    min-height: 200px;
}

.events-list {
    max-width: 100%;
}

.event-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #7b5cff;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

.event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.event-card.holiday {
    border-left-color: #ff4444;
    background: linear-gradient(135deg, #fff 0%, #fff8f8 100%);
}

.event-card.information {
    border-left-color: #2196f3;
    background: linear-gradient(135deg, #fff 0%, #f8fbff 100%);
}

.event-card.money {
    border-left-color: #4caf50;
    background: linear-gradient(135deg, #fff 0%, #f8fff8 100%);
}

.event-card.wednesday {
    border-left-color: #ff9800;
    background: linear-gradient(135deg, #fff 0%, #fffaf8 100%);
}

.event-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
}

.event-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 2px;
}

.event-icon.holiday {
    background: #ff4444;
}

.event-icon.information {
    background: #2196f3;
}

.event-icon.money {
    background: #4caf50;
}

.event-icon.wednesday {
    background: #ff9800;
}

.event-content {
    flex: 1;
}

.event-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.event-date {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    font-weight: 500;
}

.event-time {
    font-size: 12px;
    color: #94a3b8;
    margin-top: 2px;
}

/* No Events State */
.no-events-container {
    text-align: center;
    padding: 3rem 1rem;
    background: #f8f9fa;
}

.illustration-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.calendar-icon {
    font-size: 4rem;
    color: #dee2e6;
}

.person-silhouette {
    position: absolute;
    bottom: -10px;
    right: -10px;
    font-size: 2rem;
    color: #adb5bd;
}

.no-events-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.no-events-text {
    font-size: 1rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

/* Bottom Sheet */
.bottom-sheet-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bottom-sheet-overlay.show {
    opacity: 1;
}

.bottom-sheet {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 20px 20px 0 0;
    max-height: 70vh;
    min-height: 300px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.bottom-sheet-overlay.show .bottom-sheet {
    transform: translateY(0);
}

.bottom-sheet-handle {
    width: 40px;
    height: 4px;
    background: #dee2e6;
    border-radius: 2px;
    margin: 12px auto 8px;
}

.bottom-sheet-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;
}

.date-nav-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.date-nav-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.selected-date-display {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    text-align: center;
    flex: 1;
}

.bottom-sheet-content {
    padding: 0 20px 20px;
    max-height: calc(70vh - 120px);
    overflow-y: auto;
}

.bottom-sheet-events {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Bottom Sheet Event Cards */
.bottom-sheet-event-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    border-left: 4px solid #7b5cff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bottom-sheet-event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.bottom-sheet-event-card.holiday {
    border-left-color: #ff4444;
}

.bottom-sheet-event-card.information {
    border-left-color: #2196f3;
}

.bottom-sheet-event-card.money {
    border-left-color: #4caf50;
}

.bottom-sheet-event-card.wednesday {
    border-left-color: #ff9800;
}

.bottom-sheet-event-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
}

.bottom-sheet-event-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
    margin-top: 2px;
}

.bottom-sheet-event-icon.holiday {
    background: #ff4444;
}

.bottom-sheet-event-icon.information {
    background: #2196f3;
}

.bottom-sheet-event-icon.money {
    background: #4caf50;
}

.bottom-sheet-event-icon.wednesday {
    background: #ff9800;
}

.bottom-sheet-event-content {
    flex: 1;
}

.bottom-sheet-event-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.bottom-sheet-event-type {
    font-size: 12px;
    color: #64748b;
    margin: 0 0 4px 0;
    font-weight: 500;
    text-transform: uppercase;
}

.bottom-sheet-event-description {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.4;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay .spinner-border {
    color: #7b5cff;
}

/* Responsive Design */
@media (max-width: 576px) {
    .inner-page-header {
        padding: 0.75rem 1rem;
    }

    .module-title {
        font-size: 18px;
    }

    .back-btn {
        width: 36px;
        height: 36px;
    }

    .calendar-header,
    .view-toggle {
        padding: 0.75rem;
    }

    .month-display {
        font-size: 16px;
    }

    .nav-btn {
        width: 32px;
        height: 32px;
    }

    .calendar-day {
        min-height: 45px;
        padding: 6px 2px;
    }

    .calendar-day-number {
        font-size: 12px;
    }

    .event-dot {
        width: 4px;
        height: 4px;
    }

    .event-card {
        padding: 12px;
        border-radius: 8px;
    }

    .event-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .event-title {
        font-size: 14px;
    }

    .event-date {
        font-size: 12px;
    }

    /* Week View Mobile */
    .week-view-container {
        padding: 0.75rem;
    }

    .week-days-row {
        gap: 4px;
    }

    .week-day-item {
        padding: 8px 4px;
        min-height: 60px;
        border-radius: 8px;
    }

    .week-day-letter {
        font-size: 10px;
        margin-bottom: 4px;
    }

    .week-day-number {
        font-size: 16px;
    }

    .week-event-dot {
        width: 3px;
        height: 3px;
    }

    /* Bottom Sheet Mobile */
    .bottom-sheet {
        max-height: 80vh;
        min-height: 250px;
    }

    .bottom-sheet-header {
        padding: 12px 16px;
    }

    .selected-date-display {
        font-size: 16px;
    }

    .date-nav-btn {
        width: 32px;
        height: 32px;
    }

    .bottom-sheet-content {
        padding: 0 16px 16px;
    }

    .bottom-sheet-event-card {
        padding: 12px;
    }

    .bottom-sheet-event-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .bottom-sheet-event-title {
        font-size: 14px;
    }

    .bottom-sheet-event-type {
        font-size: 10px;
    }

    .bottom-sheet-event-description {
        font-size: 12px;
    }
}
</style>

<script>
$(document).ready(function() {
    // Calendar configuration
    let currentDate = new Date();
    let currentView = 'month';
    let selectedDate = null;
    let calendarEvents = [];
    let studentBoard = '<?php echo isset($student_board) ? $student_board : ""; ?>';
    let studentId = '<?php echo $studentId; ?>';

    // Initialize calendar
    initializeCalendar();
    loadCalendarEvents();

    // Navigation handlers
    $('#prevMonth').click(function() {
        if (currentView === 'month') {
            currentDate.setMonth(currentDate.getMonth() - 1);
        } else {
            currentDate.setDate(currentDate.getDate() - 7);
        }
        updateCalendarDisplay();
        loadCalendarEvents();
    });

    $('#nextMonth').click(function() {
        if (currentView === 'month') {
            currentDate.setMonth(currentDate.getMonth() + 1);
        } else {
            currentDate.setDate(currentDate.getDate() + 7);
        }
        updateCalendarDisplay();
        loadCalendarEvents();
    });

    // View toggle handlers
    $('.toggle-btn').click(function() {
        const view = $(this).data('view');
        if (view !== currentView) {
            $('.toggle-btn').removeClass('active');
            $(this).addClass('active');
            currentView = view;

            // Hide bottom sheet when switching views
            hideBottomSheet();

            // Clear selected date when switching views
            selectedDate = null;

            updateCalendarDisplay();
        }
    });

    // Initialize calendar display
    function initializeCalendar() {
        updateCalendarDisplay();
    }

    // Update calendar display based on current view
    function updateCalendarDisplay() {
        updateMonthDisplay();

        if (currentView === 'month') {
            // Hide events section for month view (use bottom sheet instead)
            $('#eventsSection').hide();
            $('#noEventsCard').hide();
            renderMonthView();
        } else {
            // Show events section for week view
            renderWeekView();
        }
    }

    // Update month display in header
    function updateMonthDisplay() {
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];

        const displayText = currentDate.getDate() + ' ' +
                           monthNames[currentDate.getMonth()] + ' ' +
                           currentDate.getFullYear();
        $('#currentMonth').text(displayText);
    }
    
    // Render month view
    function renderMonthView() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();

        // Get first day of month and calculate start date
        const firstDay = new Date(year, month, 1);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        // Get today for highlighting
        const today = new Date();
        const todayStr = formatDateString(today);

        let html = '';

        // Add weekdays header
        html += '<div class="calendar-weekdays">';
        const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        weekdays.forEach(day => {
            html += `<div class="calendar-weekday">${day}</div>`;
        });
        html += '</div>';

        // Add calendar days
        html += '<div class="calendar-days">';

        const currentDateCopy = new Date(startDate);
        for (let i = 0; i < 42; i++) {
            const dateStr = formatDateString(currentDateCopy);
            const dayNumber = currentDateCopy.getDate();
            const isCurrentMonth = currentDateCopy.getMonth() === month;
            const isToday = dateStr === todayStr;
            const dayEvents = getEventsForDate(dateStr);

            let classes = 'calendar-day';
            if (!isCurrentMonth) classes += ' other-month';
            if (isToday) classes += ' today';
            if (dayEvents.length > 0) classes += ' has-events';
            if (selectedDate && dateStr === selectedDate) classes += ' selected';

            html += `<div class="${classes}" data-date="${dateStr}">`;
            html += `<div class="calendar-day-number">${dayNumber}</div>`;

            // Add event tags (like in the design)
            if (dayEvents.length > 0 && isCurrentMonth) {
                if (dayEvents.length <= 2) {
                    html += '<div class="event-tags">';
                    dayEvents.forEach(event => {
                        const eventClass = getEventClass(event.event_type);
                        const eventName = event.names[0] || event.event_type;
                        const truncatedName = eventName.length > 8 ? eventName.substring(0, 8) + '...' : eventName;
                        html += `<div class="event-tag ${eventClass}">${truncatedName}</div>`;
                    });
                    html += '</div>';
                } else {
                    // Show dots if too many events
                    html += '<div class="event-dots">';
                    dayEvents.slice(0, 3).forEach(event => {
                        const eventClass = getEventClass(event.event_type);
                        html += `<div class="event-dot ${eventClass}"></div>`;
                    });
                    if (dayEvents.length > 3) {
                        html += '<div class="event-dot">+</div>';
                    }
                    html += '</div>';
                }
            }

            html += '</div>';
            currentDateCopy.setDate(currentDateCopy.getDate() + 1);
        }

        html += '</div>';
        $('#calendarContainer').html(html);

        // Add click handlers
        $('.calendar-day').click(function() {
            const dateStr = $(this).data('date');
            if (!$(this).hasClass('other-month')) {
                selectDateWithBottomSheet(dateStr);
            }
        });
    }

    // Render week view
    function renderWeekView() {
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

        const today = new Date();
        const todayStr = formatDateString(today);

        let html = '<div class="week-view-container">';

        // Week days row
        html += '<div class="week-days-row">';

        const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
        const currentDateCopy = new Date(startOfWeek);

        for (let i = 0; i < 7; i++) {
            const dateStr = formatDateString(currentDateCopy);
            const isToday = dateStr === todayStr;
            const dayEvents = getEventsForDate(dateStr);

            let classes = 'week-day-item';
            if (isToday) classes += ' today';
            if (dayEvents.length > 0) classes += ' has-events';
            if (selectedDate && dateStr === selectedDate) classes += ' selected';

            html += `<div class="${classes}" data-date="${dateStr}">`;
            html += `<div class="week-day-letter">${weekdays[i]}</div>`;
            html += `<div class="week-day-number">${currentDateCopy.getDate()}</div>`;

            // Add event dots if there are events
            if (dayEvents.length > 0) {
                html += '<div class="week-event-dots">';
                dayEvents.slice(0, 3).forEach(event => {
                    const eventClass = getEventClass(event.event_type);
                    html += `<div class="week-event-dot ${eventClass}"></div>`;
                });
                html += '</div>';
            }

            html += '</div>';
            currentDateCopy.setDate(currentDateCopy.getDate() + 1);
        }

        html += '</div>';
        html += '</div>';

        $('#calendarContainer').html(html);

        // Show events section for week view
        $('#eventsSection').show();
        $('#noEventsCard').hide();

        // Auto-select today or first day with events
        let autoSelectDate = todayStr;
        const todayEvents = getEventsForDate(todayStr);

        if (todayEvents.length === 0) {
            // Find first day with events in the week
            const weekStartCopy = new Date(startOfWeek);
            for (let i = 0; i < 7; i++) {
                const checkDateStr = formatDateString(weekStartCopy);
                const checkEvents = getEventsForDate(checkDateStr);
                if (checkEvents.length > 0) {
                    autoSelectDate = checkDateStr;
                    break;
                }
                weekStartCopy.setDate(weekStartCopy.getDate() + 1);
            }
        }

        // Add click handlers
        $('.week-day-item').click(function() {
            const dateStr = $(this).data('date');
            selectDate(dateStr);
        });

        // Auto-select date
        selectDate(autoSelectDate);
    }

    // Select a date and show events (for week view)
    function selectDate(dateStr) {
        selectedDate = dateStr;
        $('.week-day-item').removeClass('selected');
        $(`.week-day-item[data-date="${dateStr}"]`).addClass('selected');

        const dayEvents = getEventsForDate(dateStr);
        if (dayEvents.length > 0) {
            displayEvents(dayEvents, dateStr);
        } else {
            showNoEvents();
        }
    }

    // Select a date and show bottom sheet (for month view)
    function selectDateWithBottomSheet(dateStr) {
        selectedDate = dateStr;
        $('.calendar-day').removeClass('selected');
        $(`.calendar-day[data-date="${dateStr}"]`).addClass('selected');

        const dayEvents = getEventsForDate(dateStr);
        showBottomSheet(dateStr, dayEvents);
    }

    // Show bottom sheet with events
    function showBottomSheet(dateStr, events) {
        const date = new Date(dateStr);
        const formattedDate = date.getDate() + ' ' +
                             date.toLocaleDateString('en-US', { month: 'short' }) + ' ' +
                             date.getFullYear();

        $('#selectedDateDisplay').text(formattedDate);

        if (events.length > 0) {
            displayBottomSheetEvents(events);
        } else {
            $('#bottomSheetEvents').html('<div class="no-events-message">No events for this date</div>');
        }

        $('#bottomSheetOverlay').show().addClass('show');

        // Add navigation handlers
        $('#prevDate').off('click').on('click', function() {
            navigateBottomSheetDate(-1);
        });

        $('#nextDate').off('click').on('click', function() {
            navigateBottomSheetDate(1);
        });
    }

    // Navigate date in bottom sheet
    function navigateBottomSheetDate(direction) {
        const currentSelectedDate = new Date(selectedDate);
        currentSelectedDate.setDate(currentSelectedDate.getDate() + direction);
        const newDateStr = formatDateString(currentSelectedDate);

        selectedDate = newDateStr;
        $('.calendar-day').removeClass('selected');
        $(`.calendar-day[data-date="${newDateStr}"]`).addClass('selected');

        const dayEvents = getEventsForDate(newDateStr);
        showBottomSheet(newDateStr, dayEvents);
    }

    // Display events in bottom sheet
    function displayBottomSheetEvents(events) {
        let html = '';

        events.forEach(event => {
            const eventClass = getEventClass(event.event_type);
            const iconClass = getEventIcon(event.event_type);

            html += `<div class="bottom-sheet-event-card ${eventClass}">`;
            html += `<div class="bottom-sheet-event-header">`;
            html += `<div class="bottom-sheet-event-icon ${eventClass}">`;
            html += `<i class="fa fa-${iconClass}"></i>`;
            html += `</div>`;
            html += `<div class="bottom-sheet-event-content">`;
            html += `<div class="bottom-sheet-event-type">${event.event_type}</div>`;
            html += `<div class="bottom-sheet-event-title">${event.names.join(', ')}</div>`;
            if (event.description) {
                html += `<div class="bottom-sheet-event-description">${event.description}</div>`;
            }
            html += `</div>`;
            html += `</div>`;
            html += `</div>`;
        });

        $('#bottomSheetEvents').html(html);
    }

    // Hide bottom sheet
    function hideBottomSheet() {
        $('#bottomSheetOverlay').removeClass('show');
        setTimeout(() => {
            $('#bottomSheetOverlay').hide();
        }, 300);
    }

    // Close bottom sheet when clicking overlay
    $('#bottomSheetOverlay').click(function(e) {
        if (e.target === this) {
            hideBottomSheet();
        }
    });

    // Get events for a specific date
    function getEventsForDate(dateStr) {
        const targetDate = new Date(dateStr);
        const dayNumber = String(targetDate.getDate()).padStart(2, '0');

        return calendarEvents.filter(event => {
            const eventDate = event.event_on.split(' ')[0];
            return eventDate === dayNumber;
        });
    }

    // Get CSS class for event type
    function getEventClass(eventType) {
        const classMap = {
            'Holiday': 'holiday',
            'Information': 'information',
            'Money': 'money',
            'Wednesday': 'wednesday'
        };
        return classMap[eventType] || 'information';
    }

    // Format date as YYYY-MM-DD string
    function formatDateString(date) {
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    }
    
    // Load calendar events from server
    function loadCalendarEvents() {
        showLoading();

        const dateStr = currentDate.getFullYear() + '-' +
                       String(currentDate.getMonth() + 1).padStart(2, '0');

        $.ajax({
            url: '<?php echo site_url('parent/parent_dashboard_controller/get_calendar_events'); ?>',
            type: 'POST',
            data: {
                date: dateStr,
                state: 'current',
                student_board: studentBoard,
                student_id: studentId
            },
            success: function(response) {
                hideLoading();
                try {
                    const data = JSON.parse(response);
                    if (data.status === 'success') {
                        calendarEvents = data.events || [];
                        updateCalendarDisplay();
                    } else {
                        showError('Failed to load calendar events');
                    }
                } catch (e) {
                    showError('Error parsing calendar data');
                }
            },
            error: function() {
                hideLoading();
                showError('Failed to load calendar events');
            }
        });
    }

    // Display events for selected date
    function displayEvents(events, dateStr) {
        let html = '';

        events.forEach(event => {
            const eventClass = getEventClass(event.event_type);
            const iconClass = getEventIcon(event.event_type);

            html += `<div class="event-card ${eventClass}">`;
            html += `<div class="event-header">`;
            html += `<div class="event-icon ${eventClass}">`;
            html += `<i class="fa fa-${iconClass}"></i>`;
            html += `</div>`;
            html += `<div class="event-content">`;
            html += `<div class="event-title">${event.names.join(', ')}</div>`;
            html += `<div class="event-date">${formatEventDate(event.event_on)}</div>`;
            if (event.event_time) {
                html += `<div class="event-time">${event.event_time}</div>`;
            }
            html += `</div>`;
            html += `</div>`;
            html += `</div>`;
        });

        $('#eventsList').html(html);
        $('#eventsSection').show();
        $('#noEventsCard').hide();
    }

    // Show no events message
    function showNoEvents() {
        $('#eventsSection').hide();
        $('#noEventsCard').show();
    }

    // Get icon for event type
    function getEventIcon(eventType) {
        const iconMap = {
            'Holiday': 'calendar',
            'Information': 'info-circle',
            'Money': 'money-bill',
            'Wednesday': 'calendar-day'
        };
        return iconMap[eventType] || 'calendar';
    }

    // Format event date for display
    function formatEventDate(eventDateStr) {
        const date = new Date(eventDateStr);
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Loading and error handling
    function showLoading() {
        if ($('#loadingOverlay').length === 0) {
            $('body').append('<div id="loadingOverlay" class="loading-overlay"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div></div>');
        }
        $('#loadingOverlay').show();
    }

    function hideLoading() {
        $('#loadingOverlay').hide();
    }

    function showError(message) {
        console.error(message);
        // You can implement a toast notification here
        alert(message);
    }
    
});

/**
 * Navigate back to dashboard
 */
function goBackToDashboard() {
    // Add loading state to back button
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.style.opacity = '0.7';
        backBtn.style.pointerEvents = 'none';
    }

    // Navigate back to dashboard
    window.location.href = '<?php echo site_url('parentdashboard'); ?>';
}

/**
 * Navigate back to dashboard
 */
function goBackToDashboard() {
    // Add loading state to back button
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.style.opacity = '0.7';
        backBtn.style.pointerEvents = 'none';
    }

    // Navigate back to dashboard
    window.location.href = '<?php echo site_url('parentdashboard'); ?>';
}
</script>
