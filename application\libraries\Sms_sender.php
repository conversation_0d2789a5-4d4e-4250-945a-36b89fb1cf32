<?php
/**
 * Name:    SMS Configuration
 * Author:  <PERSON>
 * Company: NextElement
 */
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * SMS Settings
 */
class sms_sender {

  protected $CI;

  public function __construct() {
    $this->CI =& get_instance();
  }

  public function send_sms($mobArry='',$content='') {
    $smsint = $this->CI->settings->getSetting('smsintergration');
    foreach ($mobArry as $key => $mob) {
      $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$mob.'&sender='.$smsint->sender.'&message='.$content;
      $check_returned = $this->CI->curl->simple_get($get_url);
    }
    return $check_returned;
  }

  public function loopSend_sms($mob='',$content='') {
    $smsint = $this->CI->settings->getSetting('smsintergration');
    $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$mob.'&sender='.$smsint->sender.'&message='.$content;
    $check_returned = $this->CI->curl->simple_get($get_url);
    return $check_returned;
  }

  public function buildData($result, $msg, $sender, $isUnicode){
    $contact = array();
    foreach ($result as $key => $val){
      if(!empty($val['mobile_no']))
        $contact[] = array("to" => $val['mobile_no'], "custom" => $val['id']);
    }

    $data = array(
        "sms" => $contact,
        "message" => $msg,
        "sender" => $sender
    );
    if($isUnicode == 1) {
      $data["unicode"] = 1;
    }
    return json_encode($data);
  }

  public function buildUrlData($result, $sender, $isUnicode){
    $contact = array();
    foreach ($result as $key => $val){
      $iv = substr(hash('sha256', 'cbc790d23ef09d14'), 0, 16);
      $res = openssl_encrypt($val->stdId, 'aes-256-cbc', '846546546', 0, $iv);
      $encId = base64_encode($res);
      $message = "Dear Madam/Sir, Please click on the following link - ".site_url('student_profile/').$encId ." , to update student data.";
      $contact[] = array("to" => $val->mobile_no, "custom" => $val->id, "message" => $message);
    }

    $data = array(
        "sms" => $contact,
        "sender" => $sender
    );
    if($isUnicode == 1) {
      $data["unicode"] = 1;
    }
    return json_encode($data);
  }

  public function buildCustomData($result, $sender, $isUnicode){
    $contact = array();
    foreach ($result as $key => $val){
      if(!empty($val['mobile_no']))
        $contact[] = array("to" => $val['mobile_no'], "message" => $val['message'], "custom" => $val['id']);
    }

    $data = array(
        "sms" => $contact,
        "sender" => $sender
    );
    if($isUnicode == 1) {
      $data["unicode"] = 1;
    }
    return json_encode($data);
  }

  public function sendMsg($result, $message, $isUnicode){
    $smsint = $this->CI->settings->getSetting('smsintergration');
    $jsonData = $this->buildData($result, $message, $smsint->sender, $isUnicode);
    // $jsonData = str_replace("+", "%20", $jsonData);
    $get_url = 'https://'.$smsint->url.'?api_key='.$smsint->api_key.'&method=sms.json&';
    $check_returned = $this->CI->curl->simple_post($get_url,$jsonData);
    return json_decode($check_returned);
  }

  public function sendCustomMsg($result, $isUnicode){
    $smsint = $this->CI->settings->getSetting('smsintergration');
    $jsonData = $this->buildCustomData($result, $smsint->sender, $isUnicode);
    $get_url = 'http://'.$smsint->url.'?api_key='.$smsint->api_key.'&method=sms.json&';
    $check_returned = $this->CI->curl->simple_post($get_url,$jsonData);
    return json_decode($check_returned);
  }

  public function sendSMS($result, $msg, $isProfileUpdate, $isUnicode){
    $smsint = $this->CI->settings->getSetting('smsintergration');
    if($isProfileUpdate) {
      $jsonData = $this->buildUrlData($result,$smsint->sender, $isUnicode);
    }
    else {    
      $jsonData = $this->buildData($result,$msg,$smsint->sender, $isUnicode);      
    }
    $get_url = 'http://'.$smsint->url.'?api_key='.$smsint->api_key.'&method=sms.json&';
    $check_returned = $this->CI->curl->simple_post($get_url,$jsonData);
    return json_decode($check_returned);
  }

  public function sendTestSMS(){
    $smsint = $this->CI->settings->getSetting('smsintergration');
    $data = array(
			"sms" => array("to" => $_POST['to'], "custom" => $_POST['to'], "message" => $_POST['msg']),
			"sender" => $smsint->sender
		);
    $jsonData = json_encode($data);
    $get_url = 'http://'.$smsint->url.'?api_key='.$smsint->api_key.'&method=sms.json&';
    $check_returned = $this->CI->curl->simple_post($get_url,$jsonData);
    return json_decode($check_returned);
  }

  public function buildTextData($result, $msg, $sender, $isUnicode){
    $contact = array();
    foreach ($result as $key => $val){
      if(!empty($val['mobile_no']))
        $contact[] = array("to" => $val['mobile_no'], "custom" => $val['text_sent_to_id']);
    }

    $data = array(
        "sms" => $contact,
        "message" => $msg,
        "sender" => $sender
    );
    if($isUnicode == 1) {
      $data["unicode"] = 1;
    }
    return json_encode($data);
  }

  public function buildCustomTextData($result, $sender, $isUnicode){
    $contact = array();
    foreach ($result as $key => $val){
      if(!empty($val['mobile_no']))
        $contact[] = array("to" => $val['mobile_no'], "message" => $val['message'], "custom" => $val['text_sent_to_id']);
    }

    $data = array(
        "sms" => $contact,
        "sender" => $sender
    );
    if($isUnicode == 1) {
      $data["unicode"] = 1;
    }
    return json_encode($data);
  }


  //version 2 - Texting 
  public function sendTextMsg($result, $message, $isUnicode){
    
    $smsint = $this->CI->settings->getSetting('smsintergration');
    $responses = [];
    
    foreach ($result as $val) {
      if (!empty($val['mobile_no'])) {
        $mobileNumber = $val['mobile_no'];
        $textSentToId = !empty($val['text_sent_to_id']) ? $val['text_sent_to_id'] : null;
        
        $content = urlencode($message);
        $get_url = 'https://'.$smsint->url.'?apikey='.$smsint->api_key.'&senderid='.$smsint->sender.'&number='.$mobileNumber.'&message='.$content;
        
        if($smsint->mode == 'LIVE') {
          $check_returned = $this->CI->curl->simple_get($get_url);
          $response = json_decode($check_returned, true);
          if (isset($response['data']) && is_array($response['data'])) {
            foreach ($response['data'] as $key => $item) {
              $response['data'][$key]['textsentToId'] = $textSentToId;
            }
          }
          $responses[] = $response;
          $responses['msgid'] = isset($response['msgid']) ? $response['msgid'] : null;
          $responses['message'] = $response['message'];
        }
      }
    }
    
    // Combine all responses
    $combined_response = [
      'status' => 'OK',
      'data' => [],
      'msgid' => isset($response['msgid']) ? $response['msgid'] : null,
      'message' => $responses['message']
    ];
    
    foreach ($responses as $response) {
      if (isset($response['data']) && is_array($response['data'])) {
        $combined_response['data'] = array_merge($combined_response['data'], $response['data']);
      }
    }
    return $combined_response;


    // 06-05-2025 Mohan 
    // $get_url modiefied new api key requirments. the following commented code is old api key requirements.
    // $jsonData = $this->buildTextData($result, $message, $smsint->sender, $isUnicode);
    // $get_url = 'https://'.$smsint->url.'?api_key='.$smsint->api_key.'&method=sms.json&';
    // if($smsint->mode == 'LIVE') {
    //   $check_returned = $this->CI->curl->simple_post($get_url,$jsonData);
    //   return json_decode($check_returned);
    // }
  }
  
  // 08-04-2025 Mohan 
  // $get_url modiefied new api key requirments. the following commented code is old api key requirements.
  // public function sendUniqueTextMsg($result, $isUnicode){
  //   $smsint = $this->CI->settings->getSetting('smsintergration');
  //   $jsonData = $this->buildCustomTextData($result, $smsint->sender, $isUnicode);
  //   $get_url = 'http://'.$smsint->url.'?api_key='.$smsint->api_key.'&method=sms.json&';
  //   if($smsint->mode == 'LIVE') {
  //     $check_returned = $this->CI->curl->simple_post($get_url,$jsonData);
  //     return json_decode($check_returned);
  //   }
  // }

  
  public function sendUniqueTextMsg($result, $isUnicode){
    // $json = '{"url":"promotional.mysmsbasket.com/V2/http-api.php", "api_key":"CMxAn4EKyxCsLhAe", "sender":"NXTSMS", "mode":"LIVE"}';
    // $json = '{"url":"promotional.mysmsbasket.com/V2/http-api.php", "api_key":"CMxAn4EKyxCsLhAe", "sender":"NXTSMS", "mode":"LIVE"}';
    $smsint = $this->CI->settings->getSetting('smsintergration');
    $postData = [
        "apikey" => trim($smsint->api_key),
        "senderid" => trim($smsint->sender),
        "format" => "json",
        "data" => []
    ];

    // Build the data array
    foreach ($result as $val) {
        if (!empty($val['mobile_no'])) {
            $postData['data'][] = [
                "number" => $val['mobile_no'],
                "message" => $val['message'],
                "textSentToId" => !empty($val['text_sent_to_id']) ? $val['text_sent_to_id'] : null
            ];
        }
    }
    $get_url = 'https://promotional.mysmsbasket.com/V2/http-customize-api-post.php';
    if ($smsint->mode == 'LIVE') {
      // Convert to JSON
      $jsonData = json_encode($postData);
      // Make the API request
      $check_returned = $this->CI->curl->simple_post($get_url, $jsonData);
      $response = json_decode($check_returned, true);
      $textSentToIds = array_column($postData['data'], 'textSentToId');
      // Process the response
      if (isset($response['data']) && is_array($response['data'])) {
        foreach ($response['data'] as $key => $item) {
          $response['data'][$key]['textSentToId'] = isset($textSentToIds[$key]) ? $textSentToIds[$key] : null;
        }
      }
      return $response;
    }
    return null;
  }

  public function send_otp($mobileNumber, $message){
    $smsint = $this->CI->settings->getSetting('smsintergration');
    if(empty($smsint)) return false;
    $content =  urlencode(''.$message.'');
    $get_url = 'https://'.$smsint->url.'?apikey='.$smsint->api_key.'&senderid='.$smsint->sender.'&number='.$mobileNumber.'&message='.$content;
    // 04-07-2025 Mohan 
    // $get_url modiefied new api key requirments. the following commented code is old api key requirements.
    // $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$mobileNumber.'&sender='.$smsint->sender.'&message='.$content;
    if($smsint->mode == 'LIVE') {
      $check_returned = $this->CI->curl->simple_get($get_url);
      
      return json_decode($check_returned);
    }
  }
}